# 使用官方的 Node.js 18 镜像作为基础镜像
FROM node:18

# 设置工作目录
WORKDIR /app

# 复制所有配置文件
COPY package*.json ./
COPY tsconfig.json ./
COPY tailwind.config.ts ./
COPY postcss.config.mjs ./
COPY next.config.mjs ./

# 安装所有依赖（包括 devDependencies，因为构建时需要）
RUN npm install

# 创建一个空的环境配置文件
RUN echo "# This file was created during build" > .env.production

# 复制其余源代码
COPY . .

# 检查Google凭据文件 - 这仅用于构建过程中的显示，实际文件将通过volume挂载
RUN echo "Google credentials file status:" && \
    if [ -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then \
    echo "Found at: $GOOGLE_APPLICATION_CREDENTIALS"; \
    else \
    echo "Not found at build time. Will be mounted at runtime."; \
    fi

# 运行 Prisma generate
RUN npx prisma generate

# 构建 Next.js 应用
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production
ENV NODE_OPTIONS='--no-deprecation'

# 接收构建参数
ARG NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL
ARG NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL
ARG NEXT_PUBLIC_GA_ID
ARG NEXT_PUBLIC_GTAG_ID
ARG NEXT_PUBLIC_GOOGLE_CLIENT_ID
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_SITE_URL
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_APP_DESCRIPTION
ARG NEXT_PUBLIC_PROJECT_IDENTIFIER

# 将参数设置为环境变量
ENV NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL=${NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL}
ENV NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL=${NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL}
ENV NEXT_PUBLIC_GA_ID=${NEXT_PUBLIC_GA_ID}
ENV NEXT_PUBLIC_GTAG_ID=${NEXT_PUBLIC_GTAG_ID}
ENV NEXT_PUBLIC_GOOGLE_CLIENT_ID=${NEXT_PUBLIC_GOOGLE_CLIENT_ID}
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_SITE_URL=${NEXT_PUBLIC_SITE_URL}
ENV NEXT_PUBLIC_APP_NAME=${NEXT_PUBLIC_APP_NAME}
ENV NEXT_PUBLIC_APP_DESCRIPTION=${NEXT_PUBLIC_APP_DESCRIPTION}
ENV NEXT_PUBLIC_PROJECT_IDENTIFIER=${NEXT_PUBLIC_PROJECT_IDENTIFIER}

# 输出环境变量调试信息
RUN echo "Building with environment variables:" && \
    echo "NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL: $NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL" && \
    echo "NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL: $NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL" && \
    echo "NEXT_PUBLIC_API_URL: $NEXT_PUBLIC_API_URL" && \
    echo "NEXT_PUBLIC_SITE_URL: $NEXT_PUBLIC_SITE_URL"

RUN npm run build

# 清理开发依赖（但保留构建产物）
RUN npm prune --production

# 暴露应用的端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]