import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { setReferralToStorage, getReferralFromStorage, isValidReferralCode } from '@/utils/referralUtils';

export interface ReferralStats {
    referralCode: string;
    totalClicks: number;
    totalCompletions: number;
    pendingClicks: number;
    isUnlocked: boolean;
    needsCompletions: number;
    recentClicks: number;
    recentCompletions: number;
}

export function useReferralTracking() {
    const searchParams = useSearchParams();
    const [isTracking, setIsTracking] = useState(false);

    useEffect(() => {
        const refParam = searchParams.get('ref');
        console.log('useReferralTracking 检测到 URL 参数:', { refParam, searchParams: searchParams.toString() });

        if (refParam) {
            console.log('推荐码验证:', refParam, '是否有效:', isValidReferralCode(refParam));

            if (isValidReferralCode(refParam)) {
                console.log('✅ 检测到有效推荐码:', refParam);

                // 保存推荐码到存储
                setReferralToStorage(refParam);

                // 验证存储是否成功
                const { referralCode: storedCode } = getReferralFromStorage();
                console.log('存储验证:', { stored: storedCode, expected: refParam });

                // 追踪点击
                trackReferralClick(refParam);
            } else {
                console.log('❌ 推荐码格式无效:', refParam);
            }
        } else {
            console.log('没有检测到推荐码参数');
        }
    }, [searchParams]);

    const trackReferralClick = async (referralCode: string) => {
        if (isTracking) {
            console.log('正在追踪中，跳过重复请求');
            return;
        }

        setIsTracking(true);
        console.log('开始追踪推荐点击:', referralCode);

        try {
            const response = await fetch('/api/referral/track-click', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ referralCode }),
            });

            console.log('追踪API响应状态:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('✅ 推荐点击追踪成功:', data);
            } else {
                const errorText = await response.text();
                console.error('❌ 推荐点击追踪失败:', response.status, errorText);
            }
        } catch (error) {
            console.error('❌ 推荐点击追踪错误:', error);
        } finally {
            setIsTracking(false);
            console.log('追踪状态重置');
        }
    };

    const getReferralForSubmission = () => {
        const { referralCode } = getReferralFromStorage();
        console.log('获取提交用推荐码:', referralCode);
        return referralCode;
    };

    return {
        getReferralForSubmission,
        isTracking
    };
}

export function useReferralStats(testId: string | null) {
    const [stats, setStats] = useState<ReferralStats | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchStats = async () => {
        if (!testId) return;

        console.log('获取推荐统计:', testId);
        setLoading(true);
        setError(null);

        try {
            const response = await fetch(`/api/referral/stats?testId=${testId}`);

            if (response.ok) {
                const data = await response.json();
                console.log('推荐统计获取成功:', data.stats);
                setStats(data.stats);
            } else {
                const errorData = await response.json();
                console.error('推荐统计获取失败:', errorData);
                setError(errorData.error || '获取统计失败');
            }
        } catch (error) {
            console.error('获取推荐统计错误:', error);
            setError('网络错误');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchStats();
    }, [testId]);

    return {
        stats,
        loading,
        error,
        refetch: fetchStats
    };
} 