import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Script from 'next/script';
import { ReactNode } from 'react';

const inter = Inter({ 
    subsets: ["latin"],
    display: 'swap',
    variable: '--font-inter',
    weight: ['300', '400', '500', '600', '700', '800'],
    fallback: ['system-ui', 'sans-serif'],
    preload: true,
    adjustFontFallback: true,
});

const rubik = Rubik({
    subsets: ["latin"],
    display: 'swap',
    variable: '--font-rubik',
    weight: ['400', '500', '600', '700'],
    fallback: ['system-ui', 'Arial', 'sans-serif'],
    preload: true,
});

const lora = Lora({
    subsets: ["latin"],
    display: 'swap',
    variable: '--font-lora',
    weight: ['400', '500', '600', '700'],
    style: ['normal', 'italic'],
    fallback: ['Georgia', 'serif'],
    preload: true,
});

const GoogleAnalytics = () => {
    const gaId = process.env.NEXT_PUBLIC_GA_ID;
    const gtagId = process.env.NEXT_PUBLIC_GTAG_ID;

    if (!gaId && !gtagId) return null;

    return (
        <>
            {gaId && (
                <>
                    <Script
                        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
                        strategy="afterInteractive"
                    />
                    <Script id="ga-script" strategy="afterInteractive">
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${gaId}', {
                                send_page_view: false
                            });
                            
                            if (typeof window !== 'undefined') {
                                window.addEventListener('routeChangeComplete', (url) => {
                                    gtag('event', 'page_view', {
                                        page_path: url
                                    });
                                });
                            }
                        `}
                    </Script>
                </>
            )}
            {gtagId && (
                <>
                    <Script
                        src={`https://www.googletagmanager.com/gtag/js?id=${gtagId}`}
                        strategy="afterInteractive"
                    />
                    <Script id="gtag-script" strategy="afterInteractive">
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${gtagId}');
                        `}
                    </Script>
                </>
            )}
        </>
    );
};

// 添加客户端脚本来注销 Service Worker
const UnregisterServiceWorker = () => {
    return (
        <Script id="unregister-sw" strategy="afterInteractive">
            {`
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.getRegistrations().then(function(registrations) {
                        for (let registration of registrations) {
                            registration.unregister();
                        }
                    });
                }
            `}
        </Script>
    );
};

export default function RootLayout({
    children,
}: {
    children: ReactNode;
}) {
    return (
        <html lang="en" className={`${inter.variable} ${rubik.variable} ${lora.variable} scroll-smooth`}>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
                <meta name="theme-color" content="#3b82f6" />
                <link rel="icon" href="/favicon.ico" />
                <link
                    rel="preconnect"
                    href="https://fonts.gstatic.com"
                    crossOrigin="anonymous"
                />
            </head>
            <body className={`${inter.className} min-h-screen flex flex-col`}>
                {children}
                <GoogleAnalytics />
                <UnregisterServiceWorker />
            </body>
        </html>
    );
}