import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import { isValidReferralCode, extractReferralInfo, updateReferralInfo } from '@/utils/referralUtils';

// API configuration
export const dynamic = 'force-dynamic';

// 创建一个新的 Prisma 客户端实例

/**
 * 追踪推荐链接点击
 */
export async function POST(request: NextRequest) {
    try {
        const { referralCode } = await request.json();

        // 验证推荐码格式
        if (!referralCode || !isValidReferralCode(referralCode)) {
            return NextResponse.json(
                { error: '无效的推荐码' },
                { status: 400 }
            );
        }

        console.log(`追踪推荐链接点击: ${referralCode}`);

        // 查找拥有该推荐码的测试结果
        const testResults = await prisma.testResult.findMany({
            where: {
                metadata: {
                    path: ['referral', 'referralCode'],
                    equals: referralCode
                }
            }
        });

        if (testResults.length === 0) {
            return NextResponse.json(
                { error: '推荐码不存在' },
                { status: 404 }
            );
        }

        // 更新点击统计（可能有多个测试结果使用同一个推荐码，更新第一个）
        const testResult = testResults[0];
        const currentMetadata = testResult.metadata as any;
        const referralInfo = extractReferralInfo(currentMetadata);

        // 增加点击次数和记录时间戳
        const updatedReferralInfo = {
            ...referralInfo,
            referralClicks: referralInfo.referralClicks + 1,
            referralClickTimestamps: [
                ...referralInfo.referralClickTimestamps,
                new Date().toISOString()
            ]
        };

        const updatedMetadata = updateReferralInfo(currentMetadata, updatedReferralInfo);

        // 更新数据库
        await prisma.testResult.update({
            where: { id: testResult.id },
            data: { metadata: updatedMetadata }
        });

        console.log(`推荐链接点击追踪成功: ${referralCode}, 总点击数: ${updatedReferralInfo.referralClicks}`);

        return NextResponse.json({
            success: true,
            clicks: updatedReferralInfo.referralClicks,
            completions: updatedReferralInfo.referralCompletions
        });

    } catch (error) {
        console.error('追踪推荐链接点击时出错:', error);
        return NextResponse.json(
            { error: '服务器内部错误' },
            { status: 500 }
        );
    }
} 