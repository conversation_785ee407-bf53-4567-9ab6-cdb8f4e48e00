import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import { extractReferralInfo } from '@/utils/referralUtils';

// API configuration
export const dynamic = 'force-dynamic';

// 创建一个新的 Prisma 客户端实例

/**
 * 获取推荐统计信息
 */
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const testId = searchParams.get('testId');

        if (!testId) {
            return NextResponse.json(
                { error: '缺少测试ID参数' },
                { status: 400 }
            );
        }

        console.log(`获取推荐统计: ${testId}`);

        // 查找测试结果
        const testResult = await prisma.testResult.findUnique({
            where: { id: testId }
        });

        if (!testResult) {
            return NextResponse.json(
                { error: '测试结果不存在' },
                { status: 404 }
            );
        }

        // 提取推荐信息
        const referralInfo = extractReferralInfo(testResult.metadata);

        // 计算统计数据
        const stats = {
            referralCode: referralInfo.referralCode,
            totalClicks: referralInfo.referralClicks,
            totalCompletions: referralInfo.referralCompletions,
            pendingClicks: referralInfo.referralClicks - referralInfo.referralCompletions,
            isUnlocked: !testResult.isReportLocked,
            needsCompletions: Math.max(0, 1 - referralInfo.referralCompletions), // 需要1个完成才能解锁
            recentClicks: referralInfo.referralClickTimestamps
                .filter(timestamp => {
                    const clickTime = new Date(timestamp);
                    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                    return clickTime > oneDayAgo;
                }).length,
            recentCompletions: referralInfo.referralCompletionTimestamps
                .filter(timestamp => {
                    const completionTime = new Date(timestamp);
                    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                    return completionTime > oneDayAgo;
                }).length
        };

        return NextResponse.json({
            success: true,
            stats
        });

    } catch (error) {
        console.error('获取推荐统计时出错:', error);
        return NextResponse.json(
            { error: '服务器内部错误' },
            { status: 500 }
        );
    }
} 