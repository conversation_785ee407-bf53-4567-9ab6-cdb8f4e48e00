import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import { TestBindingService } from '@/utils/testBindingService';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    // 如果用户未登录，返回未授权
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取用户邮箱下的未绑定测试
    const unboundTests = await TestBindingService.checkUnboundTests(
      session.user.email || ''
    );

    // 为每个测试添加显示友好的信息
    const enrichedTests = unboundTests.map(test => ({
      ...test,
      displayName: TestBindingService.getTestDisplayName(test.testType),
      scoreSummary: TestBindingService.getTestScoreSummary(test.testType, test.scores),
    }));

    return NextResponse.json({
      success: true,
      tests: enrichedTests,
      count: enrichedTests.length,
    });
  } catch (error) {
    console.error('Error fetching unbound tests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 