import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import { TestBindingService } from '@/utils/testBindingService';
import { z } from 'zod';

// 验证请求体
const bindTestsSchema = z.object({
  testIds: z.array(z.string()).min(1, 'At least one test ID is required'),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // 如果用户未登录，返回未授权
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const validation = bindTestsSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }

    const { testIds } = validation.data;

    // 执行绑定操作
    const result = await TestBindingService.bindTests(session.user.id, testIds);

    if (!result.success) {
      return NextResponse.json(
        { error: 'Failed to bind tests', details: result.errors },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      boundCount: result.boundCount,
      message: `Successfully bound ${result.boundCount} test${result.boundCount > 1 ? 's' : ''} to your account.`,
    });
  } catch (error) {
    console.error('Error binding tests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 