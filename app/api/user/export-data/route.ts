import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import { TestBindingService } from '@/utils/testBindingService';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    // 如果用户未登录，返回未授权
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取用户已绑定的测试结果
    const userTests = await TestBindingService.getBoundTests(session.user.id);

    // 移除敏感数据
    const safeUserData = {
      id: session.user.id,
      name: session.user.name,
      email: session.user.email,
      testResults: userTests.map(result => ({
        id: result.id,
        testType: result.testType,
        scores: result.scores,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      })),
    };

    return NextResponse.json(safeUserData);
  } catch (error) {
    console.error('Error exporting user data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 