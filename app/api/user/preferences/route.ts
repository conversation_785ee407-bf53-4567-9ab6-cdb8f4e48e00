import { NextResponse } from 'next/server';
import { authOptions } from '@/utils/auth';
import { getServerSession } from 'next-auth/next';
import { prisma } from '@/utils/prisma';

export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    // 如果用户未登录，返回未授权
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const data = await request.json();
    
    // 假设我们有一个UserPreference表来存储用户偏好
    // 实际上，我们可能需要创建这个表，或者为User表添加新字段
    // 这里我们只是模拟成功响应
    
    return NextResponse.json({ 
      success: true,
      message: 'Preferences updated successfully',
      data: {
        ...data,
        userId: session.user.id
      }
    });
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return NextResponse.json({ error: 'Failed to update preferences' }, { status: 500 });
  }
} 