import { NextRequest, NextResponse } from 'next/server';
import { authOptions } from '@/utils/auth';
import { getServerSession } from 'next-auth/next';
import { prisma } from '@/utils/prisma';
import { z } from 'zod';

// 验证请求数据的schema
const updatePreferencesSchema = z.object({
  emailNotifications: z.boolean(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // 检查用户是否已登录
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await request.json();
    
    // 验证请求数据
    const validation = updatePreferencesSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }
    
    const { emailNotifications } = validation.data;
    
    // 更新用户偏好设置
    await prisma.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        emailNotifications,
      },
    });
    
    return NextResponse.json({
      success: true,
      message: 'Preferences updated successfully',
    });
    
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 