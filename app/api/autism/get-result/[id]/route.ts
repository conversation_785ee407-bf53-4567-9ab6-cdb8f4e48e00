import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';

// API configuration
export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 获取 ID 参数和 token
    const { id } = await params;
    const token = request.nextUrl.searchParams.get('token');
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: '缺少必填参数: id' },
        { status: 400 }
      );
    }
    
    // 获取用户会话
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    
    // 查询测试结果
    const testResult = await prisma.testResult.findUnique({
      where: { id }
    });
    
    if (!testResult) {
      return NextResponse.json(
        { success: false, error: '未找到测试结果' },
        { status: 404 }
      );
    }
    
    // 验证结果是否为自闭症测试（raadsrTest 或 aq10Test）
    if (testResult.testType !== 'raadsrTest' && testResult.testType !== 'aq10Test') {
      return NextResponse.json(
        { success: false, error: '所请求的测试结果不是自闭症测试' },
        { status: 400 }
      );
    }
    
    // 访问控制逻辑
    const canAccess =
      // 情况1: 已登录且报告已绑定到当前用户
      (userId && testResult.userId === userId) ||
      // 情况2: 报告未绑定且提供了正确的访问令牌
      (!testResult.isUserBound && testResult.accessToken === token);
    
    if (!canAccess) {
      return NextResponse.json(
        { success: false, error: '未授权访问测试结果' },
        { status: 403 }
      );
    }
    
    // 为前端处理结果格式
    const formattedResult = {
      id: testResult.id,
      type: testResult.testType, // 直接使用数据库中的 testType
      email: testResult.email,
      answers: testResult.answers,
      score: testResult.scores,
      metadata: testResult.metadata || {},
      createdAt: testResult.createdAt
    };
    
    return NextResponse.json({
      success: true,
      testResult: formattedResult
    });
    
  } catch (error) {
    console.error('获取自闭症测试结果出错:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误', details: String(error) },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
} 