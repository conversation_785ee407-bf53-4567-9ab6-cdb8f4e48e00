import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';

// API configuration
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数中的 ID
    const id = request.nextUrl.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: '缺少必填参数: id' },
        { status: 400 }
      );
    }
    
    // 重定向到新的路由格式
    const newUrl = request.nextUrl.clone();
    newUrl.pathname = `/api/autism/get-result/${id}`;
    newUrl.search = '';
    
    return NextResponse.redirect(newUrl);
    
  } catch (error) {
    console.error('获取自闭症测试结果出错:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误', details: String(error) },
      { status: 500 }
    );
  }
} 