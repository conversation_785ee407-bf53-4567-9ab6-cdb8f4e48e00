import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import { z } from 'zod';
import { randomBytes } from 'crypto';
import { generateReferralCode, extractReferralInfo, updateReferralInfo, isValidReferralCode } from '@/utils/referralUtils';
import { getLocalizedPath } from '@/utils/utils';

// API configuration
export const dynamic = 'force-dynamic';

// 定义请求体的验证 schema
const saveAutismResultSchema = z.object({
  type: z.enum(['raadsrTest', 'aq10Test']),
  email: z.string().email(),
  answers: z.record(z.string(), z.any()),
  score: z.union([
    // AQ-10 格式
    z.object({
      score: z.number(),
      max: z.number(),
      significant: z.boolean()
    }),
    // RAADS-R 格式
    z.object({
      language: z.object({
        score: z.number(),
        max: z.number(),
        significant: z.boolean()
      }),
      social: z.object({
        score: z.number(),
        max: z.number(),
        significant: z.boolean()
      }),
      sensoryMotor: z.object({
        score: z.number(),
        max: z.number(),
        significant: z.boolean()
      }),
      interests: z.object({
        score: z.number(),
        max: z.number(),
        significant: z.boolean()
      }),
      total: z.object({
        score: z.number(),
        max: z.number(),
        significant: z.boolean()
      })
    })
  ]),
  userId: z.string().optional(),
  metadata: z.object({
    timestamp: z.string(),
    userAgent: z.string(),
    language: z.string(),
    locale: z.string().optional(),
    referredBy: z.string().optional(), // 推荐人的推荐码
  })
});

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();

    console.log('收到请求数据:', JSON.stringify(body, null, 2));

    // 验证请求体
    const validation = saveAutismResultSchema.safeParse(body);
    if (!validation.success) {
      console.error('验证错误:', validation.error.format());
      console.log('请求数据类型:',
        `type: ${typeof body.type}`,
        `email: ${typeof body.email}`,
        `answers: ${typeof body.answers}`,
        `score: ${typeof body.score}`,
        body.score ? `score结构: ${JSON.stringify(Object.keys(body.score))}` : 'score为空'
      );

      return NextResponse.json(
        { success: false, error: '无效的请求数据', details: validation.error.format() },
        { status: 400 }
      );
    }

    const data = validation.data;

    // 将type字段直接映射到testType（与我们的类型系统兼容）
    const testType = data.type;

    // 确保metadata包含locale信息
    const metadata = {
      ...data.metadata,
      locale: data.metadata.locale || data.metadata.language || 'en'
    };

    // 检查是否提供了用户ID，决定是否自动绑定到用户
    const hasUserId = !!data.userId;

    // 生成访问令牌
    const accessToken = randomBytes(32).toString('hex');

    // 处理推荐逻辑
    let finalMetadata = metadata;
    let shouldUnlockReferrer = false;
    let referrerTestId = null;

    // 如果有推荐码，处理推荐逻辑
    if (metadata.referredBy && isValidReferralCode(metadata.referredBy)) {
      console.log(`处理推荐逻辑: ${metadata.referredBy}`);

      // 查找推荐人的测试结果
      const referrerTests = await prisma.testResult.findMany({
        where: {
          metadata: {
            path: ['referral', 'referralCode'],
            equals: metadata.referredBy
          }
        }
      });

      if (referrerTests.length > 0) {
        const referrerTest = referrerTests[0];
        referrerTestId = referrerTest.id;

        // 更新推荐人的完成统计
        const referrerMetadata = referrerTest.metadata as any;
        const referrerReferralInfo = extractReferralInfo(referrerMetadata);

        const updatedReferrerInfo = {
          ...referrerReferralInfo,
          referralCompletions: referrerReferralInfo.referralCompletions + 1,
          referralCompletionTimestamps: [
            ...referrerReferralInfo.referralCompletionTimestamps,
            new Date().toISOString()
          ]
        };

        const updatedReferrerMetadata = updateReferralInfo(referrerMetadata, updatedReferrerInfo);

        // 检查是否应该解锁推荐人的报告（需要1个完成）
        if (updatedReferrerInfo.referralCompletions >= 1 && referrerTest.isReportLocked) {
          shouldUnlockReferrer = true;
        }

        // 更新推荐人的测试结果
        await prisma.testResult.update({
          where: { id: referrerTest.id },
          data: {
            metadata: updatedReferrerMetadata,
            isReportLocked: shouldUnlockReferrer ? false : referrerTest.isReportLocked
          }
        });

        console.log(`推荐完成统计更新: ${metadata.referredBy}, 总完成数: ${updatedReferrerInfo.referralCompletions}, 解锁推荐人: ${shouldUnlockReferrer}`);
      }
    }

    // 为新用户生成推荐码并初始化推荐信息
    const newReferralCode = generateReferralCode();
    finalMetadata = updateReferralInfo(finalMetadata, {
      referralCode: newReferralCode,
      referredById: referrerTestId || undefined // 记录推荐人的测试ID
    });

    // 创建测试结果记录
    const testResult = await prisma.testResult.create({
      data: {
        testType,
        email: data.email,
        answers: testType === 'aq10Test'
          ? { ...data.answers, score: data.score } // 对于 AQ-10，将 score 存储在 answers 中
          : data.answers,
        scores: data.score, // 原始 score 数据仍保存在 scores 字段
        metadata: finalMetadata,
        // 如果提供了用户ID，自动绑定到用户
        userId: data.userId || null,
        isUserBound: hasUserId,
        accessToken,
      }
    });

    // 构建报告URL
    const reportUrl = getLocalizedPath(`/autism/result/${testResult.id}?token=${accessToken}`, metadata.locale || 'en');

    // 返回成功响应与ID
    return NextResponse.json({
      success: true,
      testId: testResult.id,
      createdAt: testResult.createdAt,
      accessToken,
      reportUrl,
      userId: testResult.userId,
    });

  } catch (error) {
    console.error('保存自闭症测试结果出错:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误', details: String(error) },
      { status: 500 }
    );
  }
} 