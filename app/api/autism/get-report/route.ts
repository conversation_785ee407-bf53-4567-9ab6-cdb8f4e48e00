import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { prisma } from '@/utils/prisma';

// API configuration
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * 获取保存的自闭症报告 - 已移除服务器端缓存，直接从数据库获取
 */
export async function GET(request: NextRequest) {
  const requestId = uuidv4().slice(0, 8);
  const startTime = Date.now();

  try {
    // 获取请求参数
    const id = request.nextUrl.searchParams.get('id');
    let locale = request.nextUrl.searchParams.get('locale') || 'en';
    locale = locale.split('-')[0];

    // 验证测试ID
    if (!id) {
      console.log(`[${requestId}] 缺少测试ID`);
      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh' ? '缺少测试ID' : 'Missing test ID'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'X-Request-ID': requestId
          }
        }
      );
    }

    console.log(`[${requestId}] 直接从数据库获取报告: ${id}-${locale}`);

    // 直接从数据库查询测试结果
    const testResult = await prisma.testResult.findUnique({
      where: { id }
    });

    if (!testResult) {
      console.log(`[${requestId}] 未找到测试结果: ${id}`);
      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh' ? '未找到测试结果' : 'Test result not found'
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
            'X-Request-ID': requestId
          }
        }
      );
    }

    // 验证测试类型
    if (testResult.testType !== 'raadsrTest' && testResult.testType !== 'aq10Test') {
      console.log(`[${requestId}] 测试类型不匹配: ${testResult.testType}`);
      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh' ? '所请求的测试结果不是自闭症测试' : 'The requested test result is not an autism test'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'X-Request-ID': requestId
          }
        }
      );
    }

    // 获取报告内容
    const reports = testResult.reports as Record<string, string> | null;
    const report = reports ? reports[locale] : null;

    const endTime = Date.now();
    console.log(`[${requestId}] 数据库查询完成，耗时: ${endTime - startTime}ms, 报告存在: ${!!report}`);

    // 返回响应数据
    const responseData = {
      report: report || null,
      isReportLocked: testResult.isReportLocked,
      testType: testResult.testType,
      createdAt: testResult.createdAt,
      updatedAt: testResult.updatedAt
    };

    return new NextResponse(
      JSON.stringify(responseData),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
          'X-Response-Time': `${endTime - startTime}ms`,
          'X-Cache': 'DISABLED',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      }
    );

  } catch (error) {
    console.error(`[${requestId}] 获取报告时出错:`, error);

    // 获取语言设置
    let locale = 'en';
    try {
      const acceptLanguage = request.headers.get('accept-language') || '';
      locale = acceptLanguage.includes('zh') ? 'zh' : 'en';
    } catch (e) {
      // 默认使用英文
    }

    return new NextResponse(
      JSON.stringify({
        error: locale === 'zh' ? '获取报告时出错，请稍后重试' : 'Error fetching report, please try again later'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId
        }
      }
    );
  }
} 