import { NextRequest, NextResponse } from 'next/server';
import * as fs from 'fs';
import * as path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { SubscriptionService } from '@/utils/subscriptionService';
import { prisma } from '@/utils/prisma';
import { clearReportCache } from '@/utils/reportCache';
import { generateContentWithOpenRouter, checkOpenRouterHealth } from '@/utils/openrouterService';
import { notifyFeishu } from '@/utils/notifyFeishu';

// API configuration
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Constants for OpenRouter configuration
const MAX_OUTPUT_TOKENS = 8192;
const TEMPERATURE = 0.2;
const TOP_P = 0.95;



/**
 * 生成AI内容，使用OpenRouter
 */
async function generateAIContent(prompt: string, locale: string): Promise<string> {
  const startTime = Date.now();

  try {
    console.log('[autism/report] Generating content with OpenRouter...');

    // 检查 OpenRouter 健康状态
    const isOpenRouterHealthy = await checkOpenRouterHealth();
    if (!isOpenRouterHealthy) {
      throw new Error('OpenRouter service is not available');
    }

    const content = await generateContentWithOpenRouter(prompt, {
      maxOutputTokens: MAX_OUTPUT_TOKENS,
      temperature: TEMPERATURE,
      topP: TOP_P
    });

    if (content && content.trim().length > 0) {
      const responseTime = Date.now() - startTime;
      console.log(`[autism/report] ✅ Successfully generated content with OpenRouter (${responseTime}ms)`);
      return content;
    } else {
      throw new Error('OpenRouter returned empty content');
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('[autism/report] ❌ OpenRouter failed:', errorMessage);

    // 发送失败通知
    await notifyFeishu.aiGenError({
      provider: 'openrouter',
      model: 'google/gemini-2.0-flash-exp:free',
      error: errorMessage,
      prompt,
      endpoint: '/api/autism/report',
      fallbackUsed: false
    });

    throw new Error(`OpenRouter failed: ${errorMessage}`);
  }
}

/**
 * Build RAADS-R report prompt with enhanced structure template
 */
function buildRaadsrPrompt(answers: Record<number, number>, score: any, locale: string): string {
  const language = locale === 'zh' ? '简体中文' : 'English';
  const questionLabel = locale === 'zh' ? '问题' : 'Question';

  // 构建各分类得分描述
  const { language: langScore, social, sensoryMotor, interests, total } = score;

  // 构建回答部分
  let answerText = '';
  Object.entries(answers).forEach(([questionId, value]) => {
    answerText += `${questionLabel}${questionId}: ${value}\n`;
  });

  if (locale === 'zh') {
    return `你是一位专业的自闭症谱系障碍评估专家。根据用户的RAADS-R测试结果，你需要生成一份专业、全面的ASD评估报告。

测试类型: RAADS-R (Ritvo Autism Asperger Diagnostic Scale-Revised)
总分: ${total.score}/${total.max} (显著性: ${total.significant ? '达到临床显著水平' : '未达到临床显著水平'})

各分类得分:
- 语言: ${langScore.score}/${langScore.max} (显著性: ${langScore.significant ? '显著' : '不显著'})
- 社交关系: ${social.score}/${social.max} (显著性: ${social.significant ? '显著' : '不显著'})
- 感知运动: ${sensoryMotor.score}/${sensoryMotor.max} (显著性: ${sensoryMotor.significant ? '显著' : '不显著'})
- 兴趣与规则性: ${interests.score}/${interests.max} (显著性: ${interests.significant ? '显著' : '不显著'})

用户的回答:
${answerText}

请严格按照以下结构生成一份详细的ASD评估报告，每个章节必须包含实质内容，总字数控制在1200-1800字：

## 必须包含的章节结构（按顺序）：

# 结果摘要
[150-200字] 概括性总结RAADS-R测试结果、总分意义及主要发现

## 测试结果总览
[200-250字] 详细分析RAADS-R测试分数和各维度表现，包括：
- 总分解读和临床意义
- 四个分类得分的具体表现
- 与常模的比较分析

## 核心特征分析
[300-400字] 深入解读自闭症谱系相关的核心特征表现，重点分析：
- 显著分类的具体表现
- 语言沟通特点
- 社交互动模式
- 感知运动特征
- 兴趣与行为模式

## 发展轨迹评估
[200-250字] 分析发展模式和潜在成长路径

## 社交互动建议
[200-250字] 针对性的社交技能提升和人际关系建议

## 生活适应策略
[200-250字] 实用的日常生活管理和适应技巧

## 个性化建议
[150-200字] 基于测试结果的专业建议和资源推荐

重要格式要求:
- 报告语言: ${language}
- 使用专业但易于理解的语言
- 格式必须使用Markdown格式
- 每个章节都必须有明确的标题（使用## 二级标题）
- 主标题使用# 一级标题
- 不要在回复中加入"好的"、"我理解"等与报告无关的内容
- 直接从标题"# 结果摘要"开始写作，不要有其他前缀文字
- 确保每个章节内容充实，避免空洞的描述

生成的内容将直接呈现给用户，所以请确保格式规范、内容专业、结构清晰。`;
  } else {
    return `You are a professional autism spectrum disorder assessment expert. Based on the user's RAADS-R test results, you need to generate a professional and comprehensive ASD assessment report.

Test Type: RAADS-R (Ritvo Autism Asperger Diagnostic Scale-Revised)
Total Score: ${total.score}/${total.max} (Significance: ${total.significant ? 'Reaches clinical significance level' : 'Does not reach clinical significance level'})

Category Scores:
- Language: ${langScore.score}/${langScore.max} (Significance: ${langScore.significant ? 'Significant' : 'Not significant'})
- Social Relatedness: ${social.score}/${social.max} (Significance: ${social.significant ? 'Significant' : 'Not significant'})
- Sensory Motor: ${sensoryMotor.score}/${sensoryMotor.max} (Significance: ${sensoryMotor.significant ? 'Significant' : 'Not significant'})
- Circumscribed Interests: ${interests.score}/${interests.max} (Significance: ${interests.significant ? 'Significant' : 'Not significant'})

User's Responses:
${answerText}

Please generate a detailed ASD assessment report following this exact structure. Each section must contain substantial content, with a total word count of 1200-1800 words:

## Required Chapter Structure (in order):

# Summary of Results
[150-200 words] Comprehensive summary of RAADS-R test results, total score significance, and key findings

## Test Results Overview
[200-250 words] Detailed analysis of RAADS-R test scores and dimensional performance, including:
- Total score interpretation and clinical significance
- Specific performance in four categories
- Comparison with normative data

## Core Characteristics Analysis
[300-400 words] In-depth interpretation of core autism spectrum-related characteristics, focusing on:
- Specific manifestations in significant categories
- Language communication patterns
- Social interaction styles
- Sensory-motor characteristics
- Interest and behavioral patterns

## Developmental Trajectory Assessment
[200-250 words] Analysis of developmental patterns and potential growth pathways

## Social Interaction Recommendations
[200-250 words] Targeted social skills enhancement and interpersonal relationship advice

## Life Adaptation Strategies
[200-250 words] Practical daily life management and adaptation techniques

## Personalized Recommendations
[150-200 words] Professional advice and resource recommendations based on test results

Important formatting requirements:
- Report language: ${language}
- Use professional but easy-to-understand language
- Format must use Markdown
- Each section must have clear headings (using ## level-2 headings)
- Main title uses # level-1 heading
- Do not include "okay", "I understand" or other content unrelated to the report
- Start directly with the title "# Summary of Results" without any prefix text
- Ensure each section has substantial content, avoid empty descriptions

The generated content will be presented directly to users, so please ensure proper formatting, professional content, and clear structure.`;
  }
}

/**
 * Build AQ-10 report prompt with enhanced structure template
 */
function buildAq10Prompt(answers: Record<number, number>, score: any, locale: string): string {
  const language = locale === 'zh' ? '简体中文' : 'English';
  const questionLabel = locale === 'zh' ? '问题' : 'Question';

  // 获取详细得分
  const { score: scoreValue, max, significant } = score;

  // 构建回答部分
  let answerText = '';
  Object.entries(answers).forEach(([questionId, value]) => {
    answerText += `${questionLabel}${questionId}: ${value}\n`;
  });

  if (locale === 'zh') {
    return `你是一位专业的自闭症谱系障碍评估专家。根据用户的AQ-10测试结果，你需要生成一份专业、全面的ASD筛查报告。

测试类型: AQ-10 (自闭症商数-10题筛查量表)
得分: ${scoreValue}/${max} (临床意义: ${significant ? '达到筛查阳性标准，建议进一步评估' : '未达到筛查阳性标准'})

用户的回答:
${answerText}

请严格按照以下结构生成一份详细的ASD筛查报告，每个章节必须包含实质内容，总字数控制在800-1200字：

## 必须包含的章节结构（按顺序）：

# 结果摘要
[120-150字] 概括性总结AQ-10测试结果及临床意义

## AQ-10快速评估结果
[200-250字] 您的自闭症谱系快速筛查结果解读，包括：
- 得分详细分析
- 与筛查标准的对比
- 临床意义说明

## 关键指标分析
[250-350字] 重点关注的自闭症特征指标详细分析，包括：
- 社交沟通能力表现
- 重复行为模式倾向
- 感知敏感性特征
- 适应性行为分析

## 筛查工具说明
[150-200字] AQ-10作为筛查工具的解释和局限性

## 后续建议
[150-200字] 基于筛查结果的专业建议和下一步行动

重要格式要求:
- 报告语言: ${language}
- 使用专业但易于理解的语言
- 格式必须使用Markdown格式
- 每个章节都必须有明确的标题（使用## 二级标题）
- 主标题使用# 一级标题
- 不要在回复中加入"好的"、"我理解"等与报告无关的内容
- 直接从标题"# 结果摘要"开始写作，不要有其他前缀文字
- 确保每个章节内容充实，避免空洞的描述

生成的内容将直接呈现给用户，所以请确保格式规范、内容专业、结构清晰。`;
  } else {
    return `You are a professional autism spectrum disorder assessment expert. Based on the user's AQ-10 test results, you need to generate a professional and comprehensive ASD screening report.

Test Type: AQ-10 (Autism Spectrum Quotient - 10 Item Screening)
Score: ${scoreValue}/${max} (Clinical Significance: ${significant ? 'Meets screening positive criteria, further assessment recommended' : 'Does not meet screening positive criteria'})

User's Responses:
${answerText}

Please generate a detailed ASD screening report following this exact structure. Each section must contain substantial content, with a total word count of 800-1200 words:

## Required Chapter Structure (in order):

# Summary of Results
[120-150 words] Comprehensive summary of AQ-10 test results and clinical significance

## AQ-10 Quick Assessment Results
[200-250 words] Interpretation of your autism spectrum quick screening results, including:
- Detailed score analysis
- Comparison with screening criteria
- Clinical significance explanation

## Key Indicators Analysis
[250-350 words] Detailed analysis of key autism characteristic indicators, including:
- Social communication abilities performance
- Repetitive behavior pattern tendencies
- Sensory sensitivity characteristics
- Adaptive behavior analysis

## Screening Tool Explanation
[150-200 words] Explanation about AQ-10 as a screening tool and its limitations

## Follow-up Recommendations
[150-200 words] Professional recommendations and next steps based on screening results

Important formatting requirements:
- Report language: ${language}
- Use professional but easy-to-understand language
- Format must use Markdown
- Each section must have clear headings (using ## level-2 headings)
- Main title uses # level-1 heading
- Do not include "okay", "I understand" or other content unrelated to the report
- Start directly with the title "# Summary of Results" without any prefix text
- Ensure each section has substantial content, avoid empty descriptions

The generated content will be presented directly to users, so please ensure proper formatting, professional content, and clear structure.`;
  }
}

/**
 * Generate Autism report with direct response
 */
export async function POST(request: NextRequest) {
  try {
    // 获取 URL 参数
    const forceRegenerate = request.nextUrl.searchParams.get('force') === 'true';

    // Parse request body
    const requestBody = await request.json();
    console.log('[autism/report] 📥 收到请求体:', JSON.stringify(requestBody, null, 2));
    
    const { type, answers, score, locale = 'en', testId } = requestBody;

    // 获取用户会话
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Validate inputs
    console.log('[autism/report] 🔍 验证参数:', {
      type: !!type,
      typeValue: type,
      answers: !!answers,
      answersKeys: answers ? Object.keys(answers).length : 0,
      score: !!score,
      scoreValue: score,
      locale,
      testId
    });
    
    if (!type || !answers || !score) {
      console.log('[autism/report] ❌ 参数验证失败:', {
        missingType: !type,
        missingAnswers: !answers,
        missingScore: !score
      });
      
      return new NextResponse(
        JSON.stringify({ error: locale === 'zh' ? '缺少必要参数' : 'Missing required fields' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`处理 ${type} 自闭症报告请求，语言: ${locale} ${forceRegenerate ? '(强制重新生成)' : ''}`);

    // 检查报告解锁状态
    if (testId) {
      const reportAccess = await SubscriptionService.checkReportAccess(testId, userId);

      if (!reportAccess.isUnlocked) {
        return new NextResponse(
          JSON.stringify({
            error: locale === 'zh' ? '报告未解锁，请先解锁或升级订阅' : 'Report not unlocked, please unlock or upgrade subscription',
            needsUnlock: true
          }),
          {
            status: 403,
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      }

      // 注意：不要在这里对Pro用户消耗额度！
      // checkReportAccess已经检查了权限，如果返回isUnlocked=true，说明用户有权限访问
      // Pro用户应该通过专门的unlock API来消耗额度，而不是在生成报告时消耗
      console.log(`✅ 用户有权限生成报告: ${reportAccess.reason}, subscriptionType: ${reportAccess.subscriptionType}`)

      // Premium用户自动解锁报告
      if (reportAccess.subscriptionType === 'premium' && reportAccess.reason === 'subscription') {
        await prisma.testResult.update({
          where: { id: testId },
          data: { isReportLocked: false }
        });

        // 清除相关缓存，确保前端能获取到最新的解锁状态
        try {
          clearReportCache(testId);
          console.log(`🗑️ Premium用户自动解锁后清除报告缓存: ${testId}`);
        } catch (cacheError) {
          console.warn('Failed to clear report cache after premium auto-unlock:', cacheError);
        }
      }
    }

    // 根据测试类型构建提示词
    let prompt = type === 'raadsrTest'
      ? buildRaadsrPrompt(answers, score, locale)
      : buildAq10Prompt(answers, score, locale);

    // 如果是强制重新生成，添加额外指令确保生成的内容与先前不同
    if (forceRegenerate) {
      const extraInstruction = locale === 'zh'
        ? '\n\n这是一次强制重新生成的请求，请确保生成全新的、不同于先前的内容。使用新的组织结构和表述方式，但保持专业性和准确性。'
        : '\n\nThis is a forced regeneration request. Please ensure the content is entirely new and different from previous versions. Use a fresh structure and expression while maintaining professionalism and accuracy.';

      prompt += extraInstruction;
    }

    // Generate report using AI (with fallback)
    console.log(`Generating report with prompt length: ${prompt.length} characters`);

    let content = '';
    try {
      content = await generateAIContent(prompt, locale);
    } catch (aiError) {
      console.error('[autism/report] All AI providers failed:', aiError);

      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh'
            ? 'AI服务暂时不可用，请稍后重试'
            : 'AI service temporarily unavailable, please try again later'
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Validate generated content
    if (!content || content.trim().length === 0) {
      console.error('[autism/report] Generated content is empty');
      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh' ? '生成内容为空，请重试' : 'No content generated, please try again'
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // 确保内容以标题开始
    if (!content.startsWith('#')) {
      const defaultTitle = locale === 'zh' ? '# 结果摘要' : '# Summary of Results';
      content = `${defaultTitle}\n\n${content}`;
    }

    console.log(`成功生成报告，内容长度: ${content.length} 字符 ${forceRegenerate ? '(强制生成新内容)' : ''}`);

    // 自动保存报告到数据库
    if (testId) {
      try {
        const testResult = await prisma.testResult.findUnique({
          where: { id: testId }
        });

        if (testResult) {
          const existingReports = testResult.reports as any || {};
          existingReports[locale] = content;

          await prisma.testResult.update({
            where: { id: testId },
            data: { reports: existingReports }
          });

          console.log(`[autism/report] ✅ 报告已自动保存到数据库 (测试ID: ${testId}, 语言: ${locale})`);
        }
      } catch (saveError) {
        console.error(`[autism/report] ❌ 保存报告到数据库失败:`, saveError);
        // 不影响报告生成，继续返回内容
      }
    }

    // 返回报告内容
    return new NextResponse(
      JSON.stringify({ report: content }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': forceRegenerate ? 'no-cache, no-store' : 'private, max-age=300'
        }
      }
    );

  } catch (error) {
    // 更安全的错误记录方式
    console.error('生成自闭症报告出错:', error instanceof Error ? error.message : String(error));

    if (error instanceof Error) {
      console.error('错误类型:', error.name);
      console.error('错误栈:', error.stack);

      // 特别处理认证错误
      if (error.message.includes('Unable to authenticate') || error.name.includes('GoogleAuth')) {
        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        console.error(`认证错误。检查凭据路径: ${credentialsPath}`);

        // 检查凭据文件是否存在
        if (credentialsPath) {
          try {
            if (fs.existsSync(credentialsPath)) {
              console.error(`凭据文件存在于: ${credentialsPath}, 但认证失败。可能存在权限或内容问题。`);
              // 尝试读取文件的前几个字符，检查是否为有效JSON
              try {
                const fileContent = fs.readFileSync(credentialsPath, 'utf8').substring(0, 100);
                console.error(`文件内容开头: ${fileContent}...`);
              } catch (readError) {
                console.error(`无法读取凭据文件: ${readError}`);
              }
            } else {
              console.error(`凭据文件不存在于: ${credentialsPath}`);
            }
          } catch (fsError) {
            console.error(`检查凭据文件时出错: ${fsError}`);
          }
        } else {
          console.error('GOOGLE_APPLICATION_CREDENTIALS 环境变量未设置');
        }
      }
    }

    // 获取请求的语言设置
    let locale = 'en';
    try {
      const acceptLanguage = request.headers.get('accept-language') || '';
      locale = acceptLanguage.includes('zh') ? 'zh' : 'en';
    } catch (e) {
      // 默认使用英文
    }

    // 返回适当的错误响应
    return new NextResponse(
      JSON.stringify({
        error: locale === 'zh'
          ? '生成报告时出错，请稍后重试'
          : 'Error generating report, please try again later'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
} 