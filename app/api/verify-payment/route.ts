import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import Stripe from 'stripe';
import { clearReportCache } from '@/utils/reportCache';

// API configuration
export const dynamic = 'force-dynamic';

// 创建 Stripe 和 Prisma 实例
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

/**
 * 验证支付状态并更新数据库
 * 这是 webhook 的替代方案，通过主动查询 Stripe API 来验证支付状态
 */
export async function POST(request: NextRequest) {
    try {
        const { paymentIntentId, testId } = await request.json();

        if (!paymentIntentId || !testId) {
            return new NextResponse(
                JSON.stringify({
                    error: '缺少必要参数：paymentIntentId, testId'
                }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        console.log(`验证支付状态: ${paymentIntentId} for test ${testId}`);

        // 1. 查询 Stripe API 获取支付状态
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

        if (paymentIntent.status !== 'succeeded') {
            return new NextResponse(
                JSON.stringify({
                    error: '支付尚未成功',
                    status: paymentIntent.status
                }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 2. 检查数据库中的当前状态
        const testResult = await prisma.testResult.findUnique({
            where: { id: testId },
            select: {
                id: true,
                isReportLocked: true,
                metadata: true
            }
        });

        if (!testResult) {
            return new NextResponse(
                JSON.stringify({ error: '未找到测试结果' }),
                {
                    status: 404,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 3. 如果已经解锁，直接返回
        if (!testResult.isReportLocked) {
            return new NextResponse(
                JSON.stringify({
                    success: true,
                    message: '报告已解锁',
                    isReportLocked: false
                }),
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 4. 更新数据库解锁状态
        const updatedResult = await prisma.testResult.update({
            where: { id: testId },
            data: {
                isReportLocked: false,
                metadata: {
                    ...((testResult.metadata as any) || {}),
                    paymentIntentId: paymentIntent.id,
                    paidAmount: paymentIntent.amount / 100,
                    paidCurrency: paymentIntent.currency,
                    paidAt: new Date().toISOString(),
                    stripeCustomerId: paymentIntent.customer as string,
                    verifiedAt: new Date().toISOString(),
                    verificationMethod: 'polling'
                }
            },
            include: {
                user: true
            }
        });

        // 5. 清除相关缓存，确保前端能获取到最新的解锁状态
        try {
            clearReportCache(testId);
            console.log(`🗑️ 支付验证后清除报告缓存: ${testId}`);
        } catch (cacheError) {
            console.warn('Failed to clear report cache after payment verification:', cacheError);
        }

        // 6. Track report usage if user is associated
        if (updatedResult.user) {
            try {
                const SubscriptionService = (await import('@/utils/subscriptionService')).default;
                await SubscriptionService.trackUsage(updatedResult.user.id, 'report');
            } catch (trackingError) {
                console.error('Error tracking report usage:', trackingError);
                // Don't fail the request if tracking fails
            }
        }

        console.log(`成功解锁测试 ID ${testId} 的报告 (通过轮询验证)`);

        return new NextResponse(
            JSON.stringify({
                success: true,
                message: '报告已成功解锁',
                isReportLocked: false,
                verificationMethod: 'polling'
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('验证支付状态时出错:', error);

        return new NextResponse(
            JSON.stringify({
                error: '验证支付状态失败',
                details: error instanceof Error ? error.message : '未知错误'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    } finally {
        await prisma.$disconnect();
    }
} 