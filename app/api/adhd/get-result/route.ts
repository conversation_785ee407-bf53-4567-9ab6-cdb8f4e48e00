import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';

// API configuration
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数中的 ID 和 token
    const id = request.nextUrl.searchParams.get('id');
    const token = request.nextUrl.searchParams.get('token');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Missing required parameter: id' },
        { status: 400 }
      );
    }
    
    // 获取用户会话
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    
    // 查询测试结果
    const testResult = await prisma.testResult.findUnique({
      where: { id }
    });
    
    if (!testResult) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }
    
    // 验证测试结果是否为 ADHD 测试
    if (testResult.testType !== 'adult' && testResult.testType !== 'child') {
      return NextResponse.json(
        { error: 'Requested test result is not an ADHD test' },
        { status: 400 }
      );
    }
    
    // 访问控制逻辑
    const canAccess =
      // 情况1: 已登录且报告已绑定到当前用户
      (userId && testResult.userId === userId) ||
      // 情况2: 报告未绑定且提供了正确的访问令牌
      (!testResult.isUserBound && testResult.accessToken === token);
    
    if (!canAccess) {
      return NextResponse.json(
        { error: 'Unauthorized access to test result' },
        { status: 403 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: testResult
    });
    
  } catch (error) {
    console.error('Error fetching test result:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
} 