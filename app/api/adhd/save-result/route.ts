import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import { z } from 'zod';
import { randomBytes } from 'crypto';
import { generateReferralCode, extractReferralInfo, updateReferralInfo, isValidReferralCode } from '@/utils/referralUtils';
import { getLocalizedPath } from '@/utils/utils';

// 定义请求体的验证 schema
const saveResultSchema = z.object({
  testType: z.enum(['child', 'adult', 'general']),
  email: z.string().email(),
  answers: z.record(z.string(), z.number()),
  scores: z.record(z.string(), z.any()),
  userId: z.string().optional(),
  prescreening: z.record(z.string(), z.union([z.string(), z.number()])).optional(),
  metadata: z.object({
    locale: z.string(),
    deviceType: z.string().optional(),
    referredBy: z.string().optional(), // 推荐人的推荐码
  }).optional(),
});

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求体
    const validation = saveResultSchema.safeParse(body);
    if (!validation.success) {
      console.error('Validation errors:', validation.error.format());
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }

    const data = validation.data;

    // 检查是否提供了用户ID，决定是否自动绑定到用户
    const hasUserId = !!data.userId;

    // 生成访问令牌
    const accessToken = randomBytes(32).toString('hex');

    // 处理推荐逻辑
    let finalMetadata = data.metadata || { locale: 'en' };
    let shouldUnlockReferrer = false;
    let referrerTestId = null;

    // 如果有推荐码，处理推荐逻辑
    if (finalMetadata.referredBy && isValidReferralCode(finalMetadata.referredBy)) {
      console.log(`处理推荐逻辑: ${finalMetadata.referredBy}`);

      // 查找推荐人的测试结果
      const referrerTests = await prisma.testResult.findMany({
        where: {
          metadata: {
            path: ['referral', 'referralCode'],
            equals: finalMetadata.referredBy
          }
        }
      });

      if (referrerTests.length > 0) {
        const referrerTest = referrerTests[0];
        referrerTestId = referrerTest.id;

        // 更新推荐人的完成统计
        const referrerMetadata = referrerTest.metadata as any;
        const referrerReferralInfo = extractReferralInfo(referrerMetadata);

        const updatedReferrerInfo = {
          ...referrerReferralInfo,
          referralCompletions: referrerReferralInfo.referralCompletions + 1,
          referralCompletionTimestamps: [
            ...referrerReferralInfo.referralCompletionTimestamps,
            new Date().toISOString()
          ]
        };

        const updatedReferrerMetadata = updateReferralInfo(referrerMetadata, updatedReferrerInfo);

        // 检查是否应该解锁推荐人的报告（需要1个完成）
        if (updatedReferrerInfo.referralCompletions >= 1 && referrerTest.isReportLocked) {
          shouldUnlockReferrer = true;
        }

        // 更新推荐人的测试结果
        await prisma.testResult.update({
          where: { id: referrerTest.id },
          data: {
            metadata: updatedReferrerMetadata,
            isReportLocked: shouldUnlockReferrer ? false : referrerTest.isReportLocked
          }
        });

        console.log(`推荐完成统计更新: ${finalMetadata.referredBy}, 总完成数: ${updatedReferrerInfo.referralCompletions}, 解锁推荐人: ${shouldUnlockReferrer}`);
      }
    }

    // 为新用户生成推荐码并初始化推荐信息
    const newReferralCode = generateReferralCode();
    finalMetadata = updateReferralInfo(finalMetadata, {
      referralCode: newReferralCode,
      referredById: referrerTestId || undefined // 记录推荐人的测试ID
    });

    console.log(`[ADHD Save] 保存testType: ${data.testType}`);

    // 创建测试结果记录
    const testResult = await prisma.testResult.create({
      data: {
        testType: data.testType,
        email: data.email,
        answers: data.answers,
        scores: data.scores,
        prescreening: data.prescreening || {},
        metadata: finalMetadata,
        // 如果提供了用户ID，自动绑定到用户
        userId: data.userId || null,
        isUserBound: hasUserId,
        accessToken,
      }
    });

    // 构建报告URL
    const reportUrl = getLocalizedPath(`/adhd/result/${testResult.id}?token=${accessToken}`, data.metadata?.locale || 'en');

    // 返回 ID 和创建时间
    return NextResponse.json({
      success: true,
      id: testResult.id,
      testId: testResult.id, // 为了与autism API一致
      createdAt: testResult.createdAt,
      accessToken,
      reportUrl,
    });

  } catch (error) {
    console.error('Error saving test result:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  }
} 