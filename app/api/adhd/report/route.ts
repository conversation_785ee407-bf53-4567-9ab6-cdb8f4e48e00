import { NextRequest, NextResponse } from 'next/server';
import { VertexAI } from '@google-cloud/vertexai';
import * as fs from 'fs';
import * as path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { SubscriptionService } from '@/utils/subscriptionService';
import { prisma } from '@/utils/prisma';
import { clearReportCache } from '@/utils/reportCache';
import { generateContentWithOpenRouter, checkOpenRouterHealth } from '@/utils/openrouterService';
import { notifyFeishu } from '@/utils/notifyFeishu';

// API configuration
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Constants
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT_ID || 'projectatm-89b3a';
const LOCATION = process.env.VERTEX_LOCATION || 'us-central1';
const MODEL_NAME = process.env.VERTEX_MODEL || 'gemini-2.0-flash';

// 初始化 Vertex AI 客户端
const initializeVertexAI = () => {
  try {
    console.log(`[adhd/report] Initializing Vertex AI client for project ${PROJECT_ID} in ${LOCATION}`);

    // 获取凭据文件路径
    const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    console.log(`[adhd/report] Using credentials path: ${credentialsPath}`);

    // 检查凭据文件是否存在
    if (credentialsPath) {
      try {
        if (fs.existsSync(credentialsPath)) {
          console.log(`[adhd/report] Credentials file exists at: ${credentialsPath}`);
        } else {
          console.warn(`[adhd/report] Credentials file not found at: ${credentialsPath}`);
          console.warn('Make sure the credentials file is correctly mounted in the container');
        }
      } catch (e) {
        console.error(`[adhd/report] Error checking credentials file:`, e);
      }
    }

    // 手动设置环境变量确保 Google Auth 库能找到凭据文件
    if (credentialsPath) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialsPath;
    }

    // 初始化VertexAI客户端
    return new VertexAI({
      project: PROJECT_ID,
      location: LOCATION
    });
  } catch (error) {
    console.error('[adhd/report] Error initializing Vertex AI:', error);
    throw error;
  }
};

// Initialize Vertex AI client
const vertexAI = initializeVertexAI();

// Get generative model with appropriate configuration
const getGenerativeModel = () => {
  return vertexAI.getGenerativeModel({
    model: MODEL_NAME,
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.2,
      topP: 0.95,
      topK: 40,
    },
  });
};

/**
 * 生成AI内容，包含Vertex AI和OpenRouter降级方案
 */
async function generateAIContent(prompt: string, locale: string): Promise<string> {
  const startTime = Date.now();
  let content = '';
  let usedProvider = '';
  let error = null;

  // 首先尝试使用 Vertex AI
  try {
    console.log('[adhd/report] Attempting to generate content with Vertex AI...');
    const generativeModel = getGenerativeModel();

    const result = await generativeModel.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: {
        maxOutputTokens: 8192,
        temperature: 0.2,
        topP: 0.95,
        topK: 40,
      }
    });

    const response = await result.response;

    if (response.candidates && response.candidates.length > 0) {
      const textParts = response.candidates[0].content.parts;
      content = textParts.map(part => part.text).join('');
    }

    if (content && content.trim().length > 0) {
      usedProvider = 'vertex-ai';
      const responseTime = Date.now() - startTime;

      // 记录成功的API调用
      await notifyFeishu.paidApiLog({
        provider: 'vertex-ai',
        model: MODEL_NAME,
        prompt,
        response: content,
        success: true,
        responseTime
      });

      console.log(`[adhd/report] ✅ Successfully generated content with Vertex AI (${responseTime}ms)`);
      return content;
    } else {
      throw new Error('Vertex AI returned empty content');
    }

  } catch (vertexError) {
    error = vertexError instanceof Error ? vertexError.message : String(vertexError);
    console.error('[adhd/report] ❌ Vertex AI failed:', error);

    const responseTime = Date.now() - startTime;

    // 记录失败的API调用
    await notifyFeishu.paidApiLog({
      provider: 'vertex-ai',
      model: MODEL_NAME,
      prompt,
      success: false,
      responseTime,
      error
    });

    // 发送AI生成错误通知
    await notifyFeishu.aiGenError({
      provider: 'vertex-ai',
      model: MODEL_NAME,
      error,
      prompt,
      endpoint: '/api/adhd/report',
      fallbackUsed: false
    });
  }

  // 如果 Vertex AI 失败，尝试使用 OpenRouter
  try {
    console.log('[adhd/report] Vertex AI failed, attempting OpenRouter fallback...');

    // 检查 OpenRouter 健康状态
    const isOpenRouterHealthy = await checkOpenRouterHealth();
    if (!isOpenRouterHealthy) {
      throw new Error('OpenRouter service is not available');
    }

    const fallbackStartTime = Date.now();
    content = await generateContentWithOpenRouter(prompt, {
      maxOutputTokens: 8192,
      temperature: 0.2,
      topP: 0.95
    });

    if (content && content.trim().length > 0) {
      usedProvider = 'openrouter';
      const responseTime = Date.now() - fallbackStartTime;

      console.log(`[adhd/report] ✅ Successfully generated content with OpenRouter fallback (${responseTime}ms)`);

      // 发送降级成功通知
      await notifyFeishu.aiGenError({
        provider: 'openrouter',
        model: 'google/gemini-2.0-flash-exp:free',
        error: `Vertex AI failed: ${error}`,
        prompt,
        endpoint: '/api/adhd/report',
        fallbackUsed: true
      });

      return content;
    } else {
      throw new Error('OpenRouter returned empty content');
    }

  } catch (openrouterError) {
    const fallbackError = openrouterError instanceof Error ? openrouterError.message : String(openrouterError);
    console.error('[adhd/report] ❌ OpenRouter fallback also failed:', fallbackError);

    // 发送最终失败通知
    await notifyFeishu.aiGenError({
      provider: 'openrouter',
      model: 'google/gemini-2.0-flash-exp:free',
      error: `Both Vertex AI and OpenRouter failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`,
      prompt,
      endpoint: '/api/adhd/report',
      fallbackUsed: true
    });

    // 抛出最终错误
    throw new Error(`Both AI providers failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`);
  }
}

/**
 * Build adult ADHD report prompt with enhanced structure template
 */
function buildAdultPrompt(answers: Record<number, number>, scores: any, locale: string): string {
  const language = locale === 'zh' ? '简体中文' : 'English';
  const questionLabel = locale === 'zh' ? '问题' : 'Question';

  // Build answer section
  let answerText = '';
  Object.entries(answers).forEach(([questionId, value]) => {
    answerText += `${questionLabel}${questionId}: ${value}\n`;
  });

  if (locale === 'zh') {
    return `你是一位专业的精神健康从业者。根据用户的ADHD测试结果，你需要生成一份专业、全面的ADHD评估报告。

测试类型: 成人ADHD自评量表
测试得分: ${scores.total?.score || 0}/${scores.total?.max || 72}
筛查结果: ${scores.screener?.positiveItems || 0}/6 项阳性 (风险等级: ${scores.screener?.riskLevel || 'low'})

用户的回答:
${answerText}

请严格按照以下结构生成一份详细的ADHD评估报告，每个章节必须包含实质内容，总字数控制在1200-1800字：

## 必须包含的章节结构（按顺序）：

# 结果摘要
[150-200字] 概括性总结ADHD测试结果、症状严重程度及主要发现

## ADHD成人测试结果
[200-250字] 您的成人ADHD症状评估详细分析，包括：
- 总分解读和严重程度评估
- 注意力缺陷维度表现
- 多动冲动维度表现
- 与常模的比较分析

## 注意力与专注度分析
[250-300字] 深入分析注意力缺陷和专注度相关表现，包括：
- 注意力维持困难的具体表现
- 工作记忆和执行功能影响
- 任务完成和时间管理挑战

## 冲动性与多动性评估
[250-300字] 评估冲动行为和多动症状的严重程度，包括：
- 冲动决策和行为控制
- 静坐不安和过度活动
- 情绪调节困难

## 工作与生活管理策略
[250-300字] 针对成人ADHD的实用管理技巧和策略

## 治疗建议与资源
[200-250字] 专业治疗建议和相关资源推荐

重要格式要求:
- 报告语言: ${language}
- 使用专业但易于理解的语言
- 格式必须使用Markdown格式
- 每个章节都必须有明确的标题（使用## 二级标题）
- 主标题使用# 一级标题
- 不要在回复中加入"好的"、"我理解"等与报告无关的内容
- 直接从标题"# 结果摘要"开始写作，不要有其他前缀文字
- 确保每个章节内容充实，避免空洞的描述

生成的内容将直接呈现给用户，所以请确保格式规范、内容专业、结构清晰。`;
  } else {
    return `You are a professional mental health practitioner. Based on the user's ADHD test results, you need to generate a professional and comprehensive ADHD assessment report.

Test Type: Adult ADHD Self-Report Scale
Test Score: ${scores.total?.score || 0}/${scores.total?.max || 72}
Screening Results: ${scores.screener?.positiveItems || 0}/6 positive items (Risk level: ${scores.screener?.riskLevel || 'low'})

User's Responses:
${answerText}

Please generate a detailed ADHD assessment report following this exact structure. Each section must contain substantial content, with a total word count of 1200-1800 words:

## Required Chapter Structure (in order):

# Summary of Results
[150-200 words] Comprehensive summary of ADHD test results, symptom severity, and key findings

## Adult ADHD Test Results
[200-250 words] Detailed analysis of your adult ADHD symptom assessment, including:
- Total score interpretation and severity assessment
- Inattention dimension performance
- Hyperactivity-impulsivity dimension performance
- Comparison with normative data

## Attention and Focus Analysis
[250-300 words] In-depth analysis of attention deficit and focus-related performance, including:
- Specific manifestations of attention maintenance difficulties
- Working memory and executive function impacts
- Task completion and time management challenges

## Impulsivity and Hyperactivity Assessment
[250-300 words] Assessment of impulsive behavior and hyperactivity symptom severity, including:
- Impulsive decision-making and behavioral control
- Restlessness and excessive activity
- Emotional regulation difficulties

## Work and Life Management Strategies
[250-300 words] Practical management techniques and strategies for adult ADHD

## Treatment Recommendations and Resources
[200-250 words] Professional treatment recommendations and related resource suggestions

Important formatting requirements:
- Report language: ${language}
- Use professional but easy-to-understand language
- Format must use Markdown
- Each section must have clear headings (using ## level-2 headings)
- Main title uses # level-1 heading
- Do not include "okay", "I understand" or other content unrelated to the report
- Start directly with the title "# Summary of Results" without any prefix text
- Ensure each section has substantial content, avoid empty descriptions

The generated content will be presented directly to users, so please ensure proper formatting, professional content, and clear structure.`;
  }
}

/**
 * Build child ADHD report prompt with enhanced structure template
 */
function buildChildPrompt(answers: Record<number, number>, scores: any, locale: string): string {
  const language = locale === 'zh' ? '简体中文' : 'English';
  const questionLabel = locale === 'zh' ? '问题' : 'Question';

  // Get detailed scores
  const inattention = scores.inattention;
  const hyperactivity = scores.hyperactivity;
  const opposition = scores.opposition;
  const respondent = scores.respondent;
  const subtype = scores.subtype;

  // Build answer section
  let answerText = '';
  Object.entries(answers).forEach(([questionId, value]) => {
    answerText += `${questionLabel}${questionId}: ${value}\n`;
  });

  if (locale === 'zh') {
    return `你是一位专业的儿童精神健康从业者。根据填写者(${respondent})对孩子的ADHD评估测试结果，你需要生成一份专业、全面的儿童ADHD评估报告。

测试类型: 儿童ADHD评估量表
填写人: ${respondent}
可能的亚型: ${subtype}

分项得分:
- 注意力不集中: ${inattention.score}/${inattention.max} (${inattention.significant ? '显著' : '不显著'})
- 多动冲动: ${hyperactivity.score}/${hyperactivity.max} (${hyperactivity.significant ? '显著' : '不显著'})
- 对立违抗: ${opposition.score}/${opposition.max} (${opposition.significant ? '显著' : '不显著'})

用户的回答:
${answerText}

请严格按照以下结构生成一份详细的儿童ADHD评估报告，每个章节必须包含实质内容，总字数控制在1200-1800字：

## 必须包含的章节结构（按顺序）：

# 结果摘要
[150-200字] 概括性总结孩子的ADHD评估结果、症状表现及主要发现

## 儿童ADHD评估结果
[200-250字] 您孩子的ADHD症状评估详细分析，包括：
- 各分项得分解读和显著性分析
- 可能的ADHD亚型判断
- 症状严重程度评估
- 与年龄常模的比较

## 学校表现分析
[250-300字] 分析孩子在学校环境中的注意力和行为表现，包括：
- 课堂注意力维持困难
- 学习任务完成挑战
- 同伴互动和社交表现
- 规则遵守和自控能力

## 家庭环境表现
[200-250字] 分析孩子在家庭环境中的行为模式和挑战

## 家庭支持策略
[250-300字] 为家长提供的ADHD儿童支持和管理策略

## 教育建议与干预
[200-250字] 专业的教育建议和早期干预策略

重要格式要求:
- 报告语言: ${language}
- 使用专业但对非专业人士友好的语言
- 格式必须使用Markdown格式
- 每个章节都必须有明确的标题（使用## 二级标题）
- 主标题使用# 一级标题
- 对可能的干预措施给出具体、实用的建议
- 保持同理心和支持性的语言风格
- 不要在回复中加入"好的"、"我理解"等与报告无关的内容
- 直接从标题"# 结果摘要"开始写作，不要有其他前缀文字
- 确保每个章节内容充实，避免空洞的描述

生成的内容将直接呈现给用户，所以请确保格式规范、内容专业、结构清晰。`;
  } else {
    return `You are a professional child mental health practitioner. Based on the ADHD assessment test results completed by ${respondent} for the child, you need to generate a professional and comprehensive child ADHD assessment report.

Test Type: Child ADHD Assessment Scale
Completed by: ${respondent}
Possible Subtype: ${subtype}

Subscale Scores:
- Inattention: ${inattention.score}/${inattention.max} (${inattention.significant ? 'Significant' : 'Not significant'})
- Hyperactivity/Impulsivity: ${hyperactivity.score}/${hyperactivity.max} (${hyperactivity.significant ? 'Significant' : 'Not significant'})
- Oppositional Defiant: ${opposition.score}/${opposition.max} (${opposition.significant ? 'Significant' : 'Not significant'})

User's Responses:
${answerText}

Please generate a detailed child ADHD assessment report following this exact structure. Each section must contain substantial content, with a total word count of 1200-1800 words:

## Required Chapter Structure (in order):

# Summary of Results
[150-200 words] Comprehensive summary of the child's ADHD assessment results, symptom presentation, and key findings

## Child ADHD Assessment Results
[200-250 words] Detailed analysis of your child's ADHD symptom assessment, including:
- Subscale score interpretation and significance analysis
- Possible ADHD subtype determination
- Symptom severity assessment
- Comparison with age-appropriate norms

## School Performance Analysis
[250-300 words] Analysis of your child's attention and behavioral performance in school environment, including:
- Classroom attention maintenance difficulties
- Learning task completion challenges
- Peer interaction and social performance
- Rule compliance and self-control abilities

## Home Environment Performance
[200-250 words] Analysis of the child's behavioral patterns and challenges in the home environment

## Family Support Strategies
[250-300 words] Support and management strategies for parents of children with ADHD

## Educational Recommendations and Interventions
[200-250 words] Professional educational recommendations and early intervention strategies

Important formatting requirements:
- Report language: ${language}
- Use professional but non-professional-friendly language
- Format must use Markdown
- Each section must have clear headings (using ## level-2 headings)
- Main title uses # level-1 heading
- Provide specific, practical recommendations for possible interventions
- Maintain empathetic and supportive language style
- Do not include "okay", "I understand" or other content unrelated to the report
- Start directly with the title "# Summary of Results" without any prefix text
- Ensure each section has substantial content, avoid empty descriptions

The generated content will be presented directly to users, so please ensure proper formatting, professional content, and clear structure.`;
  }
}

/**
 * Generate ADHD report with direct response
 */
export async function POST(request: NextRequest) {
  try {
    // 获取 URL 参数
    const forceRegenerate = request.nextUrl.searchParams.get('force') === 'true';

    // Parse request body
    const { testType, answers, scores, locale = 'en', testId } = await request.json();

    // 获取用户会话
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Validate inputs
    if (!testType || !answers || !scores) {
      return new NextResponse(
        JSON.stringify({ error: locale === 'zh' ? '缺少必要参数' : 'Missing required fields' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`Processing ${testType} ADHD report request in ${locale} ${forceRegenerate ? '(FORCE REGENERATE)' : ''}`);

    // 检查报告解锁状态
    if (testId) {
      const reportAccess = await SubscriptionService.checkReportAccess(testId, userId);

      if (!reportAccess.isUnlocked) {
        return new NextResponse(
          JSON.stringify({
            error: locale === 'zh' ? '报告未解锁，请先解锁或升级订阅' : 'Report not unlocked, please unlock or upgrade subscription',
            needsUnlock: true
          }),
          {
            status: 403,
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      }

      console.log(`✅ 用户有权限生成ADHD报告: ${reportAccess.reason}, subscriptionType: ${reportAccess.subscriptionType}`)

      // Premium用户自动解锁报告
      if (reportAccess.subscriptionType === 'premium' && reportAccess.reason === 'subscription') {
        await prisma.testResult.update({
          where: { id: testId },
          data: { isReportLocked: false }
        });

        // 清除相关缓存，确保前端能获取到最新的解锁状态
        try {
          clearReportCache(testId);
          console.log(`🗑️ Premium用户自动解锁后清除ADHD报告缓存: ${testId}`);
        } catch (cacheError) {
          console.warn('Failed to clear report cache after premium auto-unlock:', cacheError);
        }
      }
    }

    // Build prompt based on test type with extra instruction for forced regenerations
    let prompt = testType === 'adult'
      ? buildAdultPrompt(answers, scores, locale)
      : buildChildPrompt(answers, scores, locale);

    // 如果是强制重新生成，添加额外指令确保生成的内容与先前不同
    if (forceRegenerate) {
      const extraInstruction = locale === 'zh'
        ? '\n\n这是一次强制重新生成的请求，请确保生成全新的、不同于先前的内容。使用新的组织结构和表述方式，但保持专业性和准确性。'
        : '\n\nThis is a forced regeneration request. Please ensure the content is entirely new and different from previous versions. Use a fresh structure and expression while maintaining professionalism and accuracy.';

      prompt += extraInstruction;
    }

    // Generate report using AI (with fallback)
    console.log(`Generating report with prompt length: ${prompt.length} characters`);

    let content = '';
    try {
      content = await generateAIContent(prompt, locale);
    } catch (aiError) {
      console.error('[adhd/report] All AI providers failed:', aiError);

      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh'
            ? 'AI服务暂时不可用，请稍后重试'
            : 'AI service temporarily unavailable, please try again later'
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Validate generated content
    if (!content || content.trim().length === 0) {
      console.error('[adhd/report] Generated content is empty');
      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh' ? '生成内容为空，请重试' : 'No content generated, please try again'
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Ensure content starts with a title
    if (!content.startsWith('#')) {
      const defaultTitle = locale === 'zh' ? '# 结果摘要' : '# Summary of Results';
      content = `${defaultTitle}\n\n${content}`;
    }

    console.log(`Successfully generated report with ${content.length} characters ${forceRegenerate ? '(forced new content)' : ''}`);

    // 自动保存报告到数据库
    if (testId) {
      try {
        const testResult = await prisma.testResult.findUnique({
          where: { id: testId }
        });

        if (testResult) {
          const existingReports = testResult.reports as any || {};
          existingReports[locale] = content;

          await prisma.testResult.update({
            where: { id: testId },
            data: { reports: existingReports }
          });

          console.log(`[adhd/report] ✅ 报告已自动保存到数据库 (测试ID: ${testId}, 语言: ${locale})`);
        }
      } catch (saveError) {
        console.error(`[adhd/report] ❌ 保存报告到数据库失败:`, saveError);
        // 不影响报告生成，继续返回内容
      }
    }

    // Return the report content
    return new NextResponse(
      JSON.stringify({ report: content }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': forceRegenerate ? 'no-cache, no-store' : 'private, max-age=300'
        }
      }
    );

  } catch (error) {
    // 更安全的错误记录方式
    console.error('Error generating ADHD report:', error instanceof Error ? error.message : String(error));

    if (error instanceof Error) {
      console.error('Error type:', error.name);
      console.error('Error stack:', error.stack);

      // 特别处理认证错误
      if (error.message.includes('Unable to authenticate') || error.name.includes('GoogleAuth')) {
        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        console.error(`Authentication error. Checking credentials at: ${credentialsPath}`);

        // 检查凭据文件是否存在
        if (credentialsPath) {
          try {
            if (fs.existsSync(credentialsPath)) {
              console.error(`Credentials file exists at: ${credentialsPath}, but auth failed. Possible permission or content issue.`);
              // 尝试读取文件的前几个字符，检查是否为有效JSON
              try {
                const fileContent = fs.readFileSync(credentialsPath, 'utf8').substring(0, 100);
                console.error(`File starts with: ${fileContent}...`);
              } catch (readError) {
                console.error(`Could not read credentials file: ${readError}`);
              }
            } else {
              console.error(`Credentials file NOT found at: ${credentialsPath}`);
            }
          } catch (fsError) {
            console.error(`Error checking credentials file: ${fsError}`);
          }
        } else {
          console.error('GOOGLE_APPLICATION_CREDENTIALS env var not set');
        }
      }
    }

    // Get locale from request headers or default to 'en'
    let locale = 'en';
    try {
      const acceptLanguage = request.headers.get('accept-language') || '';
      locale = acceptLanguage.includes('zh') ? 'zh' : 'en';
    } catch (e) {
      // Default to English if header parsing fails
    }

    // Return appropriate error response
    return new NextResponse(
      JSON.stringify({
        error: locale === 'zh'
          ? '生成报告时出错，请稍后重试'
          : 'Error generating report, please try again later'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
} 