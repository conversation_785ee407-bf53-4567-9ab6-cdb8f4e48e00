import { NextRequest, NextResponse } from 'next/server';
import { preGenerateAdhdReport } from '@/utils/reportPreGeneration';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testId, testType, answers, scores, locale } = body;

    // Validate required fields
    if (!testId || !testType || !answers || !scores || !locale) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate test type
    if (!['adult', 'child'].includes(testType)) {
      return NextResponse.json(
        { error: 'Invalid test type' },
        { status: 400 }
      );
    }

    console.log(`[adhd/pre-generate] Starting pre-generation for test ${testId}, type: ${testType}`);

    // Pre-generate the report in background
    const success = await preGenerateAdhdReport(
      testId,
      testType as 'adult' | 'child',
      answers,
      scores,
      locale
    );

    if (success) {
      console.log(`[adhd/pre-generate] Successfully pre-generated report for test ${testId}`);
      return NextResponse.json({ success: true });
    } else {
      console.error(`[adhd/pre-generate] Failed to pre-generate report for test ${testId}`);
      return NextResponse.json(
        { error: 'Pre-generation failed' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[adhd/pre-generate] Error in pre-generation endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
