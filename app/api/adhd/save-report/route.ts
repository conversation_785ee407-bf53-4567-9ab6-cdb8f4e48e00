import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';

// API configuration
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * 保存ADHD报告到数据库
 */
export async function POST(request: NextRequest) {
  try {
    // 获取 URL 参数
    const forceOverwrite = request.nextUrl.searchParams.get('force') === 'true';

    // 解析请求正文
    const { id, locale, report } = await request.json();

    // 验证参数
    if (!id || !locale || !report) {
      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh' ? '缺少必要参数' : 'Missing required fields'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`保存测试ID ${id} 的${locale}报告，报告长度：${report.length} ${forceOverwrite ? '(强制覆盖)' : ''}`);

    // 查询数据库中的现有测试结果
    const testResult = await prisma.testResult.findUnique({
      where: { id }
    });

    // 如果没有找到测试结果
    if (!testResult) {
      return new NextResponse(
        JSON.stringify({
          error: locale === 'zh' ? '未找到测试结果' : 'Test result not found'
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // 获取现有的报告，如果没有则创建空对象
    const existingReports = (testResult.reports as Record<string, string> | null) || {};

    // 如果强制覆盖模式或者不存在当前语言的报告，则直接更新
    if (forceOverwrite || !existingReports[locale]) {
      // 更新指定语言的报告
      const updatedReports = {
        ...existingReports,
        [locale]: report
      };

      // 更新数据库
      await prisma.testResult.update({
        where: { id },
        data: { reports: updatedReports }
      });

      console.log(`成功${forceOverwrite ? '覆盖' : '保存'}测试ID ${id} 的${locale}报告`);
    } else {
      console.log(`测试ID ${id} 的${locale}报告已存在且未指定强制覆盖，跳过保存`);
    }

    // 返回成功响应
    return new NextResponse(
      JSON.stringify({ success: true }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    // 更安全的错误记录方式
    console.error('保存ADHD报告时出错:', error instanceof Error ? error.message : String(error));

    if (error instanceof Error) {
      console.error('错误类型:', error.name);
      console.error('错误栈:', error.stack);
    }

    // 获取语言设置
    let locale = 'en';
    try {
      const acceptLanguage = request.headers.get('accept-language') || '';
      locale = acceptLanguage.includes('zh') ? 'zh' : 'en';
    } catch (e) {
      // 默认使用英文
    }

    // 返回错误信息
    return new NextResponse(
      JSON.stringify({
        error: locale === 'zh'
          ? '保存报告时出错，请稍后重试'
          : 'Error saving report, please try again later'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
} 