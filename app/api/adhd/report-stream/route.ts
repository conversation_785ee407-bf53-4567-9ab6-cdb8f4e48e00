import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { SubscriptionService } from '@/utils/subscriptionService';
import { prisma } from '@/utils/prisma';
import { clearReportCache } from '@/utils/reportCache';
import { generateContentWithOpenRouter, checkOpenRouterHealth } from '@/utils/openrouterService';
import { notifyFeishu } from '@/utils/notifyFeishu';

// API configuration
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Constants for OpenRouter configuration
const MAX_OUTPUT_TOKENS = 8192;
const TEMPERATURE = 0.2;
const TOP_P = 0.95;

/**
 * Generate AI content with streaming support
 */
async function generateAIContentStream(prompt: string, locale: string, controller: ReadableStreamDefaultController<any>) {
  const startTime = Date.now();

  try {
    console.log('[adhd/report-stream] Generating content with OpenRouter...');

    // Send initial status
    controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
      type: 'status',
      message: locale === 'zh' ? '正在连接AI服务...' : 'Connecting to AI service...'
    })}\n\n`));

    // Check OpenRouter health
    const isOpenRouterHealthy = await checkOpenRouterHealth();
    if (!isOpenRouterHealthy) {
      throw new Error('OpenRouter service is not available');
    }

    controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
      type: 'status',
      message: locale === 'zh' ? '正在生成报告内容...' : 'Generating report content...'
    })}\n\n`));

    // Generate content with streaming
    const content = await generateContentWithOpenRouter(prompt, {
      maxOutputTokens: MAX_OUTPUT_TOKENS,
      temperature: TEMPERATURE,
      topP: TOP_P
    });

    if (content && content.trim().length > 0) {
      const responseTime = Date.now() - startTime;
      console.log(`[adhd/report-stream] ✅ Successfully generated content with OpenRouter (${responseTime}ms)`);
      
      // Stream content in chunks
      const chunkSize = 100; // Characters per chunk
      for (let i = 0; i < content.length; i += chunkSize) {
        const chunk = content.substring(i, i + chunkSize);
        controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
          type: 'content',
          chunk: chunk,
          progress: Math.min((i + chunkSize) / content.length * 100, 100)
        })}\n\n`));

        // Small delay to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Send completion signal
      controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
        type: 'complete',
        content: content
      })}\n\n`));

      return content;
    } else {
      throw new Error('OpenRouter returned empty content');
    }
  } catch (error) {
    console.error('[adhd/report-stream] Error generating content:', error);

    controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
      type: 'error',
      message: locale === 'zh' ? 'AI服务暂时不可用，请稍后重试' : 'AI service temporarily unavailable, please try again later'
    })}\n\n`));
    
    throw error;
  }
}

/**
 * Build ADHD report prompt
 */
function buildAdhdPrompt(testType: string, answers: Record<string, any>, scores: any, locale: string): string {
  // This is a simplified version - you should use the full prompt logic from the original route
  const isZh = locale === 'zh';
  
  if (testType === 'adhd-adult') {
    return `请基于以下ADHD成人评估结果生成详细报告：
测试类型: ${testType}
得分: ${JSON.stringify(scores)}
回答: ${JSON.stringify(answers)}

请生成一份专业的ADHD评估报告，包含结果分析、建议等内容。`;
  } else {
    return `Please generate a detailed ADHD assessment report based on:
Test type: ${testType}
Scores: ${JSON.stringify(scores)}
Answers: ${JSON.stringify(answers)}

Generate a professional ADHD assessment report with analysis and recommendations.`;
  }
}

/**
 * Stream ADHD report generation
 */
export async function POST(request: NextRequest) {
  try {
    // Get URL parameters
    const forceRegenerate = request.nextUrl.searchParams.get('force') === 'true';

    // Parse request body
    const { testType, answers, scores, locale = 'en', testId } = await request.json();

    // Get user session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Validate inputs
    if (!testType || !answers || !scores) {
      return new NextResponse(
        JSON.stringify({ error: locale === 'zh' ? '缺少必要参数' : 'Missing required fields' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`Processing ${testType} ADHD report stream request in ${locale} ${forceRegenerate ? '(FORCE REGENERATE)' : ''}`);

    // Create readable stream
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Build prompt
          const prompt = buildAdhdPrompt(testType, answers, scores, locale);

          // Generate content with streaming
          const content = await generateAIContentStream(prompt, locale, controller);
          
          // Save to database if we have testId and userId
          if (testId && userId && content) {
            try {
              await prisma.testResult.update({
                where: { id: testId },
                data: {
                  reports: {
                    [locale]: content
                  }
                }
              });
              console.log(`[adhd/report-stream] Report saved to database for test ${testId}`);
            } catch (dbError) {
              console.error('[adhd/report-stream] Error saving to database:', dbError);
            }
          }
          
        } catch (error) {
          console.error('[adhd/report-stream] Stream error:', error);
        } finally {
          controller.close();
        }
      }
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Error in ADHD report stream:', error);
    
    return new NextResponse(
      JSON.stringify({
        error: 'Error generating report stream, please try again later'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
