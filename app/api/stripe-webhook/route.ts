import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import SubscriptionService from '@/utils/subscriptionService';

export const dynamic = 'force-dynamic';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
    try {
        const body = await request.text();
        const signature = request.headers.get('stripe-signature');

        if (!signature) {
            console.error('Missing Stripe signature');
            return new NextResponse('Missing signature', { status: 400 });
        }

        let event: Stripe.Event;

        try {
            event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
        } catch (err) {
            console.error('Webhook signature verification failed:', err);
            return new NextResponse('Invalid signature', { status: 400 });
        }

        console.log(`Processing webhook event: ${event.type}`);

        // Handle the event
        switch (event.type) {
            case 'customer.subscription.created':
            case 'customer.subscription.updated':
                await handleSubscriptionEvent(event.data.object as Stripe.Subscription);
                break;

            case 'customer.subscription.deleted':
                await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
                break;

            case 'invoice.payment_succeeded':
                await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
                break;

            case 'invoice.payment_failed':
                await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
                break;

            case 'payment_intent.succeeded':
                await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
                break;

            case 'checkout.session.completed':
                await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
                break;

            default:
                console.log(`Unhandled event type: ${event.type}`);
        }

        return new NextResponse('Webhook processed successfully', { status: 200 });

    } catch (error) {
        console.error('Error processing webhook:', error);
        return new NextResponse(
            `Webhook error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            { status: 500 }
        );
    }
}

async function handleSubscriptionEvent(subscription: Stripe.Subscription) {
    try {
        console.log(`Handling subscription event: ${subscription.id}, status: ${subscription.status}`);
        await SubscriptionService.handleStripeSubscription(subscription);
    } catch (error) {
        console.error('Error handling subscription event:', error);
        throw error;
    }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
    try {
        console.log(`Handling subscription deletion: ${subscription.id}`);
        // The subscription service will handle this via the subscription status update
        await SubscriptionService.handleStripeSubscription(subscription);
    } catch (error) {
        console.error('Error handling subscription deletion:', error);
        throw error;
    }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
    try {
        console.log(`Invoice payment succeeded: ${invoice.id}`);

        if (invoice.subscription) {
            // Retrieve the subscription to get updated info
            const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
            await SubscriptionService.handleStripeSubscription(subscription);
        }
    } catch (error) {
        console.error('Error handling invoice payment succeeded:', error);
        throw error;
    }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
    try {
        console.log(`Invoice payment failed: ${invoice.id}`);

        if (invoice.subscription) {
            // Retrieve the subscription to get updated status
            const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
            await SubscriptionService.handleStripeSubscription(subscription);
        }
    } catch (error) {
        console.error('Error handling invoice payment failed:', error);
        throw error;
    }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
    try {
        console.log(`Payment intent succeeded: ${paymentIntent.id}`);

        // Handle single payments (non-subscription)
        if (!paymentIntent.invoice) {
            await SubscriptionService.handleSinglePayment(paymentIntent);
        }
    } catch (error) {
        console.error('Error handling payment intent succeeded:', error);
        throw error;
    }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
    try {
        console.log(`Checkout session completed: ${session.id}`);

        if (session.mode === 'subscription' && session.subscription) {
            // Retrieve the subscription to get full details
            const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
            await SubscriptionService.handleStripeSubscription(subscription);
        } else if (session.mode === 'payment' && session.payment_intent) {
            // Handle one-time payments
            const paymentIntent = await stripe.paymentIntents.retrieve(session.payment_intent as string);
            await SubscriptionService.handleSinglePayment(paymentIntent);
        }
    } catch (error) {
        console.error('Error handling checkout session completed:', error);
        throw error;
    }
} 