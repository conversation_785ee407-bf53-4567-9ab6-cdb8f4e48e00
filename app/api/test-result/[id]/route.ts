import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';

// API configuration
export const dynamic = 'force-dynamic';

// 创建 Prisma 客户端实例

/**
 * 获取测试结果状态
 */
export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const { id } = await params;

        if (!id) {
            return new NextResponse(
                JSON.stringify({ error: '缺少测试ID参数' }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 查询测试结果
        const testResult = await prisma.testResult.findUnique({
            where: { id },
            select: {
                id: true,
                isReportLocked: true,
                metadata: true,
                updatedAt: true
            }
        });

        if (!testResult) {
            return new NextResponse(
                JSON.stringify({ error: '未找到测试结果' }),
                {
                    status: 404,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        return new NextResponse(
            JSON.stringify({
                id: testResult.id,
                isReportLocked: testResult.isReportLocked,
                metadata: testResult.metadata,
                updatedAt: testResult.updatedAt
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取测试结果状态时出错:', error);

        return new NextResponse(
            JSON.stringify({
                error: '获取测试结果状态失败',
                details: error instanceof Error ? error.message : '未知错误'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    } finally {
        await prisma.$disconnect();
    }
} 