import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';

// API configuration
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// 创建一个新的 Prisma 客户端实例

/**
 * 解锁测试报告 (已弃用 - 请使用 Stripe 支付流程)
 * 此端点现在仅用于管理员手动解锁或特殊情况
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求正文
    const { id, adminKey } = await request.json();

    // 验证参数
    if (!id) {
      return new NextResponse(
        JSON.stringify({
          error: '缺少测试ID参数'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // 检查是否为管理员操作
    const isAdminUnlock = adminKey === process.env.ADMIN_UNLOCK_KEY;

    if (!isAdminUnlock) {
      return new NextResponse(
        JSON.stringify({
          error: '此端点已弃用，请使用 Stripe 支付流程解锁报告'
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`管理员解锁测试ID ${id} 的报告`);

    // 查询数据库中的现有测试结果
    const testResult = await prisma.testResult.findUnique({
      where: { id }
    });

    // 如果没有找到测试结果
    if (!testResult) {
      return new NextResponse(
        JSON.stringify({
          error: '未找到测试结果'
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // 更新数据库，解锁报告
    await prisma.testResult.update({
      where: { id },
      data: {
        isReportLocked: false,
        metadata: {
          ...((testResult.metadata as any) || {}),
          adminUnlocked: true,
          adminUnlockedAt: new Date().toISOString()
        }
      }
    });

    console.log(`成功解锁测试ID ${id} 的报告`);

    // 返回成功响应
    return new NextResponse(
      JSON.stringify({
        success: true,
        isReportLocked: false
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    // 更安全的错误记录方式
    console.error('解锁报告时出错:', error instanceof Error ? error.message : String(error));

    if (error instanceof Error) {
      console.error('错误类型:', error.name);
      console.error('错误栈:', error.stack);
    }

    // 返回错误信息
    return new NextResponse(
      JSON.stringify({
        error: '解锁报告时出错，请稍后重试'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
} 