import { NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";
import { nanoid } from "nanoid";
import { prisma } from "@/utils/prisma";
import crypto from "crypto";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    const { email, callbackUrl, locale } = await request.json();

    // 验证邮箱格式
    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { error: "请提供有效的邮箱地址" },
        { status: 400 }
      );
    }

    // 生成登录令牌
    const token = nanoid(32);
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期

    // 查找或创建用户
    let user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      user = await prisma.user.create({
        data: {
          email,
          name: email.split("@")[0],
        },
      });
    }

    // 创建验证令牌记录
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: await hashToken(token),
        expires,
      },
    });

    // 构建登录链接
    const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const finalCallbackUrl = callbackUrl || `/${locale === 'en' ? '' : locale}`;
    const loginUrl = `${baseUrl}/api/auth/callback/email?callbackUrl=${encodeURIComponent(finalCallbackUrl)}&token=${token}&email=${encodeURIComponent(email)}`;

    // 发送邮件
    await resend.emails.send({
      from: process.env.EMAIL_FROM || "<EMAIL>",
      to: email,
      subject: "登录到 RaadsTest",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">RaadsTest</h1>
            <p style="color: #6b7280; margin: 5px 0 0 0;">自闭症和注意力缺陷多动障碍测试平台</p>
          </div>
          
          <div style="background-color: #f9fafb; border-radius: 8px; padding: 30px; margin: 20px 0;">
            <h2 style="color: #111827; margin: 0 0 20px 0;">登录到您的账户</h2>
            <p style="color: #374151; margin: 0 0 25px 0; line-height: 1.6;">
              点击下面的按钮安全地登录到您的 RaadsTest 账户。此链接将在24小时后过期。
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${loginUrl}" 
                 style="display: inline-block; background-color: #2563eb; color: white; text-decoration: none; 
                        padding: 12px 30px; border-radius: 6px; font-weight: 600; font-size: 16px;">
                登录到 RaadsTest
              </a>
            </div>
            
            <p style="color: #6b7280; font-size: 14px; margin: 25px 0 0 0; line-height: 1.5;">
              如果您没有请求此邮件，您可以安全地忽略它。链接将自动过期。
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #9ca3af; font-size: 12px; margin: 0;">
              此邮件由 RaadsTest 发送。如需支持，请通过 <EMAIL> 联系我们
            </p>
          </div>
        </div>
      `,
      text: `
登录到 RaadsTest

点击下面的链接登录到您的账户：
${loginUrl}

此链接将在24小时后过期。

如果您没有请求此邮件，您可以安全地忽略它。

---
RaadsTest - 自闭症和注意力缺陷多动障碍测试平台
      `,
    });

    return NextResponse.json({ 
      success: true, 
      message: "登录链接已发送到您的邮箱" 
    });

  } catch (error) {
    console.error("发送登录邮件失败:", error);
    return NextResponse.json(
      { error: "发送登录邮件失败，请稍后重试" },
      { status: 500 }
    );
  }
}

// 辅助函数：对令牌进行哈希处理
async function hashToken(token: string): Promise<string> {
  return crypto
    .createHash("sha256")
    .update(`${token}${process.env.NEXTAUTH_SECRET}`)
    .digest("hex");
} 