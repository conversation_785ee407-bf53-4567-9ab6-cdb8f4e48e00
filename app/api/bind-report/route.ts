import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import { TestBindingService } from '@/utils/testBindingService';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { testId, token } = await request.json();
    
    if (!testId) {
      return NextResponse.json({ error: 'Test ID is required' }, { status: 400 });
    }

    // 使用新的绑定服务
    const result = await TestBindingService.bindSingleTest(
      session.user.id,
      testId,
      token
    );

    if (!result.success) {
      return NextResponse.json({ 
        error: result.errors?.[0] || 'Failed to bind test' 
      }, { status: 400 });
    }

    return NextResponse.json({ 
      success: true,
      message: 'Test successfully bound to your account'
    });
  } catch (error) {
    console.error('Error binding report:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
} 