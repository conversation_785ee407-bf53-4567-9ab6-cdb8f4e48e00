import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { prisma } from '@/utils/prisma';
import SubscriptionService from '@/utils/subscriptionService';
import { clearReportCache } from '@/utils/reportCache';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return new NextResponse(
                JSON.stringify({ error: 'Authentication required' }),
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const { testId } = await request.json();

        if (!testId) {
            return new NextResponse(
                JSON.stringify({ error: 'Test ID is required' }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        console.log(`🔓 Processing unlock request for test ${testId} by user ${session.user.email}`);

        // 查找用户
        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
        });

        if (!user) {
            return new NextResponse(
                JSON.stringify({ error: 'User not found' }),
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查测试结果是否存在
        const testResult = await prisma.testResult.findUnique({
            where: { id: testId },
        });

        if (!testResult) {
            return new NextResponse(
                JSON.stringify({ error: 'Test result not found' }),
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        console.log(`📊 User subscription info: type=${user.subscriptionType}, status=${user.subscriptionStatus}`);
        console.log(`📊 Current report status: isReportLocked=${testResult.isReportLocked}`);

        // 检查用户是否为Pro用户
        if (user.subscriptionType !== 'pro' || user.subscriptionStatus !== 'active') {
            return new NextResponse(
                JSON.stringify({
                    error: 'Pro subscription required',
                    needsUpgrade: true
                }),
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查是否已经解锁
        if (!testResult.isReportLocked) {
            console.log(`✅ Report ${testId} is already unlocked`);
            return new NextResponse(
                JSON.stringify({
                    success: true,
                    message: 'Report is already unlocked',
                    alreadyUnlocked: true
                }),
                { status: 200, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查Pro用户的报告解锁权限
        const canUnlock = await SubscriptionService.canPerformAction(user.id, 'report');

        if (!canUnlock.allowed) {
            console.log(`❌ User ${user.email} cannot unlock report: ${canUnlock.reason}`);
            return new NextResponse(
                JSON.stringify({
                    error: canUnlock.reason || 'Report unlock limit exceeded',
                    needsUpgrade: canUnlock.upgradeRequired || false,
                    remaining: 0
                }),
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        console.log(`🎯 Pro user has permission to unlock report`);

        // 执行解锁操作
        await prisma.$transaction(async (tx) => {
            // 解锁报告
            await tx.testResult.update({
                where: { id: testId },
                data: {
                    isReportLocked: false,
                    unlockedByPayment: false, // Pro用户通过订阅解锁，不是付费解锁
                },
            });

            // 消耗Pro用户的解锁次数
            await tx.user.update({
                where: { id: user.id },
                data: {
                    monthlyReportsUsed: {
                        increment: 1
                    }
                }
            });
        });

        console.log(`✅ Database transaction completed - report unlocked and quota consumed`);

        // 清除相关缓存，确保前端能获取到最新的解锁状态
        try {
            // 清除所有语言的缓存
            clearReportCache(testId);
            console.log(`🗑️ 清除报告缓存: ${testId}`);

            // 验证缓存是否被清除
            const { getReportCache } = await import('@/utils/reportCache');
            const cache = getReportCache();
            const remainingCacheKeys = Array.from(cache.keys()).filter(key => key.startsWith(`${testId}-`));
            console.log(`📊 缓存清除验证 - 剩余相关缓存键: ${remainingCacheKeys.length}`);
        } catch (cacheError) {
            console.warn('Failed to clear report cache:', cacheError);
        }

        // 获取更新后的订阅信息
        const updatedSubscriptionInfo = await SubscriptionService.getUserSubscriptionInfo(user.id);

        console.log(`✅ Successfully unlocked report ${testId} for Pro user ${user.email}`);
        console.log(`📊 Remaining reports: ${updatedSubscriptionInfo.reportsRemaining}`);

        return new NextResponse(
            JSON.stringify({
                success: true,
                message: 'Report unlocked successfully',
                reportsRemaining: updatedSubscriptionInfo.reportsRemaining
            }),
            { status: 200, headers: { 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error unlocking report:', error);

        return new NextResponse(
            JSON.stringify({
                error: 'Failed to unlock report',
                details: error instanceof Error ? error.message : 'Unknown error'
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
} 