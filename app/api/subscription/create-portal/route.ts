import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import Stripe from 'stripe';
import { prisma } from '@/utils/prisma';

export const dynamic = 'force-dynamic';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return new NextResponse(
                JSON.stringify({ error: 'Authentication required' }),
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Find user and their Stripe customer ID
        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            select: { stripeCustomerId: true }
        });

        if (!user?.stripeCustomerId) {
            return new NextResponse(
                JSON.stringify({ error: 'No subscription found' }),
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Create Stripe customer portal session
        const portalSession = await stripe.billingPortal.sessions.create({
            customer: user.stripeCustomerId,
            return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/profile`,
        });

        return new NextResponse(
            JSON.stringify({ url: portalSession.url }),
            { status: 200, headers: { 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error creating portal session:', error);

        return new NextResponse(
            JSON.stringify({
                error: 'Failed to create portal session',
                details: error instanceof Error ? error.message : 'Unknown error'
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
} 