import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import Stripe from 'stripe';
import { prisma } from '@/utils/prisma';
import { getPricingConfig } from '@/utils/pricingConfig';

export const dynamic = 'force-dynamic';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return new NextResponse(
                JSON.stringify({ error: 'Authentication required' }),
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const { planId, billingPeriod = 'monthly' } = await request.json();

        if (!planId) {
            return new NextResponse(
                JSON.stringify({ error: 'Plan ID is required' }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Get pricing configuration
        const pricingConfig = getPricingConfig();
        let selectedPlan;

        // Determine the selected plan
        if (planId === 'pro') {
            selectedPlan = billingPeriod === 'yearly' ? pricingConfig.proYearly : pricingConfig.proMonthly;
        } else if (planId === 'premium') {
            selectedPlan = billingPeriod === 'yearly' ? pricingConfig.premiumYearly : pricingConfig.premiumMonthly;
        } else {
            return new NextResponse(
                JSON.stringify({ error: 'Invalid plan ID' }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Find or create user
        let user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            user = await prisma.user.create({
                data: {
                    email: session.user.email,
                    name: session.user.name,
                    image: session.user.image
                }
            });
        }

        // Create or get Stripe customer
        let customerId = user.stripeCustomerId;
        if (!customerId) {
            const customer = await stripe.customers.create({
                email: session.user.email,
                name: session.user.name || undefined,
                metadata: {
                    userId: user.id
                }
            });
            customerId = customer.id;

            // Update user with Stripe customer ID
            await prisma.user.update({
                where: { id: user.id },
                data: { stripeCustomerId: customerId }
            });
        }

        // Create Stripe checkout session with proper customer updates for tax collection
        const checkoutSession = await stripe.checkout.sessions.create({
            customer: customerId,
            payment_method_types: ['card'],
            line_items: [
                {
                    price: selectedPlan.stripePriceId,
                    quantity: 1,
                },
            ],
            mode: 'subscription',
            success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/profile?session_id={CHECKOUT_SESSION_ID}&subscription=success`,
            cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/profile?subscription=canceled`,
            metadata: {
                userId: user.id,
                planId: planId,
                billingPeriod: billingPeriod
            },
            subscription_data: {
                metadata: {
                    userId: user.id,
                    planId: planId,
                    billingPeriod: billingPeriod
                }
            },
            allow_promotion_codes: true,
            billing_address_collection: 'auto',
            customer_update: {
                name: 'auto',
                address: 'auto'
            },
            // Only enable tax ID collection in production or if explicitly configured
            ...(process.env.NODE_ENV === 'production' && {
                tax_id_collection: {
                    enabled: true
                }
            })
        });

        return new NextResponse(
            JSON.stringify({
                sessionId: checkoutSession.id,
                url: checkoutSession.url
            }),
            { status: 200, headers: { 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error creating checkout session:', error);

        // Handle specific Stripe errors
        if (error instanceof Error) {
            // Tax ID collection error
            if (error.message.includes('Tax ID collection')) {
                return new NextResponse(
                    JSON.stringify({ error: 'Tax configuration error. Please try again or contact support.' }),
                    { status: 400, headers: { 'Content-Type': 'application/json' } }
                );
            }

            // Customer update error
            if (error.message.includes('customer_update')) {
                return new NextResponse(
                    JSON.stringify({ error: 'Customer information error. Please try again.' }),
                    { status: 400, headers: { 'Content-Type': 'application/json' } }
                );
            }

            // Price not found error
            if (error.message.includes('price') || error.message.includes('Price')) {
                return new NextResponse(
                    JSON.stringify({ error: 'Subscription plan not available. Please try a different plan.' }),
                    { status: 400, headers: { 'Content-Type': 'application/json' } }
                );
            }
        }

        return new NextResponse(
            JSON.stringify({
                error: 'Failed to create checkout session. Please try again.',
                details: process.env.NODE_ENV === 'development' && error instanceof Error ? error.message : undefined
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
} 