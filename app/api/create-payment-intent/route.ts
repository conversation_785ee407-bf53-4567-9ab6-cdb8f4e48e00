import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import Strip<PERSON> from 'stripe';
import { getDefaultSingleReportPrice } from '@/utils/pricingConfig';

// API configuration
export const dynamic = 'force-dynamic';

// 创建 Stripe 和 Prisma 实例
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: NextRequest) {
    try {
        const { testId, email, amount, currency = 'usd', userId, stripePriceId } = await request.json();

        // 验证必要参数
        if (!testId || !amount) {
            return new NextResponse(
                JSON.stringify({ error: '缺少必要参数：testId, amount' }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        console.log(`创建支付意图: testId=${testId}, amount=${amount}, email=${email}`);

        // 1. 验证测试结果是否存在
        const testResult = await prisma.testResult.findUnique({
            where: { id: testId },
            select: {
                id: true,
                isReportLocked: true,
                testType: true,
                answers: true,
                scores: true
            }
        });

        if (!testResult) {
            return new NextResponse(
                JSON.stringify({ error: '未找到测试结果' }),
                {
                    status: 404,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        if (!testResult.isReportLocked) {
            return new NextResponse(
                JSON.stringify({ error: '报告已解锁，无需支付' }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 2. 获取最终价格
        // 如果提供了 stripePriceId，使用传入的 amount；否则使用默认价格
        let finalAmount = amount;
        let finalStripePriceId = stripePriceId;

        if (!stripePriceId) {
            // 向后兼容：如果没有提供 stripePriceId，使用默认的单次报告价格
            const defaultPrice = getDefaultSingleReportPrice();
            finalAmount = defaultPrice.price;
            finalStripePriceId = defaultPrice.stripePriceId;
        }

        console.log(`支付价格: $${finalAmount}, Stripe Price ID: ${finalStripePriceId}`);

        // 3. 创建或获取 Stripe 客户
        let customerId: string | undefined;
        if (email) {
            try {
                const customers = await stripe.customers.list({
                    email: email,
                    limit: 1
                });

                if (customers.data.length > 0) {
                    customerId = customers.data[0].id;
                    console.log(`找到现有客户: ${customerId}`);
                } else {
                    const customer = await stripe.customers.create({
                        email: email,
                        metadata: {
                            userId: userId || '',
                            testId: testId
                        }
                    });
                    customerId = customer.id;
                    console.log(`创建新客户: ${customerId}`);
                }
            } catch (customerError) {
                console.warn('客户创建/查找失败:', customerError);
                // 继续处理，不阻塞支付流程
            }
        }

        // 4. 创建支付意图
        const paymentIntentData: any = {
            amount: Math.round(finalAmount * 100), // 转换为分
            currency: currency,
            customer: customerId,
            metadata: {
                testId: testId,
                userId: userId || '',
                email: email || '',
                originalAmount: amount.toString(),
                finalAmount: finalAmount.toString(),
                testType: testResult.testType || '',
                stripePriceId: finalStripePriceId || ''
            },
            automatic_payment_methods: {
                enabled: true,
            },
        };

        // 如果有 Stripe Price ID，可以选择使用 Checkout Session 或继续使用 Payment Intent
        // 这里我们继续使用 Payment Intent 以保持现有流程
        const paymentIntent = await stripe.paymentIntents.create(paymentIntentData);

        console.log(`支付意图创建成功: ${paymentIntent.id}`);

        return new NextResponse(
            JSON.stringify({
                clientSecret: paymentIntent.client_secret,
                paymentIntentId: paymentIntent.id,
                amount: finalAmount,
                currency: currency
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('创建支付意图时出错:', error);

        return new NextResponse(
            JSON.stringify({
                error: '创建支付意图失败',
                details: error instanceof Error ? error.message : '未知错误'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    } finally {
        await prisma.$disconnect();
    }
}
