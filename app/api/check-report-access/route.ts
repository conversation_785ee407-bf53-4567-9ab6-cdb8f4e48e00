import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { SubscriptionService } from '@/utils/subscriptionService';

export async function POST(request: NextRequest) {
    try {
        const { testId } = await request.json();

        if (!testId) {
            return NextResponse.json(
                { error: 'Test ID is required' },
                { status: 400 }
            );
        }

        // 获取用户会话
        const session = await getServerSession(authOptions);
        const userId = session?.user?.id;

        // 检查报告访问权限
        const reportAccess = await SubscriptionService.checkReportAccess(testId, userId);

        return NextResponse.json(reportAccess);

    } catch (error) {
        console.error('Error checking report access:', error);
        return NextResponse.json(
            { error: 'Failed to check report access' },
            { status: 500 }
        );
    }
} 