import { NextRequest, NextResponse } from 'next/server';
import ChatService from '@/utils/chatService';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
    try {
        const { testResultId, userMessage, assistantMessage, userId } = await request.json();

        console.log(`\n=== SAVE MESSAGE API START ===`);
        console.log(`Timestamp: ${new Date().toISOString()}`);
        console.log(`testResultId: ${testResultId}`);
        console.log(`userId: ${userId || 'NO_USER_ID'}`);
        console.log(`userMessage length: ${userMessage?.length || 0}`);
        console.log(`assistantMessage length: ${assistantMessage?.length || 0}`);

        if (!testResultId || !userMessage || !assistantMessage) {
            console.log(`❌ Missing required parameters`);
            return new NextResponse(
                JSON.stringify({
                    error: 'Missing required parameters'
                }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        console.log(`📝 Saving USER message...`);
        // 保存用户消息
        await ChatService.addMessage(testResultId, {
            role: 'user',
            content: userMessage
        }, userId);

        console.log(`📝 Saving ASSISTANT message...`);
        // 保存AI回复
        await ChatService.addMessage(testResultId, {
            role: 'assistant',
            content: assistantMessage
        }, userId);

        // 跟踪使用量（仅对订阅用户）
        if (userId) {
            console.log(`📊 Tracking consultation usage for subscribed user...`);
            try {
                await ChatService.trackConsultationUsage(testResultId, userId);
                console.log(`✅ Usage tracking completed`);
            } catch (trackingError) {
                console.error('❌ Error tracking consultation usage:', trackingError);
                // Don't fail the request if tracking fails
            }
        } else {
            console.log(`⏭️ Skipping usage tracking (no userId)`);
        }

        console.log(`✅ Messages saved successfully`);
        console.log(`=== SAVE MESSAGE API END ===\n`);

        return new NextResponse(
            JSON.stringify({ success: true }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('[save-message] ❌ Error saving chat message:', error);
        console.log(`=== SAVE MESSAGE API ERROR END ===\n`);
        return new NextResponse(
            JSON.stringify({
                error: 'Failed to save message'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
} 