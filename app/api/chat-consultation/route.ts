import { NextRequest, NextResponse } from 'next/server';
import { VertexAI } from '@google-cloud/vertexai';
import { prisma } from '@/utils/prisma';
import * as fs from 'fs';
import { generateContentWithOpenRouter, checkOpenRouterHealth } from '@/utils/openrouterService';
import { notifyFeishu } from '@/utils/notifyFeishu';

// API configuration
export const dynamic = 'force-dynamic';

// Constants
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT_ID || 'projectatm-89b3a';
const LOCATION = process.env.VERTEX_LOCATION || 'us-central1';
const MODEL_NAME = process.env.VERTEX_MODEL || 'gemini-2.0-flash';

// 初始化 Vertex AI 客户端
const initializeVertexAI = () => {
    try {
        console.log(`[chat-consultation] Initializing Vertex AI client for project ${PROJECT_ID} in ${LOCATION}`);

        // 获取凭据文件路径
        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        console.log(`[chat-consultation] Using credentials path: ${credentialsPath}`);

        // 检查凭据文件是否存在
        if (credentialsPath) {
            try {
                if (fs.existsSync(credentialsPath)) {
                    console.log(`[chat-consultation] Credentials file exists at: ${credentialsPath}`);
                } else {
                    console.warn(`[chat-consultation] Credentials file not found at: ${credentialsPath}`);
                }
            } catch (e) {
                console.error(`[chat-consultation] Error checking credentials file:`, e);
            }
        }

        // 手动设置环境变量确保 Google Auth 库能找到凭据文件
        if (credentialsPath) {
            process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialsPath;
        }

        // 初始化VertexAI客户端
        return new VertexAI({
            project: PROJECT_ID,
            location: LOCATION
        });
    } catch (error) {
        console.error('[chat-consultation] Error initializing Vertex AI:', error);
        throw error;
    }
};

// Initialize Vertex AI client
const vertexAI = initializeVertexAI();

// Get generative model with appropriate configuration
const getGenerativeModel = () => {
    return vertexAI.getGenerativeModel({
        model: MODEL_NAME,
        generationConfig: {
            maxOutputTokens: 2048,
            temperature: 0.7,
            topP: 0.9,
            topK: 40,
        },
    });
};

/**
 * 生成AI内容，包含Vertex AI和OpenRouter降级方案
 */
async function generateAIContent(prompt: string, locale: string): Promise<string> {
    const startTime = Date.now();
    let content = '';
    let usedProvider = '';
    let error = null;

    // 首先尝试使用 Vertex AI
    try {
        console.log('[chat-consultation] Attempting to generate content with Vertex AI...');
        const generativeModel = getGenerativeModel();

        const result = await generativeModel.generateContent({
            contents: [{ role: 'user', parts: [{ text: prompt }] }],
            generationConfig: {
                maxOutputTokens: 2048,
                temperature: 0.7,
                topP: 0.9,
                topK: 40,
            }
        });

        const response = await result.response;

        if (response.candidates && response.candidates.length > 0) {
            const textParts = response.candidates[0].content.parts;
            content = textParts.map(part => part.text).join('');
        }

        if (content && content.trim().length > 0) {
            usedProvider = 'vertex-ai';
            const responseTime = Date.now() - startTime;

            // 记录成功的API调用
            await notifyFeishu.paidApiLog({
                provider: 'vertex-ai',
                model: MODEL_NAME,
                prompt,
                response: content,
                success: true,
                responseTime
            });

            console.log(`[chat-consultation] ✅ Successfully generated content with Vertex AI (${responseTime}ms)`);
            return content;
        } else {
            throw new Error('Vertex AI returned empty content');
        }

    } catch (vertexError) {
        error = vertexError instanceof Error ? vertexError.message : String(vertexError);
        console.error('[chat-consultation] ❌ Vertex AI failed:', error);

        const responseTime = Date.now() - startTime;

        // 记录失败的API调用
        await notifyFeishu.paidApiLog({
            provider: 'vertex-ai',
            model: MODEL_NAME,
            prompt,
            success: false,
            responseTime,
            error
        });

        // 发送AI生成错误通知
        await notifyFeishu.aiGenError({
            provider: 'vertex-ai',
            model: MODEL_NAME,
            error,
            prompt,
            endpoint: '/api/chat-consultation',
            fallbackUsed: false
        });
    }

    // 如果 Vertex AI 失败，尝试使用 OpenRouter
    try {
        console.log('[chat-consultation] Vertex AI failed, attempting OpenRouter fallback...');

        // 检查 OpenRouter 健康状态
        const isOpenRouterHealthy = await checkOpenRouterHealth();
        if (!isOpenRouterHealthy) {
            throw new Error('OpenRouter service is not available');
        }

        const fallbackStartTime = Date.now();
        content = await generateContentWithOpenRouter(prompt, {
            maxOutputTokens: 2048,
            temperature: 0.7,
            topP: 0.9
        });

        if (content && content.trim().length > 0) {
            usedProvider = 'openrouter';
            const responseTime = Date.now() - fallbackStartTime;

            console.log(`[chat-consultation] ✅ Successfully generated content with OpenRouter fallback (${responseTime}ms)`);

            // 发送降级成功通知
            await notifyFeishu.aiGenError({
                provider: 'openrouter',
                model: 'google/gemini-2.0-flash-exp:free',
                error: `Vertex AI failed: ${error}`,
                prompt,
                endpoint: '/api/chat-consultation',
                fallbackUsed: true
            });

            return content;
        } else {
            throw new Error('OpenRouter returned empty content');
        }

    } catch (openrouterError) {
        const fallbackError = openrouterError instanceof Error ? openrouterError.message : String(openrouterError);
        console.error('[chat-consultation] ❌ OpenRouter fallback also failed:', fallbackError);

        // 发送最终失败通知
        await notifyFeishu.aiGenError({
            provider: 'openrouter',
            model: 'google/gemini-2.0-flash-exp:free',
            error: `Both Vertex AI and OpenRouter failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`,
            prompt,
            endpoint: '/api/chat-consultation',
            fallbackUsed: true
        });

        // 抛出最终错误
        throw new Error(`Both AI providers failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`);
    }
}

// 构建基于测试类型的系统提示
function buildSystemPrompt(testResult: any, locale: string): string {
    const language = locale === 'zh' ? '简体中文' : 'English';
    const testType = testResult.testType;

    let testContext = '';
    let roleDescription = '';

    // 根据测试类型构建上下文
    if (testType === 'raadsrTest') {
        testContext = locale === 'zh'
            ? `用户完成了RAADS-R自闭症筛查测试，这是一个包含80个问题的综合评估工具。`
            : `The user has completed the RAADS-R autism screening test, which is a comprehensive 80-question assessment tool.`;
    } else if (testType === 'aq10Test') {
        testContext = locale === 'zh'
            ? `用户完成了AQ-10自闭症筛查测试，这是一个快速的10问题评估工具。`
            : `The user has completed the AQ-10 autism screening test, which is a quick 10-question assessment tool.`;
    } else if (testType === 'adult' || testType === 'child') {
        testContext = locale === 'zh'
            ? `用户完成了ADHD筛查测试，用于评估注意力缺陷多动障碍相关症状。`
            : `The user has completed an ADHD screening test for assessing attention deficit hyperactivity disorder symptoms.`;
    }

    roleDescription = locale === 'zh'
        ? `你是一位专业、富有同理心的心理健康咨询师，专门帮助用户理解他们的测试结果。你的任务是：
1. 提供基于测试结果的个性化咨询和建议
2. 帮助用户理解测试结果的含义，但强调这不是诊断
3. 提供实用的日常生活建议和应对策略
4. 在适当时建议寻求专业帮助
5. 保持支持和鼓励的语调
6. 基于已有的AI分析报告内容，提供更深入的解释和建议`
        : `You are a professional, empathetic mental health consultant who specializes in helping users understand their test results. Your tasks are:
1. Provide personalized consultation and advice based on test results
2. Help users understand what their test results mean, while emphasizing this is not a diagnosis
3. Offer practical daily life advice and coping strategies
4. Suggest seeking professional help when appropriate
5. Maintain a supportive and encouraging tone
6. Based on the existing AI analysis report content, provide deeper explanations and recommendations`;

    return `${roleDescription}

${testContext}

重要准则 / Important Guidelines:
- 始终强调筛查测试不等于医学诊断 / Always emphasize that screening tests are not medical diagnoses
- 保持专业但易于理解的语言 / Use professional but easily understandable language  
- 提供具体、实用的建议 / Provide specific, practical advice
- 鼓励用户寻求专业帮助 / Encourage users to seek professional help when needed
- 保持同理心和支持性 / Maintain empathy and supportiveness
- 回答语言: ${language} / Response language: ${language}
- 保持回答简洁但有价值，通常2-4段落 / Keep responses concise but valuable, typically 2-4 paragraphs
- 使用markdown格式来增强可读性 / Use markdown formatting to enhance readability
- 参考已有的AI分析报告，提供一致且深入的建议 / Reference the existing AI analysis report to provide consistent and in-depth advice`;
}

// 构建AI分析报告上下文
function buildAnalysisReportContext(testResult: any, locale: string): string {
    const reports = testResult.reports as Record<string, string> || {};
    const reportContent = reports[locale] || reports['en'] || '';

    if (!reportContent) {
        return '';
    }

    const contextLabel = locale === 'zh'
        ? '\n\n用户的AI分析报告内容:\n'
        : '\n\nUser\'s AI Analysis Report Content:\n';

    return `${contextLabel}${reportContent}`;
}

// 构建详细的测试答案上下文
function buildDetailedAnswersContext(testResult: any, locale: string): string {
    const answers = testResult.answers as Record<string, any> || {};
    const testType = testResult.testType;

    if (!answers || Object.keys(answers).length === 0) {
        return '';
    }

    const contextLabel = locale === 'zh'
        ? '\n\n用户的详细测试答案:\n'
        : '\n\nUser\'s Detailed Test Answers:\n';

    let answersText = '';

    // 根据测试类型格式化答案
    if (testType === 'raadsrTest' || testType === 'aq10Test') {
        Object.entries(answers).forEach(([questionId, value]) => {
            answersText += `Q${questionId}: ${value}\n`;
        });
    } else if (testType === 'adult' || testType === 'child') {
        Object.entries(answers).forEach(([questionId, value]) => {
            answersText += `Q${questionId}: ${value}\n`;
        });
    }

    return answersText ? `${contextLabel}${answersText}` : '';
}

// 构建对话历史的上下文
function buildConversationContext(messages: any[]): string {
    if (messages.length === 0) return '';

    const context = messages.map(msg =>
        `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`
    ).join('\n\n');

    return `\n\nPrevious conversation:\n${context}`;
}

export async function POST(request: NextRequest) {
    try {
        const { testResultId, message, locale = 'en', conversationHistory = [], stream = true } = await request.json();

        console.log(`[chat-consultation] Processing consultation request for test ${testResultId} in ${locale} (stream: ${stream})`);

        // 验证输入
        if (!testResultId || !message) {
            return new NextResponse(
                JSON.stringify({
                    error: locale === 'zh' ? '缺少必要参数' : 'Missing required parameters'
                }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 检查是否在开发环境且缺少凭据
        const isDevelopment = process.env.NODE_ENV === 'development';
        const hasCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS &&
            fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS);

        if (isDevelopment && !hasCredentials) {
            console.warn('[chat-consultation] Running in development mode without proper credentials');
            // 在开发环境中返回模拟响应
            return new NextResponse(
                JSON.stringify({
                    response: locale === 'zh'
                        ? '这是一个开发环境的模拟回复。在生产环境中，这里会显示AI生成的个性化咨询建议。'
                        : 'This is a mock response for development environment. In production, this would show AI-generated personalized consultation advice.'
                }),
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 获取测试结果
        const testResult = await prisma.testResult.findUnique({
            where: { id: testResultId },
            include: {
                user: true
            }
        });

        if (!testResult) {
            return new NextResponse(
                JSON.stringify({
                    error: locale === 'zh' ? '未找到测试结果' : 'Test result not found'
                }),
                {
                    status: 404,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // Check consultation access using new ChatService
        const ChatService = (await import('@/utils/chatService')).default;
        const userId = testResult.user?.id;

        console.log(`\n=== CONSULTATION ACCESS CHECK START ===`);
        console.log(`Timestamp: ${new Date().toISOString()}`);
        console.log(`Test Result ID: ${testResultId}`);
        console.log(`User ID: ${userId || 'NO_USER_ID'}`);
        console.log(`User exists: ${!!testResult.user}`);
        console.log(`User subscription type: ${testResult.user?.subscriptionType || 'NO_SUBSCRIPTION'}`);
        console.log(`User subscription status: ${testResult.user?.subscriptionStatus || 'NO_STATUS'}`);
        console.log(`Message being sent: "${message.substring(0, 100)}${message.length > 100 ? '...' : ''}"`);

        const consultationAccess = await ChatService.checkConsultationAccess(testResultId, userId);

        console.log(`Consultation Access Result:`, {
            hasUsedFreeConsultation: consultationAccess.hasUsedFreeConsultation,
            canUseConsultation: consultationAccess.canUseConsultation,
            reason: consultationAccess.reason,
            upgradeRequired: consultationAccess.upgradeRequired,
            remainingConsultations: consultationAccess.remainingConsultations
        });
        console.log(`=== CONSULTATION ACCESS CHECK END ===\n`);

        if (!consultationAccess.canUseConsultation) {
            console.log(`[chat-consultation] ❌ CONSULTATION DENIED for test ${testResultId}`);
            console.log(`[chat-consultation] Reason: ${consultationAccess.reason}`);
            console.log(`[chat-consultation] Has used free: ${consultationAccess.hasUsedFreeConsultation}`);
            console.log(`[chat-consultation] Upgrade required: ${consultationAccess.upgradeRequired}`);

            const errorMessage = consultationAccess.hasUsedFreeConsultation
                ? (locale === 'zh'
                    ? '此报告的免费咨询已使用，请升级订阅获取更多咨询次数'
                    : 'Free consultation for this report has been used. Please upgrade subscription for more consultations')
                : (locale === 'zh'
                    ? '咨询次数已用完，请升级订阅'
                    : 'Consultation limit reached, please upgrade subscription');

            return new NextResponse(
                JSON.stringify({
                    error: errorMessage,
                    upgradeRequired: consultationAccess.upgradeRequired
                }),
                {
                    status: 403,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        console.log(`[chat-consultation] ✅ CONSULTATION ALLOWED for test ${testResultId}`);
        console.log(`[chat-consultation] Proceeding with AI generation...`);

        // 构建提示语
        const systemPrompt = buildSystemPrompt(testResult, locale);
        const conversationContext = buildConversationContext(conversationHistory);
        const analysisReportContext = buildAnalysisReportContext(testResult, locale);
        const detailedAnswersContext = buildDetailedAnswersContext(testResult, locale);

        // 构建包含测试结果的上下文信息
        const testMetadata = testResult.metadata as Record<string, any> || {};

        // 提取测试分数信息
        let scoreInfo = '';
        try {
            if (testResult.testType === 'raadsrTest' && testMetadata.scores) {
                const scores = testMetadata.scores;
                scoreInfo = locale === 'zh'
                    ? `\n- 总分: ${scores.total || 'N/A'}\n- 社交技能: ${scores.socialSkills || 'N/A'}\n- 感觉运动: ${scores.sensoryMotor || 'N/A'}\n- 受限行为: ${scores.restrictedBehavior || 'N/A'}`
                    : `\n- Total score: ${scores.total || 'N/A'}\n- Social skills: ${scores.socialSkills || 'N/A'}\n- Sensory motor: ${scores.sensoryMotor || 'N/A'}\n- Restricted behavior: ${scores.restrictedBehavior || 'N/A'}`;
            } else if (testResult.testType === 'aq10Test' && testMetadata.score !== undefined) {
                scoreInfo = locale === 'zh'
                    ? `\n- 得分: ${testMetadata.score}/10`
                    : `\n- Score: ${testMetadata.score}/10`;
            } else if ((testResult.testType === 'adult' || testResult.testType === 'child') && testMetadata.scores) {
                const scores = testMetadata.scores;
                scoreInfo = locale === 'zh'
                    ? `\n- 总分: ${scores.total || 'N/A'}/${scores.maxScore || 'N/A'} (${scores.percentage || 'N/A'}%)`
                    : `\n- Total score: ${scores.total || 'N/A'}/${scores.maxScore || 'N/A'} (${scores.percentage || 'N/A'}%)`;
            }
        } catch (error) {
            console.log('[chat-consultation] Error parsing test scores:', error);
        }

        const testContextInfo = locale === 'zh'
            ? `\n\n用户的测试信息:\n- 测试类型: ${testResult.testType}\n- 完成时间: ${testResult.createdAt.toLocaleDateString('zh-CN')}\n- 测试状态: ${testResult.isReportLocked ? '报告已锁定' : '报告可访问'}${scoreInfo}`
            : `\n\nUser's test information:\n- Test type: ${testResult.testType}\n- Completion date: ${testResult.createdAt.toLocaleDateString('en-US')}\n- Report status: ${testResult.isReportLocked ? 'Report locked' : 'Report accessible'}${scoreInfo}`;

        const fullPrompt = `${systemPrompt}${testContextInfo}${analysisReportContext}${detailedAnswersContext}${conversationContext}

User's current question: ${message}

Please provide a helpful, empathetic response based on their test results, analysis report, and question.`;

        console.log(`[chat-consultation] Generated prompt length: ${fullPrompt.length} characters`);

        // 调用Vertex AI
        let generativeModel;
        try {
            generativeModel = getGenerativeModel();
        } catch (modelError) {
            console.error('[chat-consultation] Error initializing generative model:', modelError);
            return new NextResponse(
                JSON.stringify({
                    error: locale === 'zh'
                        ? 'AI服务初始化失败，请稍后再试'
                        : 'AI service initialization failed, please try again later'
                }),
                {
                    status: 503,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        if (stream) {
            // 流式响应
            const encoder = new TextEncoder();

            const readableStream = new ReadableStream({
                async start(controller) {
                    try {
                        // 使用降级方案生成内容
                        let content = '';
                        try {
                            content = await generateAIContent(fullPrompt, locale);
                        } catch (aiError) {
                            console.error('[chat-consultation] All AI providers failed in stream:', aiError);
                            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                                error: locale === 'zh'
                                    ? 'AI咨询服务暂时不可用，请稍后重试'
                                    : 'AI consultation service temporarily unavailable, please try again later'
                            })}\n\n`));
                            controller.close();
                            return;
                        }

                        if (!content || content.trim().length === 0) {
                            console.error('[chat-consultation] Generated content is empty');
                            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                                error: locale === 'zh' ? '生成回复失败，请重试' : 'Failed to generate response, please try again'
                            })}\n\n`));
                            controller.close();
                            return;
                        }

                        console.log(`[chat-consultation] Generated response with ${content.length} characters`);

                        // 模拟逐字流式输出，优化阅读体验
                        const words = content.trim().split('');
                        const chunkSize = Math.max(1, Math.floor(words.length / 40)); // 分成约40个块，减少块数量使每块更大

                        for (let i = 0; i < words.length; i += chunkSize) {
                            const chunk = words.slice(i, i + chunkSize).join('');
                            const isComplete = i + chunkSize >= words.length;

                            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                                content: chunk,
                                isComplete,
                                totalProgress: ((i + chunkSize) / words.length * 100).toFixed(0)
                            })}\n\n`));

                            // 增加延迟以确保用户能够跟上阅读速度
                            if (!isComplete) {
                                await new Promise(resolve => setTimeout(resolve, 60 + Math.random() * 80)); // 增加延迟：60-140ms
                            }
                        }

                        controller.close();
                    } catch (error) {
                        console.error('[chat-consultation] Stream error:', error);
                        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                            error: locale === 'zh' ? '咨询服务暂时不可用，请稍后再试' : 'Consultation service is temporarily unavailable, please try again later'
                        })}\n\n`));
                        controller.close();
                    }
                }
            });

            return new Response(readableStream, {
                headers: {
                    'Content-Type': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Cache-Control',
                },
            });
        } else {
            // 非流式响应（使用降级方案）
            let content = '';
            try {
                content = await generateAIContent(fullPrompt, locale);
            } catch (aiError) {
                console.error('[chat-consultation] All AI providers failed:', aiError);

                return new NextResponse(
                    JSON.stringify({
                        error: locale === 'zh'
                            ? 'AI咨询服务暂时不可用，请稍后重试'
                            : 'AI consultation service temporarily unavailable, please try again later'
                    }),
                    {
                        status: 500,
                        headers: { 'Content-Type': 'application/json' }
                    }
                );
            }

            // 验证生成的内容
            if (!content || content.trim().length === 0) {
                console.error('[chat-consultation] Vertex AI returned empty content');
                return new NextResponse(
                    JSON.stringify({
                        error: locale === 'zh' ? '生成回复失败，请重试' : 'Failed to generate response, please try again'
                    }),
                    {
                        status: 500,
                        headers: { 'Content-Type': 'application/json' }
                    }
                );
            }

            console.log(`[chat-consultation] Generated response with ${content.length} characters`);

            // 返回响应
            return new NextResponse(
                JSON.stringify({ response: content.trim() }),
                {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                }
            );
        }

    } catch (error) {
        console.error('[chat-consultation] Error generating consultation response:', error instanceof Error ? error.message : String(error));

        if (error instanceof Error) {
            console.error('Error type:', error.name);
            console.error('Error stack:', error.stack);
        }

        // 获取请求的语言设置
        let locale = 'en';
        try {
            // 重新解析请求体以获取locale
            const requestClone = request.clone();
            const body = await requestClone.json();
            locale = body.locale || 'en';
        } catch (e) {
            // 默认使用英文
            console.warn('[chat-consultation] Could not parse request body for locale:', e);
        }

        // 根据错误类型返回不同的状态码和消息
        let statusCode = 500;
        let errorMessage = locale === 'zh'
            ? '咨询服务暂时不可用，请稍后再试'
            : 'Consultation service is temporarily unavailable, please try again later';

        if (error instanceof Error) {
            // 认证错误
            if (error.message.includes('authentication') || error.message.includes('credentials')) {
                statusCode = 503;
                errorMessage = locale === 'zh'
                    ? '服务配置错误，请联系管理员'
                    : 'Service configuration error, please contact administrator';
            }
            // 网络或超时错误
            else if (error.message.includes('timeout') || error.message.includes('ECONNRESET')) {
                statusCode = 504;
                errorMessage = locale === 'zh'
                    ? '请求超时，请稍后重试'
                    : 'Request timeout, please try again later';
            }
            // Vertex AI API错误
            else if (error.message.includes('Vertex') || error.message.includes('Google')) {
                statusCode = 503;
                errorMessage = locale === 'zh'
                    ? 'AI服务暂时不可用，请稍后再试'
                    : 'AI service is temporarily unavailable, please try again later';
            }
        }

        return new NextResponse(
            JSON.stringify({
                error: errorMessage,
                code: error instanceof Error ? error.name : 'UnknownError'
            }),
            {
                status: statusCode,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
} 