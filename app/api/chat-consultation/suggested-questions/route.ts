import { NextRequest, NextResponse } from 'next/server';
import { VertexAI } from '@google-cloud/vertexai';
import { prisma } from '@/utils/prisma';
import * as fs from 'fs';
import { generateContentWithOpenRouter, checkOpenRouterHealth } from '@/utils/openrouterService';
import { notifyFeishu } from '@/utils/notifyFeishu';

// API configuration
export const dynamic = 'force-dynamic';

// Constants
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT_ID || 'projectatm-89b3a';
const LOCATION = process.env.VERTEX_LOCATION || 'us-central1';
const MODEL_NAME = process.env.VERTEX_MODEL || 'gemini-2.0-flash';

// 初始化 Vertex AI 客户端
const initializeVertexAI = () => {
    try {
        console.log(`[suggested-questions] Initializing Vertex AI client for project ${PROJECT_ID} in ${LOCATION}`);

        // 获取凭据文件路径
        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        console.log(`[suggested-questions] Using credentials path: ${credentialsPath}`);

        // 检查凭据文件是否存在
        if (credentialsPath) {
            try {
                if (fs.existsSync(credentialsPath)) {
                    console.log(`[suggested-questions] Credentials file exists at: ${credentialsPath}`);
                } else {
                    console.warn(`[suggested-questions] Credentials file not found at: ${credentialsPath}`);
                }
            } catch (e) {
                console.error(`[suggested-questions] Error checking credentials file:`, e);
            }
        }

        // 手动设置环境变量确保 Google Auth 库能找到凭据文件
        if (credentialsPath) {
            process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialsPath;
        }

        // 初始化VertexAI客户端
        return new VertexAI({
            project: PROJECT_ID,
            location: LOCATION
        });
    } catch (error) {
        console.error('[suggested-questions] Error initializing Vertex AI:', error);
        throw error;
    }
};

// Initialize Vertex AI client
const vertexAI = initializeVertexAI();

// Get generative model with appropriate configuration
const getGenerativeModel = () => {
    return vertexAI.getGenerativeModel({
        model: MODEL_NAME,
        generationConfig: {
            maxOutputTokens: 1024,
            temperature: 0.8,
            topP: 0.9,
            topK: 40,
        },
    });
};

/**
 * 生成AI内容，包含Vertex AI和OpenRouter降级方案
 */
async function generateAIContent(prompt: string, locale: string): Promise<string> {
    const startTime = Date.now();
    let content = '';
    let usedProvider = '';
    let error = null;

    // 首先尝试使用 Vertex AI
    try {
        console.log('[suggested-questions] Attempting to generate content with Vertex AI...');
        const generativeModel = getGenerativeModel();

        const result = await generativeModel.generateContent({
            contents: [{ role: 'user', parts: [{ text: prompt }] }],
            generationConfig: {
                maxOutputTokens: 1024,
                temperature: 0.8,
                topP: 0.9,
                topK: 40,
            }
        });

        const response = await result.response;

        if (response.candidates && response.candidates.length > 0) {
            const textParts = response.candidates[0].content.parts;
            content = textParts.map(part => part.text).join('');
        }

        if (content && content.trim().length > 0) {
            usedProvider = 'vertex-ai';
            const responseTime = Date.now() - startTime;

            // 记录成功的API调用
            await notifyFeishu.paidApiLog({
                provider: 'vertex-ai',
                model: MODEL_NAME,
                prompt,
                response: content,
                success: true,
                responseTime
            });

            console.log(`[suggested-questions] ✅ Successfully generated content with Vertex AI (${responseTime}ms)`);
            return content;
        } else {
            throw new Error('Vertex AI returned empty content');
        }

    } catch (vertexError) {
        error = vertexError instanceof Error ? vertexError.message : String(vertexError);
        console.error('[suggested-questions] ❌ Vertex AI failed:', error);

        const responseTime = Date.now() - startTime;

        // 记录失败的API调用
        await notifyFeishu.paidApiLog({
            provider: 'vertex-ai',
            model: MODEL_NAME,
            prompt,
            success: false,
            responseTime,
            error
        });

        // 发送AI生成错误通知
        await notifyFeishu.aiGenError({
            provider: 'vertex-ai',
            model: MODEL_NAME,
            error,
            prompt,
            endpoint: '/api/chat-consultation/suggested-questions',
            fallbackUsed: false
        });
    }

    // 如果 Vertex AI 失败，尝试使用 OpenRouter
    try {
        console.log('[suggested-questions] Vertex AI failed, attempting OpenRouter fallback...');

        // 检查 OpenRouter 健康状态
        const isOpenRouterHealthy = await checkOpenRouterHealth();
        if (!isOpenRouterHealthy) {
            throw new Error('OpenRouter service is not available');
        }

        const fallbackStartTime = Date.now();
        content = await generateContentWithOpenRouter(prompt, {
            maxOutputTokens: 1024,
            temperature: 0.8,
            topP: 0.9
        });

        if (content && content.trim().length > 0) {
            usedProvider = 'openrouter';
            const responseTime = Date.now() - fallbackStartTime;

            console.log(`[suggested-questions] ✅ Successfully generated content with OpenRouter fallback (${responseTime}ms)`);

            // 发送降级成功通知
            await notifyFeishu.aiGenError({
                provider: 'openrouter',
                model: 'google/gemini-2.0-flash-exp:free',
                error: `Vertex AI failed: ${error}`,
                prompt,
                endpoint: '/api/chat-consultation/suggested-questions',
                fallbackUsed: true
            });

            return content;
        } else {
            throw new Error('OpenRouter returned empty content');
        }

    } catch (openrouterError) {
        const fallbackError = openrouterError instanceof Error ? openrouterError.message : String(openrouterError);
        console.error('[suggested-questions] ❌ OpenRouter fallback also failed:', fallbackError);

        // 发送最终失败通知
        await notifyFeishu.aiGenError({
            provider: 'openrouter',
            model: 'google/gemini-2.0-flash-exp:free',
            error: `Both Vertex AI and OpenRouter failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`,
            prompt,
            endpoint: '/api/chat-consultation/suggested-questions',
            fallbackUsed: true
        });

        // 抛出最终错误
        throw new Error(`Both AI providers failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`);
    }
}

// 构建推荐问题生成的系统提示
function buildSuggestedQuestionsPrompt(testResult: any, conversationHistory: any[], locale: string): string {
    const language = locale === 'zh' ? '简体中文' : 'English';
    const testType = testResult.testType;

    // 获取测试类型描述
    let testDescription = '';
    if (testType === 'raadsrTest') {
        testDescription = locale === 'zh'
            ? 'RAADS-R自闭症筛查测试'
            : 'RAADS-R autism screening test';
    } else if (testType === 'aq10Test') {
        testDescription = locale === 'zh'
            ? 'AQ-10自闭症筛查测试'
            : 'AQ-10 autism screening test';
    } else if (testType === 'adult' || testType === 'child') {
        testDescription = locale === 'zh'
            ? 'ADHD筛查测试'
            : 'ADHD screening test';
    }

    // 构建对话历史摘要
    let conversationSummary = '';
    if (conversationHistory.length > 0) {
        const recentMessages = conversationHistory.slice(-6); // 取最近6条消息
        conversationSummary = recentMessages.map(msg =>
            `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content.substring(0, 200)}...`
        ).join('\n');
    }

    // 获取AI分析报告摘要
    const reports = testResult.reports as Record<string, string> || {};
    const reportContent = reports[locale] || reports['en'] || '';
    let reportSummary = '';
    if (reportContent) {
        // 提取报告的前500个字符作为摘要
        reportSummary = reportContent.substring(0, 500) + '...';
    }

    // 获取测试分数信息
    const testMetadata = testResult.metadata as Record<string, any> || {};
    let scoreInfo = '';
    try {
        if (testResult.testType === 'raadsrTest' && testMetadata.scores) {
            const scores = testMetadata.scores;
            scoreInfo = locale === 'zh'
                ? `总分: ${scores.total || 'N/A'}, 社交技能: ${scores.socialSkills || 'N/A'}, 感觉运动: ${scores.sensoryMotor || 'N/A'}, 受限行为: ${scores.restrictedBehavior || 'N/A'}`
                : `Total: ${scores.total || 'N/A'}, Social: ${scores.socialSkills || 'N/A'}, Sensory: ${scores.sensoryMotor || 'N/A'}, Restricted: ${scores.restrictedBehavior || 'N/A'}`;
        } else if (testResult.testType === 'aq10Test' && testMetadata.score !== undefined) {
            scoreInfo = locale === 'zh'
                ? `得分: ${testMetadata.score}/10`
                : `Score: ${testMetadata.score}/10`;
        } else if ((testResult.testType === 'adult' || testResult.testType === 'child') && testMetadata.scores) {
            const scores = testMetadata.scores;
            scoreInfo = locale === 'zh'
                ? `总分: ${scores.total || 'N/A'}/${scores.maxScore || 'N/A'} (${scores.percentage || 'N/A'}%)`
                : `Total: ${scores.total || 'N/A'}/${scores.maxScore || 'N/A'} (${scores.percentage || 'N/A'}%)`;
        }
    } catch (error) {
        console.log('[suggested-questions] Error parsing test scores:', error);
    }

    if (locale === 'zh') {
        return `你是一位专业的心理健康咨询师，需要为用户生成个性化的推荐问题。

用户信息:
- 测试类型: ${testDescription}
- 测试分数: ${scoreInfo}
- 测试完成时间: ${testResult.createdAt.toLocaleDateString('zh-CN')}

AI分析报告摘要:
${reportSummary}

对话历史摘要:
${conversationSummary || '暂无对话历史'}

请基于以上信息，生成3个个性化的推荐问题，这些问题应该：
1. 与用户的测试结果和分析报告高度相关
2. 能够引导用户进行更深入的自我探索
3. 避免重复之前对话中已经讨论过的内容
4. 具有实用性，能帮助用户更好地理解自己
5. 语言自然、易懂，避免过于专业的术语
6. 使用第一人称视角，就像用户自己想问的问题一样（例如："我的这个分数意味着什么？"而不是"你的分数意味着什么？"）
7. **每个问题严格控制在10个字以内，力求简洁明了**
8. **使用简单直接的表达，避免复合句和从句**
9. **优先使用疑问词开头（我、如何、为什么、需要等）**
10. **避免专业术语，使用日常口语化表达**
11. **问题要能激发好奇心和紧迫感，促使用户立即点击**

请直接返回3个问题，每行一个，不需要编号或其他格式。回答语言: ${language}`;
    } else {
        return `You are a professional mental health consultant who needs to generate personalized suggested questions for users.

User Information:
- Test Type: ${testDescription}
- Test Scores: ${scoreInfo}
- Test Completion Date: ${testResult.createdAt.toLocaleDateString('en-US')}

AI Analysis Report Summary:
${reportSummary}

Conversation History Summary:
${conversationSummary || 'No conversation history yet'}

Based on the above information, please generate 3 personalized suggested questions that should:
1. Be highly relevant to the user's test results and analysis report
2. Guide users toward deeper self-exploration
3. Avoid repeating content already discussed in previous conversations
4. Be practical and help users better understand themselves
5. Use natural, easy-to-understand language, avoiding overly technical terms
6. Use first-person perspective, as if the user themselves would ask these questions (e.g., "What does my score mean?" instead of "What does your score mean?")
7. **Keep each question under 8 words maximum, aim for brevity and clarity**
8. **Use simple, direct language, avoid complex or compound sentences**
9. **Start with question words (What, How, Why, Do, Should, etc.)**
10. **Avoid technical jargon, use everyday conversational language**
11. **Questions should spark curiosity and urgency to encourage immediate clicks**

Please return 3 questions directly, one per line, without numbering or other formatting. Response language: ${language}`;
    }
}

export async function POST(request: NextRequest) {
    try {
        const { testResultId, locale = 'en', conversationHistory = [] } = await request.json();

        console.log(`[suggested-questions] Processing request for test ${testResultId} in ${locale}`);

        // 验证输入
        if (!testResultId) {
            return new NextResponse(
                JSON.stringify({
                    error: locale === 'zh' ? '缺少必要参数' : 'Missing required parameters'
                }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 获取测试结果
        const testResult = await prisma.testResult.findUnique({
            where: { id: testResultId },
        });

        if (!testResult) {
            return new NextResponse(
                JSON.stringify({
                    error: locale === 'zh' ? '未找到测试结果' : 'Test result not found'
                }),
                {
                    status: 404,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 构建提示语
        const prompt = buildSuggestedQuestionsPrompt(testResult, conversationHistory, locale);

        console.log(`[suggested-questions] Generated prompt length: ${prompt.length} characters`);

        // Generate suggested questions using AI (with fallback)
        let content = '';
        try {
            content = await generateAIContent(prompt, locale);
        } catch (aiError) {
            console.error('[suggested-questions] All AI providers failed:', aiError);

            return new NextResponse(
                JSON.stringify({
                    error: locale === 'zh'
                        ? 'AI推荐问题服务暂时不可用，请稍后重试'
                        : 'AI suggested questions service temporarily unavailable, please try again later'
                }),
                {
                    status: 500,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 验证生成的内容
        if (!content || content.trim().length === 0) {
            console.error('[suggested-questions] Generated content is empty');
            return new NextResponse(
                JSON.stringify({
                    error: locale === 'zh' ? '生成推荐问题失败，请重试' : 'Failed to generate suggested questions, please try again'
                }),
                {
                    status: 500,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 解析生成的问题
        const questions = content.trim().split('\n')
            .map(q => q.trim())
            .filter(q => q.length > 0)
            .slice(0, 3); // 确保最多3个问题

        console.log(`[suggested-questions] Generated ${questions.length} questions`);

        // 返回响应
        return new NextResponse(
            JSON.stringify({ questions }),
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            }
        );

    } catch (error) {
        console.error('[suggested-questions] Error generating suggested questions:', error instanceof Error ? error.message : String(error));

        if (error instanceof Error) {
            console.error('Error type:', error.name);
            console.error('Error stack:', error.stack);
        }

        // 获取请求的语言设置
        let locale = 'en';
        try {
            const body = await request.json();
            locale = body.locale || 'en';
        } catch (e) {
            // 默认使用英文
        }

        return new NextResponse(
            JSON.stringify({
                error: locale === 'zh'
                    ? '推荐问题生成服务暂时不可用，请稍后再试'
                    : 'Suggested questions service is temporarily unavailable, please try again later'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
} 