import { NextRequest, NextResponse } from 'next/server';
import ChatService from '@/utils/chatService';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const testResultId = searchParams.get('testResultId');

        if (!testResultId) {
            return new NextResponse(
                JSON.stringify({
                    error: 'Missing testResultId parameter'
                }),
                {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 获取聊天历史
        const chatHistory = await ChatService.getChatHistory(testResultId);

        // 获取聊天统计信息
        const chatStats = await ChatService.getChatStats(testResultId);

        return new NextResponse(
            JSON.stringify({
                messages: chatHistory,
                stats: chatStats
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('[chat-history] Error fetching chat history:', error);
        return new NextResponse(
            JSON.stringify({
                error: 'Failed to fetch chat history'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
} 