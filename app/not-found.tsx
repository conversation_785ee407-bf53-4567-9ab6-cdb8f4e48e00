// app/[locale]/not-found.tsx

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function NotFound() {
    const router = useRouter();
    const [countdown, setCountdown] = useState(6);

    useEffect(() => {
        const timer = setInterval(() => {
            setCountdown((prev) => prev - 1);
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    // 单独使用 useEffect 来处理重定向
    useEffect(() => {
        if (countdown === 0) {
            // 使用 setTimeout 来确保状态更新完成后再重定向
            const redirect = setTimeout(() => {
                router.push('/');
            }, 100);

            return () => clearTimeout(redirect);
        }
    }, [countdown, router]);

    return (
        <main className="flex items-center justify-center min-h-[calc(100vh-theme(spacing.header)-theme(spacing.footer))] px-4 sm:px-6">
            <div className="text-center space-y-6 max-w-md w-full">
                <h1 className="text-7xl sm:text-9xl font-bold text-gradient">404</h1>
                <h2 className="text-xl sm:text-2xl font-semibold text-gray-700">Page Not Found</h2>
                <p className="text-gray-600">
                    Sorry, the page you are looking for does not exist.
                </p>
                <p className="text-gray-500 text-sm sm:text-base">
                    Redirecting to homepage in <span className="font-medium text-primary">{countdown}</span> seconds...
                </p>
                <div className="pt-2">
                    <Link 
                        href="/"
                        className="inline-block px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 font-medium text-sm sm:text-base"
                    >
                        Return to Homepage
                    </Link>
                </div>
            </div>
        </main>
    );
}