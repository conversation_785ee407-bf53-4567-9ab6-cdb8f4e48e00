@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --primary: #3b82f6;
        --primary-dark: #2563eb;
        --background: #ffffff;
        --header-height-mobile: 64px;
        --header-height-desktop: 72px;
        --header-height-scrolled-mobile: 56px;
        --header-height-scrolled-desktop: 64px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        @apply tracking-tight font-medium font-heading;
    }

    h1 {
        @apply text-balance font-heading;
    }

    h2,
    h3 {
        @apply font-heading;
    }

    p {
        @apply text-balance leading-relaxed font-sans;
    }

    /* 为各种元素应用新字体 */
    .testimonial-quote {
        @apply font-serif italic;
    }

    blockquote {
        @apply font-serif italic border-l-4 border-blue-200 pl-4 py-2;
    }
}

body {
    @apply bg-background text-gray-700;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 为不同标题级别添加更具层次的样式 */
h1 {
    @apply text-4xl md:text-5xl font-bold mb-6 leading-tight;
}

h2 {
    @apply text-3xl md:text-4xl font-semibold mb-5 leading-tight;
}

h3 {
    @apply text-2xl md:text-3xl font-medium mb-4;
}

h4 {
    @apply text-xl md:text-2xl font-medium mb-3;
}

.text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-dark;
}

@layer utilities {
    .bg-gradient-soft {
        @apply bg-gradient-to-b from-background to-background/90;
    }

    .bg-gradient-accent {
        @apply bg-gradient-to-r from-primary to-primary-dark;
    }

    .text-balance {
        text-wrap: balance;
    }

    /* Added utilities for card layouts */
    .equal-height-cards {
        @apply grid grid-cols-1 sm:grid-cols-2 gap-8 max-w-4xl mx-auto;
        display: grid;
        grid-auto-rows: 1fr;
    }

    .card-content {
        @apply flex flex-col h-full;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

/* Enhanced Typography */
.prose-custom {
    @apply max-w-none leading-relaxed;
}

.prose-custom h1,
.prose-custom h2,
.prose-custom h3,
.prose-custom h4 {
    @apply font-bold text-gray-800 mb-4 font-heading;
}

.prose-custom p {
    @apply mb-6 text-gray-600 font-sans;
}

.prose-custom ul {
    @apply mb-6 list-disc pl-5 text-gray-600;
}

.prose-custom blockquote {
    @apply font-serif italic border-l-4 border-blue-200 pl-4 py-2 my-6 text-gray-700;
}

.card-shadow {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

/* Card Hover Effects */
.card-hover {
    @apply transition duration-300 hover:shadow-lg hover:transform hover:scale-[1.01];
}

/* Test icon circles */
.w-20.h-20.rounded-full.bg-white.shadow-sm.flex span {
    text-wrap: nowrap;
    white-space: nowrap;
    font-size: 1.1rem;
    line-height: 1;
}

/* Trust Elements */
.trust-badge {
    @apply inline-flex items-center justify-center bg-blue-50 text-blue-600 rounded-full text-sm font-medium px-3 py-1;
}

/* 为测试页面引入特殊样式 */
.scientific-reference {
    @apply font-serif text-sm text-gray-500 italic;
}

/* 客户评价 */
.testimonial-content {
    @apply font-serif italic text-gray-600 mb-6 text-lg;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
    .mobile-container {
        @apply px-4;
    }

    .mobile-stack {
        @apply flex flex-col gap-4;
    }

    /* Fixed height for cards on mobile */
    .equal-height-card {
        min-height: 350px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 4rem;
    /* Account for fixed header */
}

/* Fixed header adjustments */
.fixed-header-padding {
    padding-top: 4rem;
}

/* 改进排版间距和节奏 */
.section-spacing {
    @apply py-16 md:py-24;
}

.content-spacing {
    @apply my-12;
}

/* 改进表单样式 */
.form-field label {
    @apply font-heading text-sm font-medium mb-1 block;
}

.landing-section {
    @apply py-16 md:py-24;
}

.section-heading {
    @apply font-heading font-bold text-3xl md:text-4xl text-center mb-8;
}

.section-subheading {
    @apply font-heading font-medium text-xl md:text-2xl text-center text-gray-600 mb-12;
}

/* 确保在移动端为fixed header预留足够空间 */
.page-with-header {
    padding-top: var(--header-height-mobile);
}

@media (min-width: 768px) {
    .page-with-header {
        padding-top: var(--header-height-desktop);
    }
}

/* Header responsive height styles */
.header-responsive {
    height: var(--header-height-mobile);
}

.header-responsive.scrolled {
    height: var(--header-height-scrolled-mobile);
}

@media (min-width: 768px) {
    .header-responsive {
        height: var(--header-height-desktop);
    }

    .header-responsive.scrolled {
        height: var(--header-height-scrolled-desktop);
    }
}

/* Profile page specific optimizations */
.profile-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 transition-all duration-200;
}

.profile-card:hover {
    @apply shadow-md border-gray-200;
}

/* Enhanced mobile responsiveness for profile components */
@media (max-width: 640px) {
    .profile-grid {
        @apply space-y-4;
    }

    .profile-avatar {
        @apply w-16 h-16;
    }

    .test-card-mobile {
        @apply flex-col space-y-3;
    }

    .test-score-mobile {
        @apply self-start;
    }
}

/* Test history cards improvements */
.test-card {
    @apply transition-all duration-200 hover:shadow-md hover:scale-[1.01];
}

.test-indicator {
    @apply transition-transform duration-200 group-hover:scale-105;
}

/* Smooth animations for interactive elements */
.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(8px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 智能锁定报告的移动端优化样式 */
.smart-locked-report {
    /* 触控优化 */
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.smart-locked-report button {
    /* 确保按钮有足够的触控区域 */
    min-height: 44px;
    min-width: 44px;
}

.smart-locked-report .social-button {
    /* 社交按钮触控优化 */
    min-height: 40px;
    min-width: 40px;
}

/* 固定定位时的优化 */
.smart-locked-report.fixed-position {
    /* 防止内容溢出视口 */
    max-height: 100vh;
    max-width: 100vw;

    /* iOS Safari 优化 */
    -webkit-overflow-scrolling: touch;

    /* 滚动条优化 */
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

    /* 不在这里设置transform，让JavaScript控制 */
}

/* 滚动条WebKit样式 */
.smart-locked-report.fixed-position::-webkit-scrollbar {
    width: 6px;
}

.smart-locked-report.fixed-position::-webkit-scrollbar-track {
    background: transparent;
}

.smart-locked-report.fixed-position::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
}

.smart-locked-report.fixed-position::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
    .smart-locked-report.fixed-position {
        /* 移动端留出更多边距 */
        margin: 20px;
        max-height: calc(100vh - 40px);
        max-width: calc(100vw - 40px);
    }

    .smart-locked-report .content-container {
        /* 移动端内容优化 */
        padding: 12px;
    }

    .smart-locked-report .social-grid {
        /* 移动端社交按钮网格优化 */
        gap: 8px;
    }

    /* 移动端滚动条隐藏 */
    .smart-locked-report.fixed-position::-webkit-scrollbar {
        width: 2px;
    }

    .smart-locked-report.fixed-position::-webkit-scrollbar-thumb {
        background-color: rgba(156, 163, 175, 0.3);
    }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .smart-locked-report {
        /* 高分辨率屏幕下的边框优化 */
        border-width: 0.5px;
    }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
    .smart-locked-report {
        /* 深色模式下的背景优化 */
        backdrop-filter: blur(12px);
    }
}

/* 动画性能优化 */
.smart-locked-report * {
    /* 启用硬件加速 */
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 渐隐渐现动效 - 修复版本 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 固定定位专用动效 */
@keyframes fadeInFixed {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }

    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

/* 固定定位专用淡出动效 */
@keyframes fadeOutFixed {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
}

.animate-fade-in {
    animation: fadeIn 0.2s ease-out forwards;
}

.animate-fade-in-fixed {
    animation: fadeInFixed 0.2s ease-out forwards;
}

.animate-fade-out {
    animation: fadeOut 0.2s ease-in forwards;
}

.animate-fade-out-fixed {
    animation: fadeOutFixed 0.2s ease-in forwards;
}

/* 固定定位居中样式已移至JavaScript内联样式 */

/* 过渡动画优化 */
.smart-locked-report .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 焦点状态优化 */
.smart-locked-report button:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
    .smart-locked-report * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Stripe PaymentElement 响应式优化 */
.StripeElement {
    box-sizing: border-box;
    height: auto;
    min-height: 40px;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.StripeElement:focus {
    border-color: #10b981;
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.StripeElement--invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.StripeElement--webkit-autofill {
    background-color: #fefefe !important;
}

/* PaymentElement 移动端优化 */
@media (max-width: 640px) {

    /* 确保支付表单在小屏幕上有足够空间 */
    .StripeElement {
        padding: 14px 16px;
        font-size: 16px;
        /* 防止 iOS 缩放 */
        min-height: 44px;
        /* iOS 推荐的最小触摸目标 */
    }

    /* PaymentElement 容器优化 */
    .__PrivateStripeElement {
        min-height: 200px !important;
    }

    /* Tab 布局移动端优化 */
    .p-Tabs {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
    }

    .p-Tab {
        flex: 1 1 auto !important;
        min-width: 100px !important;
        padding: 12px 8px !important;
        font-size: 14px !important;
        text-align: center !important;
    }

    /* 输入字段移动端优化 */
    .p-Input {
        font-size: 16px !important;
        /* 防止 iOS 自动缩放 */
        padding: 14px 16px !important;
        line-height: 1.4 !important;
    }

    /* 地址字段优化 */
    .p-AddressElement .p-Input {
        margin-bottom: 12px !important;
    }

    /* 错误消息优化 */
    .p-Error {
        font-size: 14px !important;
        line-height: 1.4 !important;
        margin-top: 8px !important;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .StripeElement {
        padding: 16px;
        font-size: 16px;
        min-height: 48px;
    }

    .__PrivateStripeElement {
        min-height: 240px !important;
    }

    /* Tab 在极小屏幕上堆叠 */
    .p-Tabs {
        flex-direction: column !important;
    }

    .p-Tab {
        width: 100% !important;
        min-width: unset !important;
        margin-bottom: 8px !important;
    }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
    .StripeElement {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .StripeElement:focus {
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
    }

    .StripeElement--invalid {
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .StripeElement {
        border-width: 2px;
    }

    .StripeElement:focus {
        box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3);
    }
}

/* 键盘导航优化 */
.StripeElement:focus-visible {
    outline: 2px solid #10b981;
    outline-offset: 2px;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .p-Tab {
        min-height: 48px !important;
        padding: 16px 12px !important;
    }

    .StripeElement {
        min-height: 48px !important;
        padding: 16px !important;
    }
}

/* PaymentElement 特定优化 */
.payment-element-container {
    position: relative;
    min-height: 200px;
}

.payment-element-container .StripeElement {
    height: 100%;
}

/* 加载状态优化 */
.payment-element-container:has(.StripeElement--loading) {
    opacity: 0.7;
    pointer-events: none;
}

/* 动画优化 */
.payment-element-container .StripeElement {
    transition: all 0.2s ease-in-out;
}

.payment-element-container .StripeElement:not(.StripeElement--loading) {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 地区和邮编字段的特殊处理 */
.p-CountryDropdown,
.p-PostalCodeInput {
    width: 100% !important;
}

@media (max-width: 640px) {

    .p-CountryDropdown,
    .p-PostalCodeInput {
        margin-bottom: 16px !important;
    }

    /* 确保下拉菜单在移动端可用 */
    .p-CountryDropdown select {
        font-size: 16px !important;
        padding: 14px 16px !important;
        min-height: 44px !important;
    }
}

/* 防止iOS自动缩放的输入字段 */
@supports (-webkit-touch-callout: none) {

    .StripeElement input,
    .StripeElement select,
    .StripeElement textarea {
        font-size: 16px !important;
        transform: translateZ(0);
        -webkit-appearance: none;
    }
}

/* Chat Consultation 组件样式 */
.chat-consultation {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    z-index: 50;
}

.chat-consultation-button {
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border-radius: 50%;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.chat-consultation-button:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.4);
}

.chat-window {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-header {
    background: linear-gradient(135deg, #f8faff, #f1f5f9);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 1rem 1rem 0 0;
}

.chat-messages {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.message-bubble {
    border-radius: 1rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    max-width: 280px;
    word-wrap: break-word;
}

.message-assistant {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #e0f2fe;
}

.message-user {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
}

.suggested-question {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 0.75rem;
    text-align: left;
    width: 100%;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    color: #374151;
}

.suggested-question:hover {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border-color: #bfdbfe;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.chat-input {
    border: 1px solid #d1d5db;
    border-radius: 0.75rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.chat-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.chat-send-button {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border-radius: 0.75rem;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.chat-send-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: scale(1.05);
}

.chat-send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .chat-consultation {
        bottom: 1rem;
        right: 1rem;
    }

    .chat-consultation-button {
        width: 3rem;
        height: 3rem;
    }

    .chat-window-mobile {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100% !important;
        height: 100vh !important;
        border-radius: 0 !important;
        z-index: 50;
    }

    .chat-header-mobile {
        border-radius: 0 !important;
        padding: 1rem;
    }

    .chat-messages-mobile {
        max-height: none;
        flex: 1;
        overflow-y: auto;
    }
}

@media (max-width: 480px) {
    .chat-consultation {
        bottom: 0.75rem;
        right: 0.75rem;
    }

    .message-bubble {
        max-width: calc(100vw - 6rem);
    }

    .suggested-question {
        font-size: 0.8rem;
        padding: 0.6rem;
    }
}