"use client";

import { useState, useEffect } from 'react';
import { Lock, Share2, Facebook, Twitter, Linkedin, MessageCircle, Users, Eye, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useReferralStats } from '@/hooks/useReferralTracking';
import { generateShareUrl, extractReferralInfo } from '@/utils/referralUtils';

interface LockedReportProps {
  id: string;
  locale: string;
  type: 'adhd' | 'autism';
  onUnlock: () => void;
  translations: {
    reportLocked: string;
    reportLockedDescription: string;
    unlockReport: string;
    shareReport: string;
    shareToUnlock: string;
    shareReportDescription: string;
    payToUnlock: string;
    comingSoon: string;
    linkCopied?: string;
    shareUnlockDivider?: string;
    shareHelpText?: string;
    referralStats?: {
      title: string;
      clicks: string;
      completions: string;
      needsCompletions: string;
      pendingClicks: string;
      unlocked: string;
    };
  };
  children: React.ReactNode;
}

export default function LockedReport({
  id,
  locale,
  type,
  onUnlock,
  translations,
  children
}: LockedReportProps) {
  const [isUnlocking, setIsUnlocking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCopied, setIsCopied] = useState(false);
  const [referralCode, setReferralCode] = useState<string>('');

  // 获取推荐统计
  const { stats, loading: statsLoading, refetch } = useReferralStats(id);

  // 获取推荐码
  useEffect(() => {
    if (stats?.referralCode) {
      setReferralCode(stats.referralCode);
    }
  }, [stats]);

  // 生成分享的URL（带推荐码）
  const getShareUrl = () => {
    if (typeof window === 'undefined' || !referralCode) return '';
    return generateShareUrl(id, referralCode, locale, type);
  };

  // 处理系统分享
  const handleSystemShare = async () => {
    try {
      const shareUrl = getShareUrl();

      // 尝试使用Web Share API
      if (navigator.share) {
        await navigator.share({
          title: translations.reportLocked,
          text: translations.shareReportDescription,
          url: shareUrl,
        });
        // 分享成功后刷新统计（不再自动解锁）
        setTimeout(() => refetch(), 1000);
      } else {
        // 如果Web Share API不可用，复制到剪贴板
        await navigator.clipboard.writeText(shareUrl);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
        // 复制成功后刷新统计（不再自动解锁）
        setTimeout(() => refetch(), 1000);
      }
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  // 处理社交媒体分享
  const handleSocialShare = (platform: string) => {
    const shareUrl = getShareUrl();
    const text = encodeURIComponent(translations.shareReportDescription);
    const title = encodeURIComponent(translations.reportLocked);

    let shareLink = '';

    switch (platform) {
      case 'twitter':
        shareLink = `https://twitter.com/intent/tweet?text=${text}&url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'facebook':
        shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${text}`;
        break;
      case 'linkedin':
        shareLink = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'whatsapp':
        shareLink = `https://api.whatsapp.com/send?text=${text}%20${encodeURIComponent(shareUrl)}`;
        break;
      default:
        break;
    }

    if (shareLink) {
      window.open(shareLink, '_blank', 'width=600,height=500');
      // 分享成功后刷新统计（不再自动解锁）
      setTimeout(() => refetch(), 1000);
    }
  };

  // 处理解锁报告
  const handleUnlock = async () => {
    setIsUnlocking(true);
    setError(null);

    try {
      const response = await fetch('/api/unlock-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to unlock report');
      }

      // 解锁成功
      onUnlock();
    } catch (error) {
      console.error('解锁报告时出错:', error);
      setError(error instanceof Error ? error.message : '解锁报告时出错');
    } finally {
      setIsUnlocking(false);
    }
  };

  return (
    <div className="relative">
      {/* 轻量模糊处理的报告内容 - 让用户可以隐约看到内容结构 */}
      <div className="filter blur-sm pointer-events-none">
        {children}
      </div>

      {/* 轻量覆盖层 - 减少透明度让内容若隐若现 */}
      <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex flex-col items-center justify-center p-4 sm:p-6 text-center rounded-lg">
        <div className="w-full max-w-md space-y-6">
          {/* 锁定图标和标题 */}
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
              <Lock className="w-8 h-8 text-gray-500 dark:text-gray-400" />
            </div>

            <h2 className="text-xl sm:text-2xl font-bold mb-2 text-gray-900 dark:text-gray-100">
              {translations.reportLocked}
            </h2>

            <p className="text-gray-600 dark:text-gray-300 text-sm sm:text-base leading-relaxed">
              {translations.reportLockedDescription}
            </p>
          </div>

          {/* 推荐统计显示 */}
          {stats && !statsLoading && translations.referralStats && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold text-blue-800 dark:text-blue-200">
                  {translations.referralStats.title}
                </h3>
                {stats.isUnlocked && (
                  <div className="flex items-center text-green-600 dark:text-green-400">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    <span className="text-xs font-medium">{translations.referralStats.unlocked}</span>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex items-center justify-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-blue-100 dark:border-blue-700">
                  <Eye className="w-4 h-4 mr-2 text-blue-600 dark:text-blue-400" />
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">{stats.totalClicks}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{translations.referralStats.clicks}</div>
                  </div>
                </div>
                <div className="flex items-center justify-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-blue-100 dark:border-blue-700">
                  <Users className="w-4 h-4 mr-2 text-green-600 dark:text-green-400" />
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">{stats.totalCompletions}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{translations.referralStats.completions}</div>
                  </div>
                </div>
              </div>

              {stats.needsCompletions > 0 && (
                <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800 mb-3">
                  <p className="text-sm text-amber-700 dark:text-amber-300 font-medium">
                    {translations.referralStats.needsCompletions.replace('COUNT_PLACEHOLDER', stats.needsCompletions.toString())}
                  </p>
                </div>
              )}

              {stats.pendingClicks > 0 && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    {translations.referralStats.pendingClicks.replace('COUNT_PLACEHOLDER', stats.pendingClicks.toString())}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* 付费解锁按钮 */}
          <div>
            <button
              onClick={handleUnlock}
              disabled={isUnlocking}
              className="w-full px-6 py-3 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white rounded-lg font-medium flex items-center justify-center transition-colors disabled:cursor-not-allowed"
            >
              {isUnlocking ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                  {translations.comingSoon}
                </>
              ) : (
                <>
                  <Lock className="w-4 h-4 mr-2" />
                  {translations.payToUnlock}
                  <span className="ml-2 text-xs bg-emerald-500 px-2 py-1 rounded-full">
                    {translations.comingSoon}
                  </span>
                </>
              )}
            </button>
          </div>

          {/* 分享解锁分隔线 */}
          <div className="flex items-center">
            <div className="flex-grow h-px bg-gray-200 dark:bg-gray-700"></div>
            <span className="px-4 text-sm text-gray-500 dark:text-gray-400 font-medium">
              {translations.shareUnlockDivider || 'Or share to unlock'}
            </span>
            <div className="flex-grow h-px bg-gray-200 dark:bg-gray-700"></div>
          </div>

          {/* 系统分享按钮 */}
          <div>
            <button
              onClick={handleSystemShare}
              className="w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center justify-center transition-colors"
            >
              <Share2 className="w-4 h-4 mr-2" />
              {isCopied
                ? (translations.linkCopied || 'Link copied!')
                : translations.shareToUnlock}
            </button>
            {translations.shareHelpText && (
              <p className="mt-2 text-xs text-gray-500 dark:text-gray-400 leading-relaxed">
                {translations.shareHelpText}
              </p>
            )}
          </div>

          {/* 社交媒体分享按钮 */}
          <div className="grid grid-cols-4 gap-3">
            <button
              onClick={() => handleSocialShare('twitter')}
              className="p-3 bg-[#1DA1F2] hover:bg-[#0c85d0] text-white rounded-lg flex items-center justify-center transition-colors"
              aria-label="Share on Twitter"
            >
              <Twitter className="w-5 h-5" />
            </button>

            <button
              onClick={() => handleSocialShare('facebook')}
              className="p-3 bg-[#1877F2] hover:bg-[#0c60cf] text-white rounded-lg flex items-center justify-center transition-colors"
              aria-label="Share on Facebook"
            >
              <Facebook className="w-5 h-5" />
            </button>

            <button
              onClick={() => handleSocialShare('linkedin')}
              className="p-3 bg-[#0A66C2] hover:bg-[#084e96] text-white rounded-lg flex items-center justify-center transition-colors"
              aria-label="Share on LinkedIn"
            >
              <Linkedin className="w-5 h-5" />
            </button>

            <button
              onClick={() => handleSocialShare('whatsapp')}
              className="p-3 bg-[#25D366] hover:bg-[#1da84d] text-white rounded-lg flex items-center justify-center transition-colors"
              aria-label="Share on WhatsApp"
            >
              <MessageCircle className="w-5 h-5" />
            </button>
          </div>

          {/* 错误消息 */}
          {error && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-400">
                {error}
              </p>
            </div>
          )}

          {/* 底部链接 */}
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <Link
              href="/"
              className="underline hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Visit raadstest.com
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 