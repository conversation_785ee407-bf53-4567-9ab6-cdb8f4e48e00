import React, { useState, useEffect, useCallback, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import SmartGuidanceLockedReport from './SmartGuidanceLockedReport';
import UnifiedReportDisplay from '@/components/UnifiedReportDisplay';
import { useTranslations } from 'next-intl';
import ReportTemplate from '@/components/ReportTemplate';
import ErrorReportModal from '@/components/ErrorReportModal';
import ThreeDotsLoader from '@/components/ui/ThreeDotsLoader';

interface AdhdReportProps {
  testType: 'adult' | 'child';
  answers: Record<number, number>;
  scores: any;
  locale?: string;
  testId?: string;
  email?: string;
  userId?: string;
  regenerate?: boolean;
  onReset?: (resetFn: () => void) => void;
  onGenerationStateChange?: (isGenerating: boolean) => void;
}

// 全局初始化锁，避免多个实例同时初始化
const globalInitLock: Record<string, boolean> = {};

export default function AdhdReport({
  testType,
  answers,
  scores,
  locale = 'en',
  testId,
  email,
  userId,
  regenerate = false,
  onReset,
  onGenerationStateChange
}: AdhdReportProps) {
  // State management
  const [report, setReport] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isReportLocked, setIsReportLocked] = useState<boolean>(true);
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [justUnlocked, setJustUnlocked] = useState<boolean>(false);
  const isFetchingRef = useRef(false);
  const initialLoadCompletedRef = useRef(false); // 跟踪初始加载是否完成
  const mountedRef = useRef(true); // 跟踪组件是否挂载
  const [forceRefresh, setForceRefresh] = useState<boolean>(false); // 添加强制刷新状态

  // 获取翻译
  const t = useTranslations('pages.adhd.results');

  // 组件挂载和卸载处理
  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 检查是否有已保存的报告 - 已移除前端缓存，直接从API获取
  const fetchSavedReport = useCallback(async (preserveUnlockState = false) => {
    if (!testId) return { report: null, isReportLocked: true };

    // 如果已经有请求在进行中，等待它完成
    if (isFetchingRef.current) {
      console.log(`[AdhdReport] 请求已在进行中，跳过此次请求`);

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 500));
      return { report: null, isReportLocked: true };
    }

    // 设置请求锁
    isFetchingRef.current = true;
    console.log(`[AdhdReport] 开始从API获取报告: ${testId}-${locale}${forceRefresh ? ' (强制刷新)' : ''}`);

    try {
      // 直接从API获取报告，不再使用缓存
      const apiUrl = `/api/adhd/get-report?id=${testId}&locale=${locale}`;
      const response = await fetch(apiUrl);
      if (!response.ok) return { report: null, isReportLocked: true };

      try {
        const data = await response.json();

        // 更新锁定状态（除非要求保持解锁状态）
        if (data.isReportLocked !== undefined && !preserveUnlockState) {
          setIsReportLocked(data.isReportLocked);
        }

        if (data.report) {
          console.log(`[AdhdReport] 报告获取成功: ${testId}-${locale}`);
          return {
            report: data.report,
            isReportLocked: preserveUnlockState ? false : (data.isReportLocked !== undefined ? data.isReportLocked : true)
          };
        }
        console.log(`[AdhdReport] 数据库中没有保存的报告: ${testId}-${locale}`);
        return {
          report: null,
          isReportLocked: preserveUnlockState ? false : (data.isReportLocked !== undefined ? data.isReportLocked : true)
        };
      } catch (jsonError) {
        console.error('Error parsing saved report JSON:', jsonError);
        return { report: null, isReportLocked: true };
      }
    } catch (error) {
      console.error('Error fetching saved report:', error);
      return { report: null, isReportLocked: true };
    } finally {
      // 释放请求锁
      isFetchingRef.current = false;
      // 重置强制刷新状态
      setForceRefresh(false);
    }
  }, [testId, locale, forceRefresh]);

  // 保存报告到数据库 - 已移除前端缓存更新
  const saveReportToDatabase = useCallback(async (reportContent: string) => {
    if (!testId) return;

    setIsSaving(true);
    try {
      // 如果是重新生成模式，需要强制覆盖已有报告
      const endpoint = regenerate
        ? '/api/adhd/save-report?force=true'
        : '/api/adhd/save-report';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: testId,
          locale,
          report: reportContent
        })
      });

      if (!response.ok) {
        let errorMessage = 'Failed to save report';
        try {
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (jsonError) {
          // JSON解析失败，使用默认错误消息
          console.error('Error parsing JSON response:', jsonError);
        }
        console.error('Error saving report:', errorMessage);
      } else {
        console.log(`[AdhdReport] 报告${regenerate ? '强制覆盖' : '保存'}成功: ${testId}-${locale}`);
      }
    } catch (error) {
      console.error('Error saving report to database:', error);
    } finally {
      setIsSaving(false);
    }
  }, [testId, locale, regenerate]);

  // 生成新报告 - 已移除前端缓存清理
  const generateNewReport = useCallback(async () => {
    console.log('[AdhdReport] 开始生成新报告，强制重新生成模式:', regenerate);

    // 构建请求数据
    const requestData = {
      testType,
      answers,
      scores,
      locale,
      testId
    };

    try {
      // 调用API生成报告，添加强制重新生成参数
      const forceParam = regenerate ? '?force=true' : '';
      const endpoint = `/api/adhd/report${forceParam}`;

      console.log(`[AdhdReport] 调用API: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': locale,
          'Cache-Control': regenerate ? 'no-cache, no-store, must-revalidate' : 'default'
        },
        body: JSON.stringify(requestData),
        // 强制不使用缓存
        cache: regenerate ? 'no-store' : 'default'
      });

      // 检查错误
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate report');
      }

      // 获取报告内容
      const data = await response.json();
      if (!data.report) {
        throw new Error('No report content received');
      }

      console.log(`[AdhdReport] 新报告生成成功，内容长度: ${data.report.length}字符`);

      // 设置报告内容
      setReport(data.report);

      // 如果有testId，保存报告到数据库
      if (testId) {
        const saveEndpoint = regenerate
          ? '/api/adhd/save-report?force=true'
          : '/api/adhd/save-report';

        console.log(`[AdhdReport] 保存报告到数据库: ${saveEndpoint}`);
        await saveReportToDatabase(data.report);
      }

      return data.report;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }, [testType, answers, scores, locale, testId, regenerate, saveReportToDatabase]);

  // 首次加载逻辑：优先从数据库加载，如果没有则自动生成
  const initializeReport = useCallback(async () => {
    if (!mountedRef.current) return;

    // 生成锁键
    const lockKey = `${testId}-${locale}`;

    // 防止重复初始化 - 但允许强制刷新
    if (!forceRefresh && initialLoadCompletedRef.current) {
      console.log('[AdhdReport] 跳过初始化：', {
        forceRefresh,
        initialLoadCompleted: initialLoadCompletedRef.current
      });
      return;
    }

    // 如果已经有其他实例在初始化，强制等待完成
    if (globalInitLock[lockKey]) {
      console.log(`[AdhdReport] 另一个实例正在初始化，等待完成: ${lockKey}`);

      // 等待锁释放，最多等待3秒
      for (let i = 0; i < 10; i++) {
        await new Promise(resolve => setTimeout(resolve, 300));

        if (!mountedRef.current) return; // 如果组件已卸载，退出

        if (!globalInitLock[lockKey]) {
          console.log(`[AdhdReport] 锁已释放，跳过重复初始化: ${lockKey}`);
          return; // 锁已释放，跳过初始化
        }
      }

      // 如果等待超时，强制释放锁并继续
      console.log(`[AdhdReport] 等待超时，强制释放锁: ${lockKey}`);
      globalInitLock[lockKey] = false;
    }

    // 设置初始化锁
    globalInitLock[lockKey] = true;

    console.log('[AdhdReport] 初始化报告流程开始');
    setLoading(true);

    if (onGenerationStateChange) {
      onGenerationStateChange(true);
    }

    try {
      // 强制刷新模式下不再需要清除缓存，因为已移除前端缓存
      if (forceRefresh && testId) {
        console.log(`[AdhdReport] 🔄 强制刷新模式: ${testId}-${locale}`);
      }

      // 首先尝试从数据库获取报告
      if (testId) {
        const { report: savedReport, isReportLocked: locked } = await fetchSavedReport();

        if (!mountedRef.current) return; // 如果组件已卸载，退出

        // 更新锁定状态
        setIsReportLocked(locked);

        if (savedReport) {
          // 数据库中存在报告，显示它
          console.log('[AdhdReport] 发现已保存的报告，直接显示');
          setReport(savedReport);
          initialLoadCompletedRef.current = true;
          setLoading(false);
          // 重置强制刷新状态
          if (forceRefresh) {
            setForceRefresh(false);
          }
          if (onGenerationStateChange) {
            onGenerationStateChange(false);
          }
          return;
        }

        if (!mountedRef.current) return; // 如果组件已卸载，退出

        // 数据库中没有保存的报告
        console.log('[AdhdReport] 数据库中没有保存的报告');

        // 如果报告已解锁，则生成新报告；如果锁定，则显示模板
        if (!locked) {
          console.log('[AdhdReport] 🔓 报告已解锁，自动触发生成流程');
          await generateNewReport();
        } else {
          console.log('[AdhdReport] 🔒 报告被锁定，显示模板而不生成报告');
          // 不生成报告，只是设置loading为false，让组件显示锁定模板
        }
      }
    } catch (error) {
      console.error('Error in report initialization:', error);
      if (mountedRef.current) {
        setError(error instanceof Error ? error.message : 'Failed to initialize report');
      }
    } finally {
      // 释放全局初始化锁
      globalInitLock[lockKey] = false;

      if (mountedRef.current) {
        setLoading(false);
        // 重置强制刷新状态
        if (forceRefresh) {
          setForceRefresh(false);
        }
        if (onGenerationStateChange) {
          onGenerationStateChange(false);
        }
        initialLoadCompletedRef.current = true;
      }
    }
  }, [testId, locale, fetchSavedReport, onGenerationStateChange, generateNewReport, forceRefresh]);

  // 获取报告
  const fetchReport = useCallback(async () => {
    // 清除错误状态
    setError(null);

    // 如果已经有请求在进行中，终止此次请求
    if (isFetchingRef.current && !regenerate) { // regenerate 模式会强制执行，不受锁限制
      console.log('[AdhdReport] 已有请求正在进行，跳过');
      return;
    }

    // 设置请求锁
    isFetchingRef.current = true;

    // 记录请求开始，详细模式
    console.log(`[AdhdReport] 开始${regenerate ? '强制' : ''}获取报告: testId=${testId}, locale=${locale}, regenerate=${regenerate}`);

    // 强制重新生成模式下不再需要清除缓存，因为已移除前端缓存
    if (regenerate && testId) {
      console.log(`[AdhdReport] 🔄 强制重新生成模式: ${testId}-${locale}`);
    }

    // 设置加载状态
    setLoading(true);

    // 通知父组件状态变化
    if (onGenerationStateChange) {
      onGenerationStateChange(true);
    }

    try {
      // 如果是强制重新生成，直接调用API不考虑缓存
      if (regenerate) {
        console.log('[AdhdReport] 强制重新生成模式：跳过缓存和数据库检查，直接调用API');
        await generateNewReport(); // 直接调用生成函数
      } else {
        // 非强制重新生成，直接从API获取保存的报告
        const { report: savedReport, isReportLocked: locked } = await fetchSavedReport();

        // 更新锁定状态
        setIsReportLocked(locked);

        if (savedReport) {
          setReport(savedReport);
        } else {
          // 如果数据库中没有报告，检查是否解锁再决定是否生成新报告
          if (!locked) {
            console.log('[AdhdReport] 🔓 报告已解锁，生成新报告');
            await generateNewReport();
          } else {
            console.log('[AdhdReport] 🔒 报告被锁定，显示模板而不生成报告');
            // 不生成报告，让组件显示锁定模板
          }
        }
      }
    } catch (error) {
      console.error('Error fetching ADHD report:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
      if (onGenerationStateChange) {
        onGenerationStateChange(false);
      }
      // 释放请求锁
      isFetchingRef.current = false;
    }
  }, [locale, testId, regenerate, fetchSavedReport, generateNewReport, onGenerationStateChange]);

  // 解锁报告处理函数
  const handleUnlock = async () => {
    console.log('[AdhdReport] 🔓 开始解锁处理流程');

    // 立即更新本地状态以提供即时反馈
    setIsReportLocked(false);
    setJustUnlocked(true);
    setLoading(true);
    setError(null);
    console.log('[AdhdReport] ✅ 报告状态已设置为解锁，开始加载报告');

    // 重新获取报告状态和内容
    if (testId) {
      try {
        // 等待一小段时间，让数据库更新和缓存清除完成
        await new Promise(resolve => setTimeout(resolve, 200));

        console.log('[AdhdReport] 📊 解锁后获取报告（保持解锁状态）');

        // 首先尝试从数据库获取已保存的报告，但不覆盖解锁状态
        const savedReport = await fetchSavedReport(true); // preserveUnlockState = true

        if (savedReport.report) {
          console.log('[AdhdReport] ✅ 找到已保存的报告，启用流式显示');
          setIsStreaming(true);
          setReport(savedReport.report);
          // 不要覆盖解锁状态，保持 isReportLocked = false
        } else {
          console.log('[AdhdReport] 🚀 未找到保存的报告，开始生成新报告');
          setIsStreaming(true);
          // 如果没有保存的报告，立即生成新的
          await generateNewReport();
        }
      } catch (error) {
        console.error('[AdhdReport] ❌ 解锁后获取/生成报告失败:', error);
        setError(locale === 'zh' ? '获取报告失败，请重试' : 'Failed to fetch report, please try again');
        // 如果出错，恢复锁定状态
        setIsReportLocked(true);
        setJustUnlocked(false);
      } finally {
        setLoading(false);
      }
    }
  };





  // 防抖请求函数，避免短时间内多次调用
  const debouncedFetchReport = useCallback(() => {
    const fetchReportWithDebounce = () => {
      fetchReport();
    };

    // 清除任何已排队的fetchReport调用
    if ((debouncedFetchReport as any).timeoutId) {
      clearTimeout((debouncedFetchReport as any).timeoutId);
    }

    // 设置一个短暂的超时以避免连续调用
    (debouncedFetchReport as any).timeoutId = setTimeout(fetchReportWithDebounce, 100);
  }, [fetchReport]);

  // 初始化逻辑：组件加载后自动尝试获取报告
  useEffect(() => {
    // 在组件首次加载时，强制刷新状态以确保获取最新的解锁状态
    if (!initialLoadCompletedRef.current) {
      console.log('[AdhdReport] 🚀 首次加载，强制刷新报告状态');
      setForceRefresh(true);
      // 不要在这里设置 initialLoadCompletedRef.current = true
      // 让 initializeReport 函数在完成后设置
    }
  }, []);

  // 统一的初始化逻辑 - 避免多个useEffect竞态
  useEffect(() => {
    if (forceRefresh) {
      console.log('[AdhdReport] 🔄 强制刷新状态已设置，重新初始化报告');
      // 重置完成标志，允许强制刷新执行
      initialLoadCompletedRef.current = false;
      initializeReport();
    } else {
      // 正常初始化逻辑
      initializeReport();
    }
  }, [initializeReport, forceRefresh]);

  // 重置状态
  useEffect(() => {
    if (onReset) {
      // 重置组件内部状态的函数
      const handleReset = () => {
        // 清空报告内容
        setReport('');
        // 重置加载状态
        setLoading(true);
        // 清除错误
        setError(null);
        // 重置锁状态
        setIsReportLocked(true);

        // 尝试重新获取
        initializeReport();
      };

      // 将重置函数传递给父组件
      onReset(handleReset);
    }
  }, [onReset, initializeReport]);

  // 加载状态显示
  if (loading) {
    return (
      <ThreeDotsLoader text={locale === 'zh' ? '正在生成您的ADHD测试报告...' : 'Generating your ADHD test report...'} />
    );
  }

  // 错误状态显示
  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
        <div className="p-4 mb-4 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"></path>
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
                {locale === 'zh' ? '生成报告出错' : 'Error generating report'}
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-center mt-4">
          <button
            onClick={debouncedFetchReport}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-md"
          >
            {locale === 'zh' ? '重试' : 'Retry'}
          </button>
        </div>
      </div>
    );
  }

  // Use unified report display for both locked and unlocked states
  if (testId) {
    return (
      <UnifiedReportDisplay
        testId={testId}
        testType={testType === 'adult' ? 'adhdAdultTest' : 'adhdChildTest'}
        answers={answers}
        scores={scores}
        report={report || ''}
        isReportLocked={isReportLocked}
        isStreaming={isStreaming}
        justUnlocked={justUnlocked}
        onStreamingComplete={() => {
          setIsStreaming(false);
          setJustUnlocked(false);
        }}
        onUnlock={handleUnlock}
        translations={{
          reportLocked: t('reportLocked'),
          reportLockedDescription: t('reportLockedDescription'),
          unlockReport: t('unlockReport'),
          shareReport: t('shareReport'),
          shareToUnlock: t('shareToUnlock'),
          shareReportDescription: t('shareReportDescription'),
          payToUnlock: t('payToUnlock'),
          comingSoon: t('comingSoon'),
          linkCopied: t('linkCopied'),
          shareUnlockDivider: t('shareUnlockDivider'),
          shareHelpText: t('shareHelpText'),
          payToUnlockTab: t('payToUnlockTab'),
          shareToUnlockTab: t('shareToUnlockTab'),
          shareToUnlockFull: t('shareToUnlockFull'),
          free: t('free'),
          share: t('share'),
          unlockReportButton: t('unlockReportButton'),
          viewCompleteAnalysis: t('viewCompleteAnalysis'),
          payment: {
            title: t('payment.title'),
            description: t('payment.description'),
            cardDetails: t('payment.cardDetails'),
            payButton: t('payment.payButton'),
            processing: t('payment.processing'),
            cancel: t('payment.cancel'),
            success: t('payment.success'),
            error: t('payment.error')
          },
          subscription: {
            title: t('subscription.title'),
            billingPeriod: {
              monthly: t('subscription.billingPeriod.monthly'),
              yearly: t('subscription.billingPeriod.yearly'),
              save: t('subscription.billingPeriod.save'),
              saveAmount: locale === 'zh' ? '省{amount}%' : 'SAVE {amount}%'
            },
            period: {
              month: t('subscription.period.month'),
              year: t('subscription.period.year')
            },
            singleReport: {
              oneTime: t('subscription.singleReport.oneTime'),
              unlockButton: locale === 'zh' ? '解锁 ${price}' : 'Unlock ${price}'
            },
            plans: {
              selectPro: t('subscription.plans.selectPro'),
              selectPremium: t('subscription.plans.selectPremium'),
              mostPopular: t('subscription.plans.mostPopular'),
              savePerYear: locale === 'zh' ? '每年省$${amount}' : 'Save $${amount}/year'
            }
          },
          referralStats: {
            title: t('referralStats.title'),
            clicks: t('referralStats.clicks'),
            completions: t('referralStats.completions'),
            needsCompletions: t('referralStats.needsCompletions'),
            pendingClicks: t('referralStats.pendingClicks'),
            unlocked: t('referralStats.unlocked')
          }
        }}
        email={email}
        userId={userId}
        locale={locale}
        reportType="adhd"
      />
    );
  }

  // Fallback for no test ID
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
      <div className="text-center p-8">
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          {locale === 'zh' ? '无法加载报告' : 'Unable to load report'}
        </p>
      </div>
    </div>
  );
} 