import React, { useState, useEffect, useCallback, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import SmartGuidanceLockedReport from './SmartGuidanceLockedReport';
import UnifiedReportDisplay from '@/components/UnifiedReportDisplay';
import StreamingReportGenerator from '@/components/StreamingReportGenerator';
import { useTranslations } from 'next-intl';
import ReportTemplate from '@/components/ReportTemplate';
import ErrorReportModal from '@/components/ErrorReportModal';
import ThreeDotsLoader from '@/components/ui/ThreeDotsLoader';

interface AutismReportProps {
  testType: 'raadsr' | 'aq10';
  answers: Record<number, number>;
  scores: any;
  score?: any; // 添加可选的 score 属性，用于 AQ-10 测试
  locale?: string;
  testId?: string;
  email?: string; // 用户邮箱，用于支付
  userId?: string; // 用户ID，可选
  regenerate?: boolean;
  onReset?: (resetFn: () => void) => void;
  onGenerationStateChange?: (isGenerating: boolean) => void;
}

// 全局初始化锁，避免多个实例同时初始化
const globalInitLock: Record<string, boolean> = {};

export default function AutismReport({
  testType,
  answers,
  scores,
  score,
  locale = 'en',
  testId,
  email,
  userId,
  regenerate = false,
  onReset,
  onGenerationStateChange
}: AutismReportProps) {
  // 调试日志：检查传入的参数
  console.log('[AutismReport] 组件参数:', {
    testType,
    hasAnswers: !!answers,
    hasScores: !!scores,
    hasScore: !!score,
    scoresValue: scores,
    scoreValue: score,
    locale,
    testId,
    hasEmail: !!email,
    hasUserId: !!userId,
    regenerate
  });

  // State management
  const [report, setReport] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isReportLocked, setIsReportLocked] = useState<boolean>(true);
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [useStreaming, setUseStreaming] = useState<boolean>(true); // Enable streaming by default
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [justUnlocked, setJustUnlocked] = useState<boolean>(false);
  const isFetchingRef = useRef(false);
  const initialLoadCompletedRef = useRef(false); // 跟踪初始加载是否完成
  const mountedRef = useRef(true); // 跟踪组件是否挂载
  const [forceRefresh, setForceRefresh] = useState<boolean>(false); // 添加强制刷新状态

  // 获取翻译
  const t = useTranslations('pages.autism.result');

  // 组件挂载和卸载处理
  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 检查是否有已保存的报告 - 已移除前端缓存，直接从API获取
  const fetchSavedReport = useCallback(async () => {
    console.log('[AutismReport] 🔧 fetchSavedReport 函数被调用', { testId, locale, forceRefresh });
    if (!testId) {
      console.log('[AutismReport] ❌ testId 为空，返回默认值');
      return { report: null, isReportLocked: true };
    }

    // 如果已经有请求在进行中，等待它完成
    console.log('[AutismReport] 检查请求锁:', { isFetching: isFetchingRef.current });
    if (isFetchingRef.current) {
      console.log(`[AutismReport] ⏳ 请求已在进行中，跳过此次请求`);

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log(`[AutismReport] ⏳ 等待超时，返回默认值`);
      return { report: null, isReportLocked: true };
    }

    // 设置请求锁
    isFetchingRef.current = true;
    console.log(`[AutismReport] 🔍 开始从API获取报告: ${testId}-${locale}${forceRefresh ? ' (强制刷新)' : ''}`, {
      testId,
      locale,
      forceRefresh
    });

    try {
      // 直接从API获取报告，不再使用缓存
      const apiUrl = `/api/autism/get-report?id=${testId}&locale=${locale}`;
      console.log(`[AutismReport] 📡 调用API: ${apiUrl}`);

      const response = await fetch(apiUrl);
      console.log(`[AutismReport] 📡 API响应状态: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.log(`[AutismReport] ❌ API请求失败: ${response.status} ${response.statusText}`);
        return { report: null, isReportLocked: true };
      }

      try {
        console.log(`[AutismReport] 📊 开始解析JSON响应...`);
        const data = await response.json();
        console.log(`[AutismReport] 📊 API响应数据:`, {
          hasReport: !!data.report,
          reportLength: data.report ? data.report.length : 0,
          isReportLocked: data.isReportLocked,
          dataKeys: Object.keys(data)
        });

        // 更新锁定状态
        if (data.isReportLocked !== undefined) {
          setIsReportLocked(data.isReportLocked);
        }

        if (data.report) {
          console.log(`[AutismReport] ✅ 报告获取成功: ${testId}-${locale}, 长度: ${data.report.length}字符`);
          return {
            report: data.report,
            isReportLocked: data.isReportLocked !== undefined ? data.isReportLocked : true
          };
        }
        console.log(`[AutismReport] 📭 数据库中没有保存的报告: ${testId}-${locale}`);
        return {
          report: null,
          isReportLocked: data.isReportLocked !== undefined ? data.isReportLocked : true
        };
      } catch (jsonError) {
        console.error('[AutismReport] ❌ JSON解析错误:', jsonError);
        return { report: null, isReportLocked: true };
      }
    } catch (error) {
      console.error('[AutismReport] ❌ 网络请求错误:', error);
      return { report: null, isReportLocked: true };
    } finally {
      console.log(`[AutismReport] 🔧 fetchSavedReport finally 块执行`);
      // 释放请求锁
      isFetchingRef.current = false;
      // 重置强制刷新状态
      setForceRefresh(false);
    }
  }, [testId, locale, forceRefresh]);

  // 保存报告到数据库 - 已移除前端缓存更新
  const saveReportToDatabase = useCallback(async (reportContent: string) => {
    if (!testId) return;

    setIsSaving(true);
    try {
      // 如果是重新生成模式，需要强制覆盖已有报告
      const endpoint = regenerate
        ? '/api/autism/save-report?force=true'
        : '/api/autism/save-report';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: testId,
          locale,
          report: reportContent
        })
      });

      if (!response.ok) {
        let errorMessage = 'Failed to save report';
        try {
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (jsonError) {
          // JSON解析失败，使用默认错误消息
          console.error('Error parsing JSON response:', jsonError);
        }
        console.error('Error saving report:', errorMessage);
      } else {
        console.log(`[AutismReport] 报告${regenerate ? '强制覆盖' : '保存'}成功: ${testId}-${locale}`);
      }
    } catch (error) {
      console.error('Error saving report to database:', error);
    } finally {
      setIsSaving(false);
    }
  }, [testId, locale, regenerate]);

  // 生成新报告 - 已移除前端缓存清理
  const generateNewReport = useCallback(async () => {
    console.log('[AutismReport] 🚀 开始生成新报告流程，强制重新生成模式:', regenerate);

    // 非强制模式下，再次检查数据库是否已有报告（防止并发请求）
    if (!regenerate && testId) {
      console.log('[AutismReport] 🔍 非强制模式：再次检查数据库是否已有报告...');
      const { report: existingReport } = await fetchSavedReport();
      if (existingReport) {
        console.log('[AutismReport] ✅ 发现已有报告，跳过生成流程');
        setReport(existingReport);
        return existingReport;
      }
      console.log('[AutismReport] 📭 确认数据库中无报告，继续生成');
    }

    // 构建请求数据
    // 根据测试类型和数据可用性构建请求
    let requestData: any;

    // 将组件属性中的 testType 映射到 API 使用的类型
    const apiTestType = testType === 'raadsr' ? 'raadsrTest' : 'aq10Test';

    if (testType === 'raadsr') {
      requestData = {
        type: apiTestType,
        answers,
        score: scores,
        locale,
        testId
      };
    } else if (testType === 'aq10') {
      // For AQ-10, try to get score from multiple possible sources
      const aq10Score = score || (scores?.total) || scores;
      console.log('[AutismReport] 🔍 AQ-10分数来源:', {
        score,
        scoresTotal: scores?.total,
        scores,
        finalScore: aq10Score
      });

      requestData = {
        type: apiTestType,
        answers,
        score: aq10Score,
        locale,
        testId
      };
    }

    console.log('[AutismReport] 📤 准备发送报告生成请求:', {
      testType: apiTestType,
      answersCount: Object.keys(answers || {}).length,
      hasScores: !!scores,
      hasScore: !!score,
      locale,
      testId
    });

    console.log('[AutismReport] 📤 请求数据详情:', JSON.stringify(requestData, null, 2));

    try {
      if (useStreaming) {
        // 使用流式生成 - 这将由StreamingReportGenerator组件处理
        console.log('[AutismReport] 使用流式生成模式');
        return null; // 流式模式下不直接返回内容
      } else {
        // 传统模式
        const forceParam = regenerate ? '?force=true' : '';
        const endpoint = `/api/autism/report${forceParam}`;

        console.log(`[AutismReport] 📡 调用传统API: ${endpoint}`);

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept-Language': locale,
            'Cache-Control': regenerate ? 'no-cache, no-store, must-revalidate' : 'default'
          },
          body: JSON.stringify(requestData),
          cache: regenerate ? 'no-store' : 'default'
        });

        console.log(`[AutismReport] 📡 API响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error('[AutismReport] ❌ API返回错误:', errorData);
          throw new Error(errorData.error || 'Failed to generate report');
        }

        const data = await response.json();
        if (!data.report) {
          console.error('[AutismReport] ❌ API返回空报告');
          throw new Error('No report content received');
        }

        console.log(`[AutismReport] ✅ 新报告生成成功，内容长度: ${data.report.length}字符`);

        // 设置报告内容
        setReport(data.report);

        // 如果有testId，保存报告到数据库
        if (testId) {
          console.log(`[AutismReport] 💾 开始保存报告到数据库...`);
          await saveReportToDatabase(data.report);
        }

        return data.report;
      }
    } catch (error) {
      console.error('[AutismReport] ❌ 生成报告出错:', error);
      throw error;
    }
  }, [testType, answers, scores, score, locale, testId, regenerate, useStreaming, saveReportToDatabase, fetchSavedReport]);

  // 处理流式生成完成
  const handleStreamingComplete = useCallback(async (content: string) => {
    console.log(`[AutismReport] 流式生成完成，内容长度: ${content.length}字符`);
    setReport(content);
    setLoading(false);

    // 如果有testId，保存报告到数据库
    if (testId) {
      try {
        await saveReportToDatabase(content);
      } catch (error) {
        console.error('Error saving streamed report:', error);
      }
    }
  }, [testId, saveReportToDatabase]);

  // 处理流式生成错误
  const handleStreamingError = useCallback((errorMessage: string) => {
    console.error('[AutismReport] 流式生成错误:', errorMessage);
    setError(errorMessage);
    setLoading(false);
  }, []);

  // 初始化报告（组件首次加载时调用）
  const initializeReport = useCallback(async () => {
    console.log('[AutismReport] 🔧 initializeReport 函数被调用', {
      forceRefresh,
      initialLoadCompleted: initialLoadCompletedRef.current,
      hasTestId: !!testId,
      mounted: mountedRef.current
    });

    // 防止重复初始化 - 但允许强制刷新
    if ((!forceRefresh && initialLoadCompletedRef.current) || !testId) {
      console.log('[AutismReport] 跳过初始化：', {
        forceRefresh,
        initialLoadCompleted: initialLoadCompletedRef.current,
        hasTestId: !!testId
      });
      return;
    }

    // 全局初始化锁，防止多个组件实例同时初始化
    const lockKey = `${testId}-${locale}`;
    console.log('[AutismReport] 检查全局初始化锁:', { lockKey, isLocked: globalInitLock[lockKey] });
    if (globalInitLock[lockKey]) {
      console.log('[AutismReport] 其他实例正在初始化，跳过此次初始化');
      return;
    }

    globalInitLock[lockKey] = true;
    console.log('[AutismReport] 初始化报告流程开始', {
      testId,
      locale,
      forceRefresh,
      hasTestId: !!testId
    });
    setLoading(true);

    if (onGenerationStateChange) {
      onGenerationStateChange(true);
    }

    try {
      // 强制刷新模式下不再需要清除缓存，因为已移除前端缓存
      if (forceRefresh && testId) {
        console.log(`[AutismReport] 🔄 强制刷新模式: ${testId}-${locale}`);
      }

      // 首先尝试从数据库获取报告
      if (testId) {
        console.log('[AutismReport] 检查数据库中是否存在报告...', { testId, locale, forceRefresh });
        const { report: savedReport, isReportLocked: locked } = await fetchSavedReport();
        console.log('[AutismReport] fetchSavedReport 结果:', {
          hasReport: !!savedReport,
          isReportLocked: locked,
          reportLength: savedReport ? savedReport.length : 0
        });

        if (!mountedRef.current) return; // 如果组件已卸载，退出

        // 更新锁定状态
        setIsReportLocked(locked);

        if (savedReport) {
          // 数据库中存在报告，显示它
          console.log('[AutismReport] ✅ 发现已保存的报告，直接显示（避免重新生成）');
          setReport(savedReport);
          initialLoadCompletedRef.current = true;
          setLoading(false);
          if (onGenerationStateChange) {
            onGenerationStateChange(false);
          }
          return;
        }

        if (!mountedRef.current) return; // 如果组件已卸载，退出

        // 数据库中没有保存的报告
        console.log('[AutismReport] 📝 数据库中没有保存的报告');

        // 如果报告已解锁，则生成新报告；如果锁定，则显示模板
        if (!locked) {
          console.log('[AutismReport] 🔓 报告已解锁，自动触发生成流程');
          await generateNewReport();
        } else {
          console.log('[AutismReport] 🔒 报告被锁定，显示模板而不生成报告');
          // 不生成报告，只是设置loading为false，让组件显示锁定模板
        }
      }
    } catch (error) {
      console.error('Error in report initialization:', error);
      if (mountedRef.current) {
        setError(error instanceof Error ? error.message : 'Failed to initialize report');
      }
    } finally {
      console.log(`[AutismReport] 🔧 initializeReport finally 块执行`, {
        mounted: mountedRef.current,
        lockKey
      });
      // 释放全局初始化锁
      globalInitLock[lockKey] = false;

      if (mountedRef.current) {
        setLoading(false);
        if (onGenerationStateChange) {
          onGenerationStateChange(false);
        }
        // 设置完成标志
        initialLoadCompletedRef.current = true;
        console.log(`[AutismReport] ✅ 初始化完成，设置完成标志`);
      }
    }
  }, [testId, locale, fetchSavedReport, onGenerationStateChange, generateNewReport, forceRefresh]);

  // 获取报告
  const fetchReport = useCallback(async () => {
    // 清除错误状态
    setError(null);

    // 如果已经有请求在进行中，终止此次请求
    if (isFetchingRef.current && !regenerate) { // regenerate 模式会强制执行，不受锁限制
      console.log('[AutismReport] 已有请求正在进行，跳过');
      return;
    }

    // 设置请求锁
    isFetchingRef.current = true;

    // 记录请求开始，详细模式
    console.log(`[AutismReport] 开始${regenerate ? '强制' : ''}获取报告: testId=${testId}, locale=${locale}, regenerate=${regenerate}`);

    // 强制重新生成模式下不再需要清除缓存，因为已移除前端缓存
    if (regenerate && testId) {
      console.log(`[AutismReport] 🔄 强制重新生成模式: ${testId}-${locale}`);
    }

    // 设置加载状态
    setLoading(true);

    // 通知父组件状态变化
    if (onGenerationStateChange) {
      onGenerationStateChange(true);
    }

    try {
      // 如果是强制重新生成，直接调用API不考虑缓存
      if (regenerate) {
        console.log('[AutismReport] 强制重新生成模式：跳过缓存和数据库检查，直接调用API');
        await generateNewReport(); // 直接调用生成函数
      } else {
        // 非强制重新生成，直接从API获取保存的报告
        const { report: savedReport, isReportLocked: locked } = await fetchSavedReport();

        // 更新锁定状态
        setIsReportLocked(locked);

        if (savedReport) {
          setReport(savedReport);
        } else {
          // 如果数据库中没有报告，检查是否解锁再决定是否生成新报告
          if (!locked) {
            console.log('[AutismReport] 🔓 报告已解锁，生成新报告');
            await generateNewReport();
          } else {
            console.log('[AutismReport] 🔒 报告被锁定，显示模板而不生成报告');
            // 不生成报告，让组件显示锁定模板
          }
        }
      }
    } catch (error) {
      console.error('Error fetching Autism report:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
      if (onGenerationStateChange) {
        onGenerationStateChange(false);
      }
      // 释放请求锁
      isFetchingRef.current = false;
    }
  }, [locale, testId, regenerate, fetchSavedReport, generateNewReport, onGenerationStateChange]);

  // 防抖请求函数，避免短时间内多次调用
  const debouncedFetchReport = useCallback(() => {
    const fetchReportWithDebounce = () => {
      fetchReport();
    };

    fetchReportWithDebounce();
  }, [fetchReport]);

  // 初始化逻辑：组件加载后自动尝试获取报告
  useEffect(() => {
    console.log('[AutismReport] 🔧 初始化 useEffect 执行', {
      initialLoadCompleted: initialLoadCompletedRef.current,
      testId
    });
    // 在组件首次加载时，强制刷新状态以确保获取最新的解锁状态
    if (!initialLoadCompletedRef.current) {
      console.log('[AutismReport] 🚀 首次加载，强制刷新报告状态');
      setForceRefresh(true);
      // 不要在这里设置 initialLoadCompletedRef.current = true
      // 让 initializeReport 函数在完成后设置
    }
  }, [testId]);

  // 当 forceRefresh 状态变化时，重新初始化
  useEffect(() => {
    console.log('[AutismReport] 🔄 forceRefresh useEffect 执行', { forceRefresh });
    if (forceRefresh) {
      console.log('[AutismReport] 🔄 强制刷新状态已设置，重新初始化报告');
      // 重置完成标志，允许强制刷新执行
      initialLoadCompletedRef.current = false;
      initializeReport();
    }
  }, [forceRefresh, initializeReport]);

  // 正常初始化逻辑
  useEffect(() => {
    console.log('[AutismReport] 🔧 正常初始化 useEffect 执行', { forceRefresh });
    if (!forceRefresh) {
      initializeReport();
    }
  }, [initializeReport, forceRefresh]);

  // 重试生成报告
  const handleRetry = () => {
    setError(null);
    debouncedFetchReport();
  };

  // 解锁报告处理函数
  const handleUnlock = async () => {
    console.log('[AutismReport] 🔓 开始解锁处理流程');

    // 立即更新本地状态以提供即时反馈
    setIsReportLocked(false);
    setJustUnlocked(true);
    setLoading(true);
    setError(null);
    console.log('[AutismReport] ✅ 报告状态已设置为解锁，开始加载报告');

    // 重新获取报告状态和内容
    if (testId) {
      try {
        // 等待一小段时间，让数据库更新和缓存清除完成
        await new Promise(resolve => setTimeout(resolve, 200));

        console.log('[AutismReport] 📊 解锁后获取报告（保持解锁状态）');

        // 首先尝试从数据库获取已保存的报告，但不覆盖解锁状态
        const savedReport = await fetchSavedReport();

        if (savedReport.report) {
          console.log('[AutismReport] ✅ 找到已保存的报告，启用流式显示');
          setIsStreaming(true);
          setReport(savedReport.report);
          // 不要覆盖解锁状态，保持 isReportLocked = false
        } else {
          console.log('[AutismReport] 🚀 未找到保存的报告，开始生成新报告');
          setIsStreaming(true);
          // 如果没有保存的报告，立即生成新的
          await generateNewReport();
        }
      } catch (error) {
        console.error('[AutismReport] ❌ 解锁后获取/生成报告失败:', error);
        setError(locale === 'zh' ? '获取报告失败，请重试' : 'Failed to fetch report, please try again');
        // 如果出错，恢复锁定状态
        setIsReportLocked(true);
        setJustUnlocked(false);
      } finally {
        setLoading(false);
      }
    }
  };

  // 加载状态显示
  console.log('[AutismReport] 🔧 渲染检查:', { loading, error, isReportLocked, hasReport: !!report, testId });
  if (loading) {
    console.log('[AutismReport] 🔄 显示加载状态');
    return (
      <ThreeDotsLoader text={locale === 'zh' ? '正在生成您的自闭症测试报告...' : 'Generating your autism test report...'} />
    );
  }

  // 错误状态显示
  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
        <div className="p-4 mb-4 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"></path>
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
                {locale === 'zh' ? '生成报告出错' : 'Error generating report'}
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-center mt-4">
          <button
            onClick={handleRetry}
            className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white rounded-md"
          >
            {locale === 'zh' ? '重试' : 'Retry'}
          </button>
        </div>
      </div>
    );
  }

  // Use unified report display for both locked and unlocked states
  if (testId) {
    // 如果正在加载且使用流式生成，显示流式生成器
    if (loading && useStreaming && !report) {
      const forceParam = regenerate ? '?force=true' : '';
      const streamEndpoint = `/api/autism/report-stream${forceParam}`;

      // 构建请求数据
      let requestData: any;
      const apiTestType = testType === 'raadsr' ? 'raadsrTest' : 'aq10Test';

      if (testType === 'raadsr') {
        requestData = {
          type: apiTestType,
          answers,
          score: scores,
          locale,
          testId
        };
      } else if (testType === 'aq10') {
        const aq10Score = score || (scores?.total) || scores;
        requestData = {
          type: apiTestType,
          answers,
          score: aq10Score,
          locale,
          testId
        };
      }

      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <StreamingReportGenerator
            endpoint={streamEndpoint}
            requestData={requestData}
            onComplete={handleStreamingComplete}
            onError={handleStreamingError}
            locale={locale}
            className="p-4 sm:p-6"
          />
        </div>
      );
    }

    return (
      <UnifiedReportDisplay
        testId={testId}
        testType={testType === 'raadsr' ? 'raadsrTest' : 'aq10Test'}
        answers={answers}
        scores={scores}
        score={score}
        report={report || ''}
        isReportLocked={isReportLocked}
        isStreaming={isStreaming}
        justUnlocked={justUnlocked}
        onStreamingComplete={() => {
          setIsStreaming(false);
          setJustUnlocked(false);
        }}
        onUnlock={handleUnlock}
        translations={{
          reportLocked: t('reportLocked'),
          reportLockedDescription: t('reportLockedDescription'),
          unlockReport: t('unlockReport'),
          shareReport: t('shareReport'),
          shareToUnlock: t('shareToUnlock'),
          shareReportDescription: t('shareReportDescription'),
          payToUnlock: t('payToUnlock'),
          comingSoon: t('comingSoon'),
          linkCopied: t('linkCopied'),
          shareUnlockDivider: t('shareUnlockDivider'),
          shareHelpText: t('shareHelpText'),
          payToUnlockTab: t('payToUnlockTab'),
          shareToUnlockTab: t('shareToUnlockTab'),
          shareToUnlockFull: t('shareToUnlockFull'),
          free: t('free'),
          share: t('share'),
          unlockReportButton: t('unlockReportButton'),
          viewCompleteAnalysis: t('viewCompleteAnalysis'),
          payment: {
            title: t('payment.title'),
            description: t('payment.description'),
            cardDetails: t('payment.cardDetails'),
            payButton: t('payment.payButton'),
            processing: t('payment.processing'),
            cancel: t('payment.cancel'),
            success: t('payment.success'),
            error: t('payment.error')
          },
          subscription: {
            title: t('subscription.title'),
            billingPeriod: {
              monthly: t('subscription.billingPeriod.monthly'),
              yearly: t('subscription.billingPeriod.yearly'),
              save: t('subscription.billingPeriod.save'),
              saveAmount: locale === 'zh' ? '省{amount}%' : 'SAVE {amount}%'
            },
            period: {
              month: t('subscription.period.month'),
              year: t('subscription.period.year')
            },
            singleReport: {
              oneTime: t('subscription.singleReport.oneTime'),
              unlockButton: locale === 'zh' ? '解锁 ${price}' : 'Unlock ${price}'
            },
            plans: {
              selectPro: t('subscription.plans.selectPro'),
              selectPremium: t('subscription.plans.selectPremium'),
              mostPopular: t('subscription.plans.mostPopular'),
              savePerYear: locale === 'zh' ? '每年省$${amount}' : 'Save $${amount}/year'
            }
          },
          referralStats: {
            title: t('referralStats.title'),
            clicks: t('referralStats.clicks'),
            completions: t('referralStats.completions'),
            needsCompletions: t('referralStats.needsCompletions'),
            pendingClicks: t('referralStats.pendingClicks'),
            unlocked: t('referralStats.unlocked')
          }
        }}
        email={email}
        userId={userId}
        locale={locale}
        reportType="autism"
      />
    );
  }

  // Fallback for no test ID
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
      <div className="text-center p-8">
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          {locale === 'zh' ? '无法加载报告' : 'Unable to load report'}
        </p>
      </div>
    </div>
  );


} 