"use client";

import React from 'react';
import LockedReportUnlock from '@/components/LockedReportUnlock';

interface SmartGuidanceLockedReportProps {
    id: string;
    locale: string;
    type: 'adhd' | 'autism';
    email?: string;
    userId?: string;
    onUnlock: () => void;
    translations: {
        reportLocked: string;
        reportLockedDescription: string;
        unlockReport: string;
        shareReport: string;
        shareToUnlock: string;
        shareReportDescription: string;
        payToUnlock: string;
        comingSoon: string;
        linkCopied?: string;
        shareUnlockDivider?: string;
        shareHelpText?: string;
        payToUnlockTab?: string;
        shareToUnlockTab?: string;
        shareToUnlockFull?: string;
        free?: string;
        share?: string;
        unlockReportButton?: string;
        viewCompleteAnalysis?: string;
        payment?: {
            title: string;
            description: string;
            cardDetails: string;
            payButton: string;
            processing: string;
            cancel: string;
            success: string;
            error: string;
        };
        subscription?: {
            title: string;
            billingPeriod: {
                monthly: string;
                yearly: string;
                save: string;
                saveAmount: string;
            };
            period: {
                month: string;
                year: string;
            };
            singleReport: {
                oneTime: string;
                unlockButton: string;
            };
            plans: {
                selectPro: string;
                selectPremium: string;
                mostPopular: string;
                savePerYear: string;
            };
        };
        referralStats?: {
            title: string;
            clicks: string;
            completions: string;
            needsCompletions: string;
            pendingClicks: string;
            unlocked: string;
        };
    };
    children: React.ReactNode;
}

export default function SmartGuidanceLockedReport({
    id,
    locale,
    type,
    email,
    userId,
    onUnlock,
    translations,
    children
}: SmartGuidanceLockedReportProps) {
    return (
        <LockedReportUnlock
            id={id}
            locale={locale}
            type={type}
            email={email}
            userId={userId}
            onUnlock={onUnlock}
            translations={translations}
        >
            {children}
        </LockedReportUnlock>
    );
} 