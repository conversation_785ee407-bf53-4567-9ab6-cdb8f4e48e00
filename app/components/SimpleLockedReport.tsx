"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import { Lock, Share2, Facebook, Twitter, Linkedin, MessageCircle, Users, Eye, CheckCircle } from 'lucide-react';
import { useReferralStats } from '@/hooks/useReferralTracking';
import { generateShareUrl } from '@/utils/referralUtils';

interface SimpleLockedReportProps {
    id: string;
    locale: string;
    type: 'adhd' | 'autism';
    onUnlock: () => void;
    translations: {
        reportLocked: string;
        reportLockedDescription: string;
        unlockReport: string;
        shareReport: string;
        shareToUnlock: string;
        shareReportDescription: string;
        payToUnlock: string;
        comingSoon: string;
        linkCopied?: string;
        shareUnlockDivider?: string;
        shareHelpText?: string;
        referralStats?: {
            title: string;
            clicks: string;
            completions: string;
            needsCompletions: string;
            pendingClicks: string;
            unlocked: string;
        };
    };
    children: React.ReactNode;
}

export default function SimpleLockedReport({
    id,
    locale,
    type,
    onUnlock,
    translations,
    children
}: SimpleLockedReportProps) {
    // 状态管理
    const [showUnlockArea, setShowUnlockArea] = useState(false);
    const [referralCode, setReferralCode] = useState<string>('');
    const [isCopied, setIsCopied] = useState(false);
    const [isUnlocking, setIsUnlocking] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Refs
    const blurContainerRef = useRef<HTMLDivElement>(null);
    const observerRef = useRef<IntersectionObserver | null>(null);

    // 获取推荐统计
    const { stats, loading: statsLoading, refetch } = useReferralStats(id);

    // 设置Intersection Observer
    useEffect(() => {
        if (!blurContainerRef.current) return;

        // 清理之前的observer
        if (observerRef.current) {
            observerRef.current.disconnect();
        }

        // 创建新的observer
        observerRef.current = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    // 检查blur区域是否在viewport中央
                    const rect = entry.boundingClientRect;
                    const viewportHeight = window.innerHeight;
                    const viewportCenter = viewportHeight / 2;

                    // 移动端优化：降低触发阈值，更容易触发
                    const isMobile = window.innerWidth <= 768;

                    let isInCenter;
                    if (isMobile) {
                        // 移动端：只要blur区域占据viewport中央30%以上就触发
                        const centerZoneTop = viewportHeight * 0.35; // 35%位置
                        const centerZoneBottom = viewportHeight * 0.65; // 65%位置
                        isInCenter = rect.top <= centerZoneBottom && rect.bottom >= centerZoneTop;
                    } else {
                        // 桌面端：保持原有逻辑
                        isInCenter = rect.top <= viewportCenter && rect.bottom >= viewportCenter;
                    }

                    setShowUnlockArea(isInCenter);

                    // 调试信息（开发环境）
                    if (process.env.NODE_ENV === 'development') {
                        console.log('IntersectionObserver Debug:', {
                            isMobile,
                            viewportHeight,
                            rectTop: rect.top,
                            rectBottom: rect.bottom,
                            isInCenter,
                            intersectionRatio: entry.intersectionRatio
                        });
                    }
                });
            },
            {
                root: null,
                rootMargin: '0px',
                // 移动端使用更密集的threshold
                threshold: window.innerWidth <= 768
                    ? [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95, 1.0]
                    : [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
            }
        );

        observerRef.current.observe(blurContainerRef.current);

        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, []);

    // 移动端备用滚动监听器（fallback机制）
    useEffect(() => {
        const isMobile = window.innerWidth <= 768;
        if (!isMobile || !blurContainerRef.current) return;

        let scrollTimeout: NodeJS.Timeout;

        const handleScroll = () => {
            // 防抖处理
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (!blurContainerRef.current) return;

                const rect = blurContainerRef.current.getBoundingClientRect();
                const viewportHeight = window.innerHeight;
                const centerZoneTop = viewportHeight * 0.35;
                const centerZoneBottom = viewportHeight * 0.65;

                const isInCenter = rect.top <= centerZoneBottom && rect.bottom >= centerZoneTop;

                // 只在IntersectionObserver可能失效时使用这个fallback
                setShowUnlockArea(isInCenter);

                if (process.env.NODE_ENV === 'development') {
                    console.log('Scroll Fallback Debug:', {
                        rectTop: rect.top,
                        rectBottom: rect.bottom,
                        centerZoneTop,
                        centerZoneBottom,
                        isInCenter
                    });
                }
            }, 100); // 100ms防抖
        };

        // 添加滚动监听器
        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('touchmove', handleScroll, { passive: true });

        return () => {
            clearTimeout(scrollTimeout);
            window.removeEventListener('scroll', handleScroll);
            window.removeEventListener('touchmove', handleScroll);
        };
    }, []);

    // 获取推荐码
    useEffect(() => {
        if (stats?.referralCode) {
            setReferralCode(stats.referralCode);
        }
    }, [stats]);

    // 生成分享URL
    const getShareUrl = () => {
        if (typeof window === 'undefined' || !referralCode) return '';
        return generateShareUrl(id, referralCode, locale, type);
    };

    // 处理系统分享
    const handleSystemShare = async () => {
        try {
            const shareUrl = getShareUrl();

            if (navigator.share) {
                await navigator.share({
                    title: translations.reportLocked,
                    text: translations.shareReportDescription,
                    url: shareUrl,
                });
                setTimeout(() => refetch(), 1000);
            } else {
                await navigator.clipboard.writeText(shareUrl);
                setIsCopied(true);
                setTimeout(() => setIsCopied(false), 2000);
                setTimeout(() => refetch(), 1000);
            }
        } catch (error) {
            console.error('分享失败:', error);
        }
    };

    // 处理社交媒体分享
    const handleSocialShare = (platform: string) => {
        const shareUrl = getShareUrl();
        const text = encodeURIComponent(translations.shareReportDescription);

        let shareLink = '';
        switch (platform) {
            case 'twitter':
                shareLink = `https://twitter.com/intent/tweet?text=${text}&url=${encodeURIComponent(shareUrl)}`;
                break;
            case 'facebook':
                shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${text}`;
                break;
            case 'linkedin':
                shareLink = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
                break;
            case 'whatsapp':
                shareLink = `https://api.whatsapp.com/send?text=${text}%20${encodeURIComponent(shareUrl)}`;
                break;
        }

        if (shareLink) {
            window.open(shareLink, '_blank', 'width=600,height=500');
            setTimeout(() => refetch(), 1000);
        }
    };

    // 处理解锁报告
    const handleUnlock = async () => {
        setIsUnlocking(true);
        setError(null);

        try {
            const response = await fetch('/api/unlock-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to unlock report');
            }

            onUnlock();
        } catch (error) {
            console.error('解锁报告时出错:', error);
            setError(error instanceof Error ? error.message : '解锁报告时出错');
        } finally {
            setIsUnlocking(false);
        }
    };

    return (
        <div className="relative">
            {/* 模糊的报告内容 */}
            <div ref={blurContainerRef} className="filter blur-sm pointer-events-none">
                {children}
            </div>

            {/* 固定定位的unlock区域 - 只在blur区域在viewport中央时显示 */}
            {showUnlockArea && (
                <div
                    className="fixed inset-0 z-40 flex items-center justify-center p-4 bg-black/20 backdrop-blur-sm"
                    style={{
                        animation: 'fadeIn 0.3s ease-out'
                    }}
                >
                    <div className="w-full max-w-sm sm:max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden">
                        {/* 锁定图标和标题 - 紧凑布局 */}
                        <div className="text-center px-4 sm:px-6 pt-4 sm:pt-6 pb-3">
                            <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mx-auto mb-3">
                                <Lock className="w-6 h-6 sm:w-7 sm:h-7 text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <h2 className="text-lg sm:text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">
                                {translations.reportLocked}
                            </h2>
                            <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed">
                                {translations.reportLockedDescription}
                            </p>
                        </div>

                        {/* 推荐统计 - 优化布局 */}
                        {stats && !statsLoading && translations.referralStats && (
                            <div className="mx-4 sm:mx-6 mb-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 overflow-hidden">
                                <div className="flex items-center justify-between px-3 py-2 bg-blue-100 dark:bg-blue-900/40">
                                    <h3 className="text-xs font-semibold text-blue-800 dark:text-blue-200 flex items-center">
                                        <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full mr-2"></div>
                                        {translations.referralStats.title}
                                    </h3>
                                    {stats.isUnlocked && (
                                        <div className="flex items-center text-green-600 dark:text-green-400">
                                            <CheckCircle className="w-3 h-3 mr-1" />
                                            <span className="text-xs font-medium">{translations.referralStats.unlocked}</span>
                                        </div>
                                    )}
                                </div>
                                <div className="p-3">
                                    <div className="grid grid-cols-2 gap-2 mb-2">
                                        <div className="bg-white dark:bg-gray-800 rounded border border-blue-100 dark:border-blue-700 p-2.5">
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100 leading-none">{stats.totalClicks}</div>
                                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{translations.referralStats.clicks}</div>
                                                </div>
                                                <div className="ml-2">
                                                    <Eye className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                                </div>
                                            </div>
                                        </div>
                                        <div className="bg-white dark:bg-gray-800 rounded border border-blue-100 dark:border-blue-700 p-2.5">
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100 leading-none">{stats.totalCompletions}</div>
                                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{translations.referralStats.completions}</div>
                                                </div>
                                                <div className="ml-2">
                                                    <Users className="w-4 h-4 text-green-600 dark:text-green-400" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {!stats.isUnlocked && (
                                        <div className="bg-orange-50 dark:bg-orange-900/20 rounded border border-orange-200 dark:border-orange-800 p-2.5">
                                            <div className="flex items-center justify-center">
                                                <div className="w-2 h-2 bg-orange-500 rounded-full mr-2 flex-shrink-0"></div>
                                                <p className="text-xs text-orange-800 dark:text-orange-200 text-center">
                                                    {translations.referralStats.needsCompletions.replace('COUNT_PLACEHOLDER', String(stats.needsCompletions - stats.totalCompletions))}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* 解锁按钮 - 突出显示 */}
                        <div className="px-4 sm:px-6 pb-4">
                            <button
                                onClick={handleUnlock}
                                disabled={isUnlocking}
                                className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 disabled:from-emerald-400 disabled:to-emerald-500 text-white font-bold py-3.5 px-6 rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
                            >
                                {isUnlocking ? (
                                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                ) : (
                                    <>
                                        <Lock className="w-5 h-5 mr-2" />
                                        <span className="text-base">{translations.unlockReport}</span>
                                    </>
                                )}
                            </button>

                            {/* 分享选项 - 优化布局 */}
                            <div className="mt-4 text-center">
                                <div className="relative">
                                    <div className="absolute inset-0 flex items-center">
                                        <div className="w-full border-t border-gray-200 dark:border-gray-600"></div>
                                    </div>
                                    <div className="relative flex justify-center text-xs">
                                        <span className="bg-white dark:bg-gray-800 px-3 text-gray-500 dark:text-gray-400">
                                            {translations.shareUnlockDivider || 'Or share to unlock'}
                                        </span>
                                    </div>
                                </div>

                                <div className="mt-4 space-y-3">
                                    <button
                                        onClick={handleSystemShare}
                                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                                    >
                                        <Share2 className="w-4 h-4 mr-2" />
                                        <span className="text-sm">{isCopied ? (translations.linkCopied || 'Link copied!') : translations.shareToUnlock}</span>
                                    </button>

                                    {/* 社交媒体按钮 - 添加文案 */}
                                    <div className="grid grid-cols-2 gap-2">
                                        <button
                                            onClick={() => handleSocialShare('facebook')}
                                            className="flex items-center justify-center p-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                                            title="Share on Facebook"
                                        >
                                            <Facebook className="w-4 h-4 mr-1.5" />
                                            <span className="text-xs font-medium">Facebook</span>
                                        </button>
                                        <button
                                            onClick={() => handleSocialShare('twitter')}
                                            className="flex items-center justify-center p-2.5 bg-sky-500 hover:bg-sky-600 text-white rounded-lg transition-colors duration-200"
                                            title="Share on Twitter"
                                        >
                                            <Twitter className="w-4 h-4 mr-1.5" />
                                            <span className="text-xs font-medium">Twitter</span>
                                        </button>
                                        <button
                                            onClick={() => handleSocialShare('linkedin')}
                                            className="flex items-center justify-center p-2.5 bg-blue-700 hover:bg-blue-800 text-white rounded-lg transition-colors duration-200"
                                            title="Share on LinkedIn"
                                        >
                                            <Linkedin className="w-4 h-4 mr-1.5" />
                                            <span className="text-xs font-medium">LinkedIn</span>
                                        </button>
                                        <button
                                            onClick={() => handleSocialShare('whatsapp')}
                                            className="flex items-center justify-center p-2.5 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
                                            title="Share on WhatsApp"
                                        >
                                            <MessageCircle className="w-4 h-4 mr-1.5" />
                                            <span className="text-xs font-medium">WhatsApp</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {error && (
                            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                            </div>
                        )}
                    </div>
                </div>
            )}

            <style jsx>{`
                @keyframes fadeIn {
                    from {
                        opacity: 0;
                        transform: scale(0.95);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1);
                    }
                }
            `}</style>
        </div>
    );
} 