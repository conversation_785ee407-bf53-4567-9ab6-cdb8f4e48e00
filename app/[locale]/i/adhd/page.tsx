import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import AdhdPreScreening from '../../adhd/components/AdhdPreScreening';
import IframeResizer from '@/components/IframeResizer';

export async function generateMetadata({
    params
}: {
    params: Promise<{ locale: string }>
}): Promise<Metadata> {
    // 等待params对象解析
    const resolvedParams = await params;
    const { locale } = resolvedParams;

    const t = await getTranslations('pages.adhd.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}/${locale}/i/adhd`,
        }
    };
}

export default async function AdhdIframePage() {
    const t = await getTranslations('pages.adhd.content');

    return (
        <div className="py-4 min-h-screen">
            <div className="container mx-auto px-4">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center space-y-4 mb-6">
                        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gradient">
                            {t('title')}
                        </h1>
                        <p className="text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            {t('description')}
                        </p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6">
                        <AdhdPreScreening />
                    </div>
                </div>
            </div>
            {/* Client component to handle iframe resizing */}
            <IframeResizer 
              testType="adhd" 
              observeElement=".container"
              compactMode={true}
              resizeInterval={300}
            />
        </div>
    );
} 