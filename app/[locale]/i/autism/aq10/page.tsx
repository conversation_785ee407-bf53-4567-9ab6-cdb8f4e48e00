import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import Aq10Test from '../../../autism/components/Aq10Test';
import IframeResizer from '@/components/IframeResizer';

interface PageProps {
    params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
    // Ensure params are resolved
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations({ locale, namespace: 'pages.autism' });
    const baseUrl = await getBaseUrl();

    return {
        title: `AQ-10 ${t('metadata.title')}`,
        description: t('metadata.description'),
        openGraph: {
            title: `AQ-10 ${t('metadata.title')}`,
            description: t('metadata.description'),
            type: 'website',
            url: `${baseUrl}/${locale}/i/autism/aq10`,
        }
    };
}

export default function Aq10IframePage({ params }: { params: Promise<{ locale: string }> }) {
    return (
        <div className="py-4">
            <Aq10Test />
            <IframeResizer 
              testType="autism" 
              observeElement="#aq10-test-container"
              compactMode={true}
              resizeInterval={300}
            />
        </div>
    );
} 