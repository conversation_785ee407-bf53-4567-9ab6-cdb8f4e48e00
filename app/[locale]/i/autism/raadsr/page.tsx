import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import RaadsrTest from '../../../autism/components/RaadsrTest';
import IframeResizer from '@/components/IframeResizer';

interface PageProps {
    params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
    // Ensure params are resolved
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations({ locale, namespace: 'pages.autism' });
    const baseUrl = await getBaseUrl();

    return {
        title: `RAADS-R ${t('metadata.title')}`,
        description: t('metadata.description'),
        openGraph: {
            title: t('metadata.title'),
            description: t('metadata.description'),
            type: 'website',
            url: `${baseUrl}/${locale}/i/autism/raadsr`,
        }
    };
}

export default function RaadsrIframePage({ params }: { params: Promise<{ locale: string }> }) {
    return (
        <div className="py-4">
            <RaadsrTest />
            <IframeResizer 
              testType="autism" 
              observeElement="#raadsr-test-container"
              compactMode={true}
              resizeInterval={300}
            />
        </div>
    );
} 