import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import ResultDisplay from '../../../../autism/result/[id]/ResultDisplay';
import { prisma } from '@/utils/prisma';
import type { TestResult } from '@/types/prisma';
import IframeResizer from '@/components/IframeResizer';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string; id: string }>
}): Promise<Metadata> {
  // 等待 params 对象
  const resolvedParams = await params;
  const { locale, id } = resolvedParams;
  
  const t = await getTranslations({ locale, namespace: 'pages.autism' });
  const baseUrl = await getBaseUrl();
  
  return {
    title: t('result.pageTitle'),
    description: t('result.description'),
    openGraph: {
      title: t('result.pageTitle'),
      description: t('result.description'),
      type: 'website',
      url: `${baseUrl}/${locale}/i/autism/result/${id}`,
    }
  };
}

// 获取特定测试结果
async function getTestResult(id: string) {
  try {
    console.log(`[Iframe] 获取测试结果，ID: ${id}`);
    const testResult = await prisma.testResult.findUnique({
      where: { id }
    });
    
    if (!testResult) {
      console.log(`[Iframe] 未找到测试结果，ID: ${id}`);
      return null;
    }
    
    console.log(`[Iframe] 测试类型: ${testResult.testType}`);
    console.log(`[Iframe] 答案数据类型: ${typeof testResult.answers}`);
    if (typeof testResult.answers === 'object') {
      console.log(`[Iframe] 答案包含 score 字段: ${'score' in (testResult.answers || {})}`);
    }
    
    // 将 Prisma 模型转换为 TestResult 类型
    const typedResult: TestResult = {
      ...testResult,
      testType: testResult.testType as 'raadsrTest' | 'aq10Test',
      answers: testResult.answers as Record<number, number>,
      scores: testResult.scores as TestResult['scores'],
    };
    
    // 对于 AQ-10 测试，确保 score 数据正确提取
    if (testResult.testType === 'aq10Test') {
      // 尝试从 answers.score 中获取分数
      if (typeof testResult.answers === 'object' && 
          testResult.answers && 
          'score' in testResult.answers) {
        console.log(`[Iframe] 找到 answers.score 数据`);
        typedResult.score = testResult.answers.score as TestResult['score'];
      } 
      // 如果没有在 answers.score 中找到，尝试直接使用 scores
      else if (testResult.scores && typeof testResult.scores === 'object') {
        console.log(`[Iframe] 使用 scores 作为 score 数据`);
        // 如果 scores 有 score/max/significant 属性，则说明它就是 score 对象
        if ('score' in testResult.scores && 'max' in testResult.scores && 'significant' in testResult.scores) {
          typedResult.score = testResult.scores as unknown as TestResult['score'];
        }
        // 否则尝试 scores.total
        else if (testResult.scores && typeof testResult.scores === 'object' && 
                (testResult.scores as any).total) {
          typedResult.score = (testResult.scores as any).total as TestResult['score'];
        }
      }
      
      console.log(`[Iframe] 最终 score 数据: ${typedResult.score ? JSON.stringify(typedResult.score) : '未找到'}`);
    }
    
    return typedResult;
  } catch (error) {
    console.error('Error fetching test result:', error);
    return null;
  }
}

// iframe 测试结果页面
export default async function IframeResultPage({ params }: { params: Promise<{ id: string, locale: string }> }) {
  // 等待 params 对象
  const resolvedParams = await params;
  const { id, locale } = resolvedParams;
  
  // 获取测试结果
  const testResult = await getTestResult(id);
  
  if (!testResult) {
    return (
      <>
        <div className="p-6 text-center text-red-500">结果不存在或已过期</div>
        <IframeResizer 
          testType="autism" 
          compactMode={true}
          resizeInterval={300}
        />
      </>
    );
  }
  
  return (
    <>
      <ResultDisplay testResult={testResult} locale={locale} />
      <IframeResizer 
        testType="autism" 
        observeElement=".result-container" 
        compactMode={true}
        resizeInterval={300}
      />
    </>
  );
} 