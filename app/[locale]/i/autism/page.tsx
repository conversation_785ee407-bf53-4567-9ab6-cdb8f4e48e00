import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import Link from 'next/link';
import { 
  ArrowRight, 
  BrainCircuit, 
  ClipboardList
} from 'lucide-react';
import IframeResizer from '@/components/IframeResizer';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
    // Ensure params are resolved
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations({ locale, namespace: 'pages.autism.metadata' });
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}/${locale}/i/autism`,
        }
    };
}

export default async function AutismIframePage({ params }: { params: Promise<{ locale: string }> }) {
    // Ensure params are resolved
    const resolvedParams = await params;
    const locale = resolvedParams.locale;
    
    const t = await getTranslations({ locale, namespace: 'pages.autism' });
    
    return (
        <div className="py-4 min-h-screen">
            <div className="container mx-auto px-4">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center space-y-4 mb-6">
                        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gradient">
                            {t('content.title')}
                        </h1>
                        <p className="text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            {t('content.description')}
                        </p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6">
                        <div className="grid md:grid-cols-2 gap-6">
                            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition duration-300 flex flex-col h-full">
                                <div>
                                    <div className="flex items-baseline mb-4">
                                        <BrainCircuit className="w-5 h-5 text-indigo-600 dark:text-indigo-400 mr-3 flex-shrink-0 mt-0.5" />
                                        <h2 className="text-xl font-bold leading-none">RAADS-R</h2>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {t('content.raadsrDescription')}
                                    </p>
                                    <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-md mb-6 text-sm">
                                        <p className="mb-1">{t('tests.raadsr.description')}</p>
                                    </div>
                                </div>
                                <div className="mt-auto">
                                    <Link href={`/${locale}/i/autism/raadsr`} className="inline-flex items-center justify-center w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md transition duration-300">
                                        {t('tests.raadsr.button')}
                                        <ArrowRight className="ml-2 w-4 h-4" />
                                    </Link>
                                </div>
                            </div>

                            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition duration-300 flex flex-col h-full">
                                <div>
                                    <div className="flex items-baseline mb-4">
                                        <ClipboardList className="w-5 h-5 text-emerald-600 dark:text-emerald-400 mr-3 flex-shrink-0 mt-0.5" />
                                        <h2 className="text-xl font-bold leading-none">AQ-10</h2>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {t('content.aq10Description')}
                                    </p>
                                    <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-md mb-6 text-sm">
                                        <p className="mb-1">{t('tests.aq10.description')}</p>
                                    </div>
                                </div>
                                <div className="mt-auto">
                                    <Link href={`/${locale}/i/autism/aq10`} className="inline-flex items-center justify-center w-full bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-2 px-4 rounded-md transition duration-300">
                                        {t('tests.aq10.button')}
                                        <ArrowRight className="ml-2 w-4 h-4" />
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Client component to handle iframe resizing */}
            <IframeResizer 
              testType="autism" 
              observeElement=".container"
              compactMode={true}
              resizeInterval={300}
            />
        </div>
    );
} 