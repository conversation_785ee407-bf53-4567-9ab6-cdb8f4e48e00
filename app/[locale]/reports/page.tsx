import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { redirect } from 'next/navigation';
import { prisma } from '@/utils/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import Link from 'next/link';
import { ChevronRight, Clock, BarChart2, Calendar, Lock, CheckCircle } from 'lucide-react';
import { formatDate } from '@/utils/formatters';
import UnboundTestsBanner from '@/components/UnboundTestsBanner';
import { getLocalizedPath } from '@/utils/utils';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  const t = await getTranslations('pages.reports');

  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
  };
}

// Helper function to generate correct result URL based on test type
const getTestResultUrl = (testType: string, testId: string, locale: string): string => {
  const localePrefix = locale === 'en' ? '' : `/${locale}`;

  if (testType === 'raadsrTest' || testType === 'aq10Test') {
    return `${localePrefix}/autism/result/${testId}`;
  } else if (testType === 'adult' || testType === 'child' || testType === 'general') {
    return `${localePrefix}/adhd/result/${testId}`;
  } else {
    // For unknown test types, redirect to home page
    return `${localePrefix}/`;
  }
};

// Helper function to get test type visual representation
const getTestTypeIndicator = (testType: string, color: string) => {
  const indicators = {
    'raadsrTest': { label: 'R', fullName: 'RAADS-R' },
    'aq10Test': { label: 'AQ', fullName: 'AQ-10' },
    'adult': { label: 'AD', fullName: 'Adult ADHD' },
    'child': { label: 'CH', fullName: 'Child ADHD' },
    'general': { label: 'GN', fullName: 'General' },
  };

  const indicator = indicators[testType as keyof typeof indicators] || { label: 'T', fullName: testType };

  const colorClasses = {
    'indigo': 'bg-indigo-100 text-indigo-700 border-indigo-200',
    'blue': 'bg-blue-100 text-blue-700 border-blue-200',
    'purple': 'bg-purple-100 text-purple-700 border-purple-200',
    'teal': 'bg-teal-100 text-teal-700 border-teal-200',
    'gray': 'bg-gray-100 text-gray-700 border-gray-200'
  }[color] || 'bg-gray-100 text-gray-700 border-gray-200';

  return {
    element: (
      <div className={`flex-shrink-0 w-14 h-14 rounded-lg ${colorClasses} border flex items-center justify-center font-bold text-sm transition-all group-hover:scale-105`}>
        {indicator.label}
      </div>
    ),
    fullName: indicator.fullName
  };
};

export default async function ReportsPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 确保参数已解析
  const resolvedParams = await params;
  const locale = resolvedParams.locale;

  const t = await getTranslations('pages.reports');
  const session = await getServerSession(authOptions);

  // 检查用户是否已登录
  if (!session) {
    redirect(getLocalizedPath(`/auth/signin?callbackUrl=${encodeURIComponent(getLocalizedPath('/reports', locale))}`, locale));
  }

  // 获取用户的所有测试结果，添加分页以提高性能
  const userReports = await prisma.testResult.findMany({
    where: {
      userId: session.user.id,
      isUserBound: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    // 可选：添加分页
    // take: 20,
  });

  // 测试类型映射 - 增加颜色和描述
  const testTypeMap: Record<string, { name: string, color: string, description: string }> = {
    'raadsrTest': {
      name: t('testTypes.raadsrTest'),
      color: 'indigo',
      description: t('descriptions.raadsrTest')
    },
    'aq10Test': {
      name: t('testTypes.aq10Test'),
      color: 'blue',
      description: t('descriptions.aq10Test')
    },
    'adult': {
      name: t('testTypes.adult'),
      color: 'purple',
      description: t('descriptions.adult')
    },
    'child': {
      name: t('testTypes.child'),
      color: 'teal',
      description: t('descriptions.child')
    },
  };

  // 格式化日期函数
  const formatTestDate = (date: Date) => {
    return new Date(date).toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <main className="flex flex-col min-h-screen pt-20 md:pt-24 pb-8 md:pb-12 px-3 md:px-4 bg-gray-50">
      <div className="container mx-auto max-w-6xl">
        {/* 页头部分 - 移动端优化的紧凑布局 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8 mb-4 md:mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3 md:gap-4">
            <div>
              <h1 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2 text-gray-900 flex items-center font-heading">
                <BarChart2 className="mr-2 text-blue-600 h-5 w-5 md:h-6 md:w-6" />
                {t('heading')}
              </h1>
              <p className="text-gray-600 text-sm md:text-base max-w-lg leading-relaxed">
                {t('description')}
              </p>
            </div>
            <Link
              href={getLocalizedPath('/', locale)}
              className="inline-flex items-center justify-center px-4 py-2.5 md:py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm self-start md:self-center text-sm md:text-base font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              {t('takeNewTest')}
              <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
        </div>

        {/* UnboundTestsBanner - 显示未绑定的测试 */}
        <UnboundTestsBanner locale={locale} />

        {userReports.length === 0 ? (
          <div className="bg-white rounded-xl shadow-md p-6 md:p-8 lg:p-12 text-center">
            <div className="w-16 h-16 md:w-24 md:h-24 lg:w-32 lg:h-32 mx-auto mb-4 md:mb-6 bg-blue-50 rounded-full flex items-center justify-center">
              <BarChart2 size={32} className="text-blue-600 md:hidden" />
              <BarChart2 size={40} className="text-blue-600 hidden md:block" />
            </div>
            <h2 className="text-lg md:text-xl lg:text-2xl font-bold mb-3 md:mb-4 text-gray-900 font-heading">{t('noReports.title')}</h2>
            <p className="text-gray-600 max-w-md mx-auto mb-6 md:mb-8 text-sm md:text-base leading-relaxed">{t('noReports.description')}</p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center items-center">
              <Link
                href={getLocalizedPath('/', locale)}
                className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full sm:w-auto text-sm md:text-base"
              >
                {t('noReports.button')}
                <ChevronRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Link>
              <Link
                href={getLocalizedPath('/profile', locale)}
                className="inline-flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 w-full sm:w-auto text-sm md:text-base"
              >
                View Profile
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-3 md:space-y-4">
            {userReports.map((report) => {
              const testInfo = testTypeMap[report.testType] || {
                name: report.testType,
                color: 'gray',
                description: ''
              };

              // 获取元数据信息
              const metadata = report.metadata as Record<string, any> || {};
              const testDate = formatTestDate(report.createdAt);
              const typeIndicator = getTestTypeIndicator(report.testType, testInfo.color);

              // 提取分数信息
              let scoreDisplay = null;
              let scorePercentage = 0;

              try {
                if (report.testType === 'aq10Test') {
                  // AQ-10 测试分数提取逻辑 - 简化并与 profile 页面一致
                  let score, max;

                  // 尝试从 scores.score 中获取
                  if (report.scores && typeof report.scores === 'object' && report.scores !== null && 'score' in report.scores) {
                    const scoreData = (report.scores as any).score;
                    if (scoreData && typeof scoreData === 'object') {
                      score = scoreData.score || scoreData;
                      max = scoreData.max || 10; // AQ-10 最大分数是 10
                    } else if (typeof scoreData === 'number') {
                      score = scoreData;
                      max = 10;
                    }
                  }
                  // 尝试从 scores 直接获取（如果 scores 本身就是分数对象）
                  else if (report.scores && typeof report.scores === 'object' && 'score' in report.scores && 'max' in report.scores) {
                    const scoresObj = report.scores as any;
                    score = scoresObj.score;
                    max = scoresObj.max;
                  }
                  // 如果都没找到，检查是否在 answers.score 中
                  else if (report.answers && typeof report.answers === 'object' && report.answers !== null && 'score' in report.answers) {
                    const scoreData = (report.answers as any).score;
                    if (scoreData && typeof scoreData === 'object') {
                      score = scoreData.score || scoreData;
                      max = scoreData.max || 10;
                    }
                  }

                  if (typeof score === 'number' && typeof max === 'number') {
                    scorePercentage = (score / max) * 100;

                    scoreDisplay = (
                      <div className="bg-blue-50 rounded-lg p-2 text-center min-w-[70px] border border-blue-100 shadow-sm h-14 flex flex-col justify-center">
                        <div className="text-lg font-bold text-blue-700 font-heading leading-none">{score}</div>
                        <div className="text-xs text-blue-600 leading-tight mt-0.5">of {max}</div>
                        <div className="w-full bg-blue-200 rounded-full h-1 mt-1">
                          <div className="bg-blue-600 h-1 rounded-full transition-all duration-300" style={{ width: `${scorePercentage}%` }}></div>
                        </div>
                      </div>
                    );
                  }
                } else if (report.scores && typeof report.scores === 'object' && report.scores !== null && 'total' in report.scores) {
                  // 其他测试类型的分数提取逻辑
                  const totalData = (report.scores as any).total;
                  if (totalData && typeof totalData === 'object') {
                    const score = totalData.score;
                    const max = totalData.max;
                    if (typeof score === 'number' && typeof max === 'number') {
                      scorePercentage = (score / max) * 100;

                      const colorClasses = {
                        indigo: 'bg-indigo-50 text-indigo-700 border-indigo-100',
                        purple: 'bg-purple-50 text-purple-700 border-purple-100',
                        teal: 'bg-teal-50 text-teal-700 border-teal-100',
                        gray: 'bg-gray-50 text-gray-700 border-gray-100'
                      }[testInfo.color] || 'bg-gray-50 text-gray-700 border-gray-100';

                      const progressColor = {
                        indigo: 'bg-indigo-600',
                        purple: 'bg-purple-600',
                        teal: 'bg-teal-600',
                        gray: 'bg-gray-600'
                      }[testInfo.color] || 'bg-gray-600';

                      scoreDisplay = (
                        <div className={`${colorClasses} rounded-lg p-2 text-center min-w-[70px] border shadow-sm h-14 flex flex-col justify-center`}>
                          <div className="text-lg font-bold font-heading leading-none">{score}</div>
                          <div className="text-xs leading-tight mt-0.5">of {max}</div>
                          <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                            <div className={`${progressColor} h-1 rounded-full transition-all duration-300`} style={{ width: `${scorePercentage}%` }}></div>
                          </div>
                        </div>
                      );
                    }
                  }
                }
              } catch (e) {
                console.error("Error extracting score:", e);
              }

              return (
                <Link
                  key={report.id}
                  href={getTestResultUrl(report.testType, report.id, locale)}
                  className="block bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-4 hover:shadow-lg hover:border-gray-300 transition-all duration-200 group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-[0.98] md:active:scale-100"
                  aria-label={`View ${typeIndicator.fullName} test result`}
                >
                  {/* 优化的响应式布局 - 移动端纵向，桌面端横向 */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                    {/* 移动端：顶部测试类型和状态行 */}
                    <div className="flex items-center justify-between gap-2 sm:hidden">
                      <div className="flex items-center gap-3">
                        {/* 移动端测试类型指示器 - 统一h-14高度 */}
                        <div className="group-hover:scale-105 transition-transform">
                          {typeIndicator.element}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 font-heading text-sm truncate group-hover:text-blue-600 transition-colors">
                            {typeIndicator.fullName.split(' ')[0]}
                          </h3>
                          <div className="flex items-center text-xs text-gray-500 gap-1">
                            <Calendar size={11} className="flex-shrink-0" />
                            <span className="truncate">{testDate}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {/* 移动端：彩色圆点状态指示器 */}
                        <div className="flex-shrink-0 sm:hidden">
                          {report.isReportLocked ? (
                            <div className="w-3 h-3 rounded-full bg-amber-400 border-2 border-amber-100" title={t('status.locked')}></div>
                          ) : (
                            <div className="w-3 h-3 rounded-full bg-green-400 border-2 border-green-100" title={t('status.unlocked')}></div>
                          )}
                        </div>
                        {scoreDisplay && (
                          <div className="flex-shrink-0 group-hover:scale-105 transition-transform">
                            {scoreDisplay}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 桌面端：水平布局，所有元素在同一行且垂直居中 */}
                    <div className="hidden sm:flex sm:items-center sm:w-full sm:gap-4">
                      {/* 测试类型指示器 - 统一h-14高度 */}
                      <div className="group-hover:scale-105 transition-transform">
                        {typeIndicator.element}
                      </div>

                      {/* 测试信息 - 垂直居中 */}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 font-heading text-base truncate mb-1 group-hover:text-blue-600 transition-colors">
                          {typeIndicator.fullName}
                        </h3>
                        <div className="flex items-center text-sm text-gray-500 gap-1">
                          <Calendar size={13} className="flex-shrink-0" />
                          <span className="truncate">{testDate}</span>
                        </div>
                      </div>

                      {/* 桌面端：完整文字状态标签 */}
                      <div className="flex-shrink-0">
                        <span className={`inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium ${report.isReportLocked
                          ? 'bg-amber-50 text-amber-700 border border-amber-200'
                          : 'bg-green-50 text-green-700 border border-green-200'
                          }`}>
                          {report.isReportLocked ? (
                            <>
                              <Lock className="mr-2 h-4 w-4" />
                              {t('status.locked')}
                            </>
                          ) : (
                            <>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              {t('status.unlocked')}
                            </>
                          )}
                        </span>
                      </div>

                      {/* 分数显示 - 与icon高度一致 */}
                      {scoreDisplay && (
                        <div className="flex-shrink-0 group-hover:scale-105 transition-transform">
                          {scoreDisplay}
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        )}
      </div>
    </main>
  );
} 