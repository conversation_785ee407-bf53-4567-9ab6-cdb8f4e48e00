import { NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale, getTranslations } from 'next-intl/server';
import { Locale, locales } from '@/i18n/routing';
import { ReactNode } from 'react';
import { getBaseUrl } from '@/utils/metadata';
import PageViewTracker from "@/components/PageViewTracker";
import ClientLayout from '@/components/layout/ClientLayout';
import ClientProviders from '@/components/ClientProviders';

type Props = {
    children: ReactNode;
    params: Promise<{ locale: string }>;
    header: ReactNode;
    footer: ReactNode;
};

export function generateStaticParams() {
    return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
    params
}: {
    params: Promise<{ locale: string }>
}) {
    // 确保 params 已解析
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const baseUrl = await getBaseUrl();
    const t = await getTranslations('site');

    const alternateLinks: Record<string, string> = {};
    locales.forEach(lang => {
        const path = lang === 'en' ? '' : `/${lang}`;
        alternateLinks[lang] = `${baseUrl}${path}`;
    });

    return {
        title: {
            default: t('title'),
            template: `%s | ${t('title')}`
        },
        description: t('description'),
        metadataBase: new URL(baseUrl),
        alternates: {
            canonical: alternateLinks[locale],
            languages: alternateLinks
        },
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: "website",
            url: alternateLinks[locale],
            locale: locale,
            alternateLocale: locales.filter(l => l !== locale),
        },
        twitter: {
            card: "summary_large_image",
            title: t('title'),
            description: t('description')
        },
        other: {
            monetag: 'a214e8c629f8bdc9588e1e61d7128335',
            'google-adsense-account': 'ca-pub-****************'
        }
    };
}

export default async function LocaleLayout({
    children,
    params,
    header,
    footer,
}: Props) {
    // 确保 params 已解析
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    console.log('LocaleLayout rendering with path:', resolvedParams);
    console.log('Header component in LocaleLayout:', header ? 'present' : 'missing');
    console.log('Footer component in LocaleLayout:', footer ? 'present' : 'missing');

    // Enable static rendering
    setRequestLocale(locale);

    // Get messages for the locale
    const messages = await getMessages();

    return (
        <NextIntlClientProvider locale={locale} messages={messages}>
            <ClientProviders>
                <ClientLayout header={header} footer={footer}>
                    {children}
                </ClientLayout>
                <PageViewTracker />
            </ClientProviders>
        </NextIntlClientProvider>
    );
} 