import Link from 'next/link';
import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import {
  <PERSON>R<PERSON>,
  BrainCircuit,
  ClipboardList
} from 'lucide-react';
import AutismPageWrapper from './components/AutismPageWrapper';

interface PageProps {
  params: Promise<{
    locale: string;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  // Ensure params are resolved
  const resolvedParams = await params;
  const locale = resolvedParams.locale;

  const t = await getTranslations({ locale, namespace: 'pages.autism.metadata' });

  return {
    title: t('title'),
    description: t('description'),
  };
}

export default async function AutismPage({ params }: PageProps) {
  // Ensure params are resolved
  const resolvedParams = await params;
  const locale = resolvedParams.locale;

  const t = await getTranslations({ locale, namespace: 'pages.autism' });

  return (
    <AutismPageWrapper>
      <main className="pt-20 sm:pt-24 md:pt-28 pb-12 max-w-5xl mx-auto px-4">
        <section className="mb-12 text-center">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gradient">
            {t('content.title')}
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('content.description')}
          </p>
        </section>

        <section className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition duration-300 flex flex-col h-full">
            <div>
              <div className="flex items-baseline mb-4">
                <BrainCircuit className="w-5 h-5 text-indigo-600 dark:text-indigo-400 mr-3 flex-shrink-0 mt-0.5" />
                <h2 className="text-xl font-bold leading-none">RAADS-R</h2>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {t('content.raadsrDescription')}
              </p>
              <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-md mb-6 text-sm">
                <p className="mb-1">{t('tests.raadsr.description')}</p>
              </div>
            </div>
            <div className="mt-auto">
              <Link href={`/${locale}/autism/raadsr`} className="inline-flex items-center justify-center w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md transition duration-300">
                {t('tests.raadsr.button')}
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition duration-300 flex flex-col h-full">
            <div>
              <div className="flex items-baseline mb-4">
                <ClipboardList className="w-5 h-5 text-emerald-600 dark:text-emerald-400 mr-3 flex-shrink-0 mt-0.5" />
                <h2 className="text-xl font-bold leading-none">AQ-10</h2>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {t('content.aq10Description')}
              </p>
              <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-md mb-6 text-sm">
                <p className="mb-1">{t('tests.aq10.description')}</p>
              </div>
            </div>
            <div className="mt-auto">
              <Link href={`/${locale}/autism/aq10`} className="inline-flex items-center justify-center w-full bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-2 px-4 rounded-md transition duration-300">
                {t('tests.aq10.button')}
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </div>
          </div>
        </section>

        <section className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6 mb-12">
          <div className="flex items-start">
            <div>
              <h2 className="text-xl font-bold mb-2">{t('disclaimer.title')}</h2>
              <p className="text-gray-600 dark:text-gray-300">
                {t('disclaimer.content')}
              </p>
            </div>
          </div>
        </section>
      </main>
    </AutismPageWrapper>
  );
} 