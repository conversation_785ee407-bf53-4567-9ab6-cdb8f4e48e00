import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import RaadsrTestWrapper from '../components/RaadsrTestWrapper';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  // 确保 params 已解析
  const resolvedParams = await params;
  const locale = resolvedParams.locale;

  const t = await getTranslations({ locale, namespace: 'pages.autism' });

  return {
    title: `RAADS-R ${t('metadata.title')}`,
    description: t('metadata.description'),
  };
}

export default function RaadsrTestPage() {
  return <RaadsrTestWrapper />;
} 