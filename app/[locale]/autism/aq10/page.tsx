import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import Aq10Test from '../components/Aq10Test';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  // 确保 params 已解析
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  const t = await getTranslations({ locale, namespace: 'pages.autism' });
  
  return {
    title: `AQ-10 ${t('metadata.title')}`,
    description: t('metadata.description'),
  };
}

export default function Aq10TestPage() {
  return <Aq10Test />;
} 