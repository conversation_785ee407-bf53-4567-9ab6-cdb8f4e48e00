"use client"

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';
import { ChevronLeft, ChevronRight, Send, ExternalLink, RefreshCcw, User } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useReferralTracking } from '@/hooks/useReferralTracking';

// Define types for questions and answers
type ResponseValue = 0 | 1 | 2 | 3;
type QuestionCategory = 'language' | 'social' | 'sensoryMotor' | 'interests';

interface Question {
  id: number;
  text: string;
  category: QuestionCategory;
  isReversed?: boolean; // For normative questions with reversed scoring
}

export default function RaadsrTest() {
  const t = useTranslations('pages.autism');
  const router = useRouter();
  const locale = useLocale();
  const { data: session } = useSession();
  const { getReferralForSubmission } = useReferralTracking();

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<number, ResponseValue>>({});
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [submitError, setSubmitError] = useState('');
  const [reportLoading, setReportLoading] = useState(false);
  const [isIframe, setIsIframe] = useState(false);
  const [showResultModal, setShowResultModal] = useState(false);
  const [resultUrl, setResultUrl] = useState('');

  // 检查用户是否已登录并设置邮箱
  useEffect(() => {
    if (session?.user?.email) {
      setEmail(session.user.email);
    }
  }, [session]);

  // Check if we're in iframe mode based on URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsIframe(window.location.pathname.includes('/i/autism'));
    }
  }, []);

  // Define RAADS-R questions with their categories - Complete 80-question implementation
  // Language section (7 questions)
  // Social Relatedness section (39 questions) - now complete with all standard RAADS-R questions
  // Sensory-Motor section (20 questions)
  // Circumscribed Interests section (14 questions)
  // Total: 80 questions (matching the standard RAADS-R)
  const questions: Question[] = [
    // Language questions (7 total)
    { id: 1, text: 'questions.language.q1', category: 'language' },
    { id: 2, text: 'questions.language.q2', category: 'language' },
    { id: 3, text: 'questions.language.q3', category: 'language', isReversed: true },
    { id: 4, text: 'questions.language.q4', category: 'language' },
    { id: 5, text: 'questions.language.q5', category: 'language' },
    { id: 6, text: 'questions.language.q6', category: 'language' },
    { id: 7, text: 'questions.language.q7', category: 'language' },

    // Social Relatedness questions (all 39 questions - complete RAADS-R implementation)
    { id: 8, text: 'questions.social.q1', category: 'social', isReversed: true },
    { id: 9, text: 'questions.social.q2', category: 'social' },
    { id: 10, text: 'questions.social.q3', category: 'social' },
    { id: 11, text: 'questions.social.q4', category: 'social', isReversed: true },
    { id: 12, text: 'questions.social.q5', category: 'social', isReversed: true },
    { id: 13, text: 'questions.social.q6', category: 'social' },
    { id: 14, text: 'questions.social.q7', category: 'social' },
    { id: 15, text: 'questions.social.q8', category: 'social', isReversed: true },
    { id: 16, text: 'questions.social.q9', category: 'social', isReversed: true },
    { id: 17, text: 'questions.social.q10', category: 'social' },
    { id: 18, text: 'questions.social.q11', category: 'social', isReversed: true },
    { id: 19, text: 'questions.social.q12', category: 'social' },
    { id: 20, text: 'questions.social.q13', category: 'social' },
    { id: 21, text: 'questions.social.q14', category: 'social', isReversed: true },
    { id: 22, text: 'questions.social.q15', category: 'social', isReversed: true },
    { id: 23, text: 'questions.social.q16', category: 'social' },
    { id: 24, text: 'questions.social.q17', category: 'social' },
    { id: 25, text: 'questions.social.q18', category: 'social', isReversed: true },
    { id: 26, text: 'questions.social.q19', category: 'social', isReversed: true },
    { id: 27, text: 'questions.social.q20', category: 'social', isReversed: true },
    { id: 28, text: 'questions.social.q21', category: 'social' },
    { id: 29, text: 'questions.social.q22', category: 'social' },
    { id: 30, text: 'questions.social.q23', category: 'social' },
    { id: 31, text: 'questions.social.q24', category: 'social' },
    { id: 32, text: 'questions.social.q25', category: 'social', isReversed: true },
    { id: 33, text: 'questions.social.q26', category: 'social' },
    { id: 34, text: 'questions.social.q27', category: 'social', isReversed: true },
    { id: 35, text: 'questions.social.q28', category: 'social' },
    { id: 36, text: 'questions.social.q29', category: 'social' },
    { id: 37, text: 'questions.social.q30', category: 'social' },
    // Additional 9 social questions to complete the 39 total
    { id: 38, text: 'questions.social.q31', category: 'social', isReversed: true },
    { id: 39, text: 'questions.social.q32', category: 'social', isReversed: true },
    { id: 40, text: 'questions.social.q33', category: 'social', isReversed: true },
    { id: 41, text: 'questions.social.q34', category: 'social', isReversed: true },
    { id: 42, text: 'questions.social.q35', category: 'social', isReversed: true },
    { id: 43, text: 'questions.social.q36', category: 'social', isReversed: true },
    { id: 44, text: 'questions.social.q37', category: 'social', isReversed: true },
    { id: 45, text: 'questions.social.q38', category: 'social', isReversed: true },
    { id: 46, text: 'questions.social.q39', category: 'social', isReversed: true },

    // Sensory-Motor questions (all 20 questions)
    { id: 47, text: 'questions.sensoryMotor.q1', category: 'sensoryMotor' },
    { id: 48, text: 'questions.sensoryMotor.q2', category: 'sensoryMotor', isReversed: true },
    { id: 49, text: 'questions.sensoryMotor.q3', category: 'sensoryMotor' },
    { id: 50, text: 'questions.sensoryMotor.q4', category: 'sensoryMotor' },
    { id: 51, text: 'questions.sensoryMotor.q5', category: 'sensoryMotor' },
    { id: 52, text: 'questions.sensoryMotor.q6', category: 'sensoryMotor' },
    { id: 53, text: 'questions.sensoryMotor.q7', category: 'sensoryMotor' },
    { id: 54, text: 'questions.sensoryMotor.q8', category: 'sensoryMotor' },
    { id: 55, text: 'questions.sensoryMotor.q9', category: 'sensoryMotor', isReversed: true },
    { id: 56, text: 'questions.sensoryMotor.q10', category: 'sensoryMotor' },
    { id: 57, text: 'questions.sensoryMotor.q11', category: 'sensoryMotor' },
    { id: 58, text: 'questions.sensoryMotor.q12', category: 'sensoryMotor' },
    { id: 59, text: 'questions.sensoryMotor.q13', category: 'sensoryMotor' },
    { id: 60, text: 'questions.sensoryMotor.q14', category: 'sensoryMotor' },
    { id: 61, text: 'questions.sensoryMotor.q15', category: 'sensoryMotor' },
    { id: 62, text: 'questions.sensoryMotor.q16', category: 'sensoryMotor' },
    { id: 63, text: 'questions.sensoryMotor.q17', category: 'sensoryMotor' },
    { id: 64, text: 'questions.sensoryMotor.q18', category: 'sensoryMotor' },
    { id: 65, text: 'questions.sensoryMotor.q19', category: 'sensoryMotor' },
    { id: 66, text: 'questions.sensoryMotor.q20', category: 'sensoryMotor' },

    // Circumscribed Interests questions (all 14)
    { id: 67, text: 'questions.interests.q1', category: 'interests' },
    { id: 68, text: 'questions.interests.q2', category: 'interests' },
    { id: 69, text: 'questions.interests.q3', category: 'interests' },
    { id: 70, text: 'questions.interests.q4', category: 'interests' },
    { id: 71, text: 'questions.interests.q5', category: 'interests' },
    { id: 72, text: 'questions.interests.q6', category: 'interests' },
    { id: 73, text: 'questions.interests.q7', category: 'interests' },
    { id: 74, text: 'questions.interests.q8', category: 'interests' },
    { id: 75, text: 'questions.interests.q9', category: 'interests' },
    { id: 76, text: 'questions.interests.q10', category: 'interests' },
    { id: 77, text: 'questions.interests.q11', category: 'interests' },
    { id: 78, text: 'questions.interests.q12', category: 'interests' },
    { id: 79, text: 'questions.interests.q13', category: 'interests' },
    { id: 80, text: 'questions.interests.q14', category: 'interests' }
  ];

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];

  // RAADS-R has 4 response options with different scores
  const responseOptions = [
    { value: 3, key: 'trueNowAndWhenYoung', label: t('test.responseOptions.trueNowAndWhenYoung') },
    { value: 2, key: 'trueNowOnly', label: t('test.responseOptions.trueNowOnly') },
    { value: 1, key: 'trueWhenYoungOnly', label: t('test.responseOptions.trueWhenYoungOnly') },
    { value: 0, key: 'neverTrue', label: t('test.responseOptions.neverTrue') }
  ];

  // For reversed scoring questions (normative questions)
  const reversedResponseOptions = [
    { value: 0, key: 'trueNowAndWhenYoung', label: t('test.responseOptions.trueNowAndWhenYoung') },
    { value: 1, key: 'trueNowOnly', label: t('test.responseOptions.trueNowOnly') },
    { value: 2, key: 'trueWhenYoungOnly', label: t('test.responseOptions.trueWhenYoungOnly') },
    { value: 3, key: 'neverTrue', label: t('test.responseOptions.neverTrue') }
  ];

  // Handle answer selection
  const handleAnswerSelect = (questionId: number, value: ResponseValue) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // Calculate scores
  const calculateScores = () => {
    // Calculate subscale scores
    const languageQuestions = questions.filter(q => q.category === 'language');
    const socialQuestions = questions.filter(q => q.category === 'social');
    const sensoryMotorQuestions = questions.filter(q => q.category === 'sensoryMotor');
    const interestsQuestions = questions.filter(q => q.category === 'interests');

    const languageScore = languageQuestions.reduce((sum, q) => {
      const answer = answers[q.id];
      // For reversed scoring questions, the value is already correctly set in handleAnswerSelect
      return sum + (answer !== undefined ? answer : 0);
    }, 0);

    const socialScore = socialQuestions.reduce((sum, q) => {
      const answer = answers[q.id];
      return sum + (answer !== undefined ? answer : 0);
    }, 0);

    const sensoryMotorScore = sensoryMotorQuestions.reduce((sum, q) => {
      const answer = answers[q.id];
      return sum + (answer !== undefined ? answer : 0);
    }, 0);

    const interestsScore = interestsQuestions.reduce((sum, q) => {
      const answer = answers[q.id];
      return sum + (answer !== undefined ? answer : 0);
    }, 0);

    const totalScore = languageScore + socialScore + sensoryMotorScore + interestsScore;

    return {
      language: {
        score: languageScore,
        max: languageQuestions.length * 3, // Max score is 3 per question
        significant: languageScore >= 4 // Threshold for significance
      },
      social: {
        score: socialScore,
        max: socialQuestions.length * 3,
        significant: socialScore >= 31
      },
      sensoryMotor: {
        score: sensoryMotorScore,
        max: sensoryMotorQuestions.length * 3,
        significant: sensoryMotorScore >= 16
      },
      interests: {
        score: interestsScore,
        max: interestsQuestions.length * 3,
        significant: interestsScore >= 15
      },
      total: {
        score: totalScore,
        max: questions.length * 3,
        significant: totalScore >= 65 // Clinical threshold
      }
    };
  };

  // Get result interpretation
  const getResultInterpretation = (score: number) => {
    if (score < 50) {
      return 'result.raadsr.notSignificant';
    } else if (score < 65) {
      return 'result.raadsr.notSignificant';
    } else if (score < 90) {
      return 'result.raadsr.significant';
    } else if (score < 130) {
      return 'result.raadsr.significant';
    } else if (score < 161) {
      return 'result.raadsr.significant';
    } else {
      return 'result.raadsr.significant';
    }
  };

  // Move to next question
  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      // Scroll to top of test container
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          const testContainer = document.getElementById('raadsr-test-container');
          if (testContainer) {
            testContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
    } else {
      // All questions answered, show email form
      setShowEmailForm(true);
    }
  };

  // Go back to previous question
  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Handle email change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
  };

  // Validate email format
  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  // Submit email to get results
  const submitEmail = async () => {
    // Clear any previous errors
    setEmailError('');
    setSubmitError('');

    // Validate email
    if (!email) {
      setEmailError(t('emailForm.errors.required'));
      return;
    }

    if (!validateEmail(email)) {
      setEmailError(t('emailForm.errors.invalid'));
      return;
    }

    // Calculate final scores
    const scores = calculateScores();

    // Set loading state
    setReportLoading(true);

    try {
      console.log('Submitting RAADS-R test results...');

      // Create request data
      const requestData = {
        type: 'raadsrTest',
        email,
        answers,
        score: scores,
        userId: session?.user?.id,
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          language: navigator.language,
          locale,
          ...(getReferralForSubmission() && { referredBy: getReferralForSubmission() })
        }
      };

      console.log('Request data:', requestData);

      // Send data to API
      const response = await fetch('/api/autism/save-result', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        throw new Error(errorData.message || 'Failed to submit test');
      }

      const data = await response.json();
      console.log('API response data:', data);

      // 获取结果页面URL
      const testId = data.testId;
      if (testId) {
        console.log('Got test ID:', testId);
        // 使用后端返回的 reportUrl（带 accessToken）
        const resultPageUrl = data.reportUrl;
        setResultUrl(resultPageUrl);
        window.open(resultPageUrl, '_blank');
        setShowResultModal(true);
        setReportLoading(false);
      } else {
        console.error('No testId returned from API');
        throw new Error('No test ID returned from server');
      }
    } catch (error) {
      console.error('Error submitting test:', error);
      setReportLoading(false);
      setSubmitError(error instanceof Error ? error.message : 'An error occurred while submitting the test');
    }
  };

  // 重新开始测试
  const restartTest = () => {
    setCurrentQuestionIndex(0);
    setAnswers({});
    setShowEmailForm(false);
    setEmail('');
    setEmailError('');
    setSubmitError('');
    setShowResultModal(false);
    setResultUrl('');
  };

  // 结果蒙层弹窗
  const getResultModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
          <h2 className="text-xl font-bold mb-4 text-center">
            {t('result.modalTitle') || '测试完成'}
          </h2>

          <p className="mb-6 text-center">
            {t('result.modalDescription') || '您的测试结果已在新窗口中打开。如果您没有看到结果页面，请点击下方按钮再次查看。'}
          </p>

          <div className="flex flex-col space-y-4">
            <button
              onClick={() => window.open(resultUrl, '_blank')}
              className="flex items-center justify-center px-4 py-3 rounded-md bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 text-white font-medium"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              {t('result.openResultButton') || '查看测试结果'}
            </button>

            <button
              onClick={restartTest}
              className="flex items-center justify-center px-4 py-3 rounded-md bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium"
            >
              <RefreshCcw className="mr-2 h-4 w-4" />
              {t('result.restartButton') || '重新测试'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Display question
  const getQuestionSection = () => {
    return (
      <div className="mb-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-6">
            {t(currentQuestion.text)}
          </h3>

          <div className="space-y-3">
            {(currentQuestion.isReversed ? reversedResponseOptions : responseOptions).map((option) => (
              <div
                key={option.key}
                className="flex items-center"
              >
                <input
                  type="radio"
                  id={`option-${option.key}`}
                  name={`question-${currentQuestion.id}`}
                  checked={answers[currentQuestion.id] === option.value}
                  onChange={() => handleAnswerSelect(currentQuestion.id, option.value as ResponseValue)}
                  className="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                />
                <label
                  htmlFor={`option-${option.key}`}
                  className="ml-3 block text-gray-700 dark:text-gray-300"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Email submission form
  const getEmailForm = () => {
    const scores = calculateScores();
    const interpretation = getResultInterpretation(scores.total.score);
    const isLoggedIn = !!session?.user;

    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-bold mb-4">
          {t('emailForm.title')}
        </h2>

        <p className="mb-6 text-gray-600 dark:text-gray-300">
          {t('emailForm.description')}
        </p>

        <div className="mb-6">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t('emailForm.emailLabel')}
          </label>
          <div className="relative">
            <input
              type="email"
              id="email"
              value={email}
              onChange={handleEmailChange}
              placeholder={t('emailForm.emailPlaceholder')}
              className="w-full p-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              disabled={reportLoading}
            />
            {isLoggedIn && (
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              </div>
            )}
          </div>
          {isLoggedIn && (
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 flex items-center">
              <span className="inline-flex items-center justify-center p-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </span>
              {t('emailForm.loggedInMessage') || "Using your account email"}
            </p>
          )}
          {emailError && (
            <p className="mt-1 text-red-600 dark:text-red-400 text-sm">
              {emailError}
            </p>
          )}
        </div>



        <div className="mb-6">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t('emailForm.privacyNote')}
          </p>
        </div>

        {submitError && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-600 dark:text-red-400 text-sm">
            {submitError}
          </div>
        )}

        <button
          onClick={submitEmail}
          disabled={reportLoading}
          className={`w-full flex items-center justify-center px-4 py-2 rounded-md text-white font-medium ${reportLoading
            ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
            : 'bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600'
            }`}
        >
          {reportLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t('emailForm.processing')}
            </>
          ) : (
            <>
              <Send className="mr-2 h-4 w-4" />
              {t('emailForm.submitButton')}
            </>
          )}
        </button>
      </div>
    );
  };

  return (
    <main className={`container mx-auto px-4 ${isIframe ? 'py-4' : 'pt-20 sm:pt-24 md:pt-28 pb-12'} max-w-3xl`}>
      <div id="raadsr-test-container">
        <section className="mb-8 text-center">
          <h1 className="text-2xl md:text-3xl font-bold mb-4">
            {t('test.title')}
          </h1>

          {!showEmailForm && !showResultModal && (
            <div className="text-gray-600 dark:text-gray-300 mb-4">
              <p className="mb-1">
                {t('test.questionProgress', { current: currentQuestionIndex + 1, total: questions.length })}
              </p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-4">
                <div
                  className="bg-indigo-600 dark:bg-indigo-500 h-2.5 rounded-full"
                  style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </section>

        {!showEmailForm && !showResultModal ? (
          <>
            {getQuestionSection()}

            <div className="flex justify-between mt-6">
              <button
                onClick={previousQuestion}
                disabled={currentQuestionIndex === 0}
                className={`px-4 py-2 rounded-md flex items-center ${currentQuestionIndex === 0
                  ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                  }`}
              >
                <ChevronLeft className="mr-1 h-5 w-5" />
                {t('test.previousButton')}
              </button>

              <button
                onClick={nextQuestion}
                disabled={answers[currentQuestion.id] === undefined}
                className={`px-4 py-2 rounded-md flex items-center ${answers[currentQuestion.id] === undefined
                  ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 text-white'
                  }`}
              >
                {t('test.nextButton')}
                <ChevronRight className="ml-1 h-5 w-5" />
              </button>
            </div>
          </>
        ) : showEmailForm && !showResultModal ? (
          getEmailForm()
        ) : null}

        {showResultModal && getResultModal()}
      </div>
    </main>
  );
} 