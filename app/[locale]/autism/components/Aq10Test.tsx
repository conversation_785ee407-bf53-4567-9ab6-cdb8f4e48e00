"use client"

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';
import { ChevronLeft, ChevronRight, Send, ExternalLink, RefreshCcw, User } from 'lucide-react';
import { useSession } from 'next-auth/react';

// 定义AQ-10评估的类型
interface Question {
  id: number;
  text: string;
  reverseScoring: boolean; // 反向评分的问题
}

// 回答选项
type AnswerOption = 'definitelyAgree' | 'slightlyAgree' | 'slightlyDisagree' | 'definitelyDisagree';

export default function Aq10Test() {
  const t = useTranslations('pages.autism');
  const router = useRouter();
  const locale = useLocale();
  const { data: session } = useSession();

  // 当前问题索引
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  // 存储回答
  const [answers, setAnswers] = useState<Record<number, AnswerOption>>({});

  // 邮箱表单状态
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [submitError, setSubmitError] = useState('');
  const [reportLoading, setReportLoading] = useState(false);
  const [isIframe, setIsIframe] = useState(false);

  // 结果状态
  const [showResultModal, setShowResultModal] = useState(false);
  const [resultUrl, setResultUrl] = useState('');
  const [submitMessage, setSubmitMessage] = useState('');

  // 检查用户是否已登录并设置邮箱
  useEffect(() => {
    if (session?.user?.email) {
      setEmail(session.user.email);
    }
  }, [session]);

  // 检测是否在iframe中展示
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsIframe(window.location.pathname.includes('/i/autism'));
    }
  }, []);

  // AQ-10问题列表，基于研究文献
  const questions: Question[] = [
    { id: 1, text: 'aq10.q1', reverseScoring: false }, // "I often notice small sounds when others do not"
    { id: 2, text: 'aq10.q2', reverseScoring: false }, // "I usually concentrate more on the whole picture, rather than the small details"
    { id: 3, text: 'aq10.q3', reverseScoring: true }, // "I find it easy to do more than one thing at once"
    { id: 4, text: 'aq10.q4', reverseScoring: true }, // "If there is an interruption, I can switch back to what I was doing very quickly"
    { id: 5, text: 'aq10.q5', reverseScoring: false }, // "I find it difficult to work out people's intentions"
    { id: 6, text: 'aq10.q6', reverseScoring: true }, // "I know how to tell if someone listening to me is getting bored"
    { id: 7, text: 'aq10.q7', reverseScoring: true }, // "When I'm reading a story, I find it difficult to work out the characters' intentions"
    { id: 8, text: 'aq10.q8', reverseScoring: true }, // "I like to collect information about categories of things"
    { id: 9, text: 'aq10.q9', reverseScoring: true }, // "I find it easy to work out what someone is thinking or feeling just by looking at their face"
    { id: 10, text: 'aq10.q10', reverseScoring: false }, // "I find it difficult to work out people's intentions"
  ];

  // 获取当前问题
  const currentQuestion = questions[currentQuestionIndex];

  // 回答选项
  const answerOptions = [
    { value: 'definitelyAgree', label: t('aq10.responseOptions.definitelyAgree') },
    { value: 'slightlyAgree', label: t('aq10.responseOptions.slightlyAgree') },
    { value: 'slightlyDisagree', label: t('aq10.responseOptions.slightlyDisagree') },
    { value: 'definitelyDisagree', label: t('aq10.responseOptions.definitelyDisagree') }
  ];

  // 处理答案选择
  const handleAnswerSelect = (questionId: number, value: AnswerOption) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // 计算得分 - AQ-10使用二分法计分，无论选择"完全"还是"稍微"都一样
  const calculateScore = () => {
    let totalScore = 0;

    Object.entries(answers).forEach(([questionIdStr, answer]) => {
      const questionId = parseInt(questionIdStr);
      const question = questions.find(q => q.id === questionId);

      if (!question) return;

      const isAgree = answer === 'definitelyAgree' || answer === 'slightlyAgree';

      // 评分规则：
      // 对于正向题，同意（非常/略微）得1分，不同意得0分
      // 对于反向题，不同意（非常/略微）得1分，同意得0分
      if ((isAgree && !question.reverseScoring) || (!isAgree && question.reverseScoring)) {
        totalScore += 1;
      }
    });

    return {
      score: totalScore,
      max: questions.length,
      significant: totalScore >= 6 // AQ-10的临床阈值是6分
    };
  };

  // 前往下一题
  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      // 滚动到测试容器顶部
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          const testContainer = document.getElementById('aq10-test-container');
          if (testContainer) {
            testContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
    } else {
      // 所有问题已回答，显示邮箱表单
      setShowEmailForm(true);
    }
  };

  // 返回上一题
  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // 处理邮箱变更
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
  };

  // 验证邮箱格式
  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  // 提交邮箱获取结果
  const submitEmail = async () => {
    // 清除之前的错误
    setEmailError('');
    setSubmitError('');

    // 验证邮箱
    if (!email) {
      setEmailError(t('emailForm.errors.required'));
      return;
    }

    if (!validateEmail(email)) {
      setEmailError(t('emailForm.errors.invalid'));
      return;
    }

    // 计算最终得分
    const score = calculateScore();

    // 设置加载状态
    setReportLoading(true);

    try {
      console.log('提交AQ-10测试结果...');

      // 创建请求数据
      const requestData = {
        type: 'aq10Test',
        email,
        answers,
        score,
        userId: session?.user?.id, // 添加用户ID，如果用户已登录
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          language: navigator.language,
          locale
        }
      };

      console.log('请求数据:', requestData);

      // 发送数据到API
      const response = await fetch('/api/autism/save-result', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('API响应状态:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API错误:', errorData);
        throw new Error(errorData.message || '提交测试失败');
      }

      const data = await response.json();
      console.log('API响应数据:', data);

      // 获取结果页面URL
      if (data.testId) {
        // 使用后端返回的 reportUrl（带 accessToken）
        const resultPageUrl = data.reportUrl;
        setResultUrl(resultPageUrl);
        window.open(resultPageUrl, '_blank');
        setShowResultModal(true);
        setReportLoading(false);
      } else {
        console.error('API未返回testId');
        throw new Error('服务器未返回测试ID');
      }
    } catch (error) {
      console.error('提交测试时出错:', error);
      setReportLoading(false);
      setSubmitError(error instanceof Error ? error.message : '提交测试时发生错误');
    }
  };

  // 重新开始测试
  const restartTest = () => {
    setCurrentQuestionIndex(0);
    setAnswers({});
    setShowEmailForm(false);
    setEmail('');
    setEmailError('');
    setSubmitError('');
    setSubmitMessage('');
    setShowResultModal(false);
    setResultUrl('');
  };

  // 问题部分UI
  const getQuestionSection = () => {
    return (
      <div className="mb-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-6">
            {t(currentQuestion.text)}
          </h3>

          <div className="space-y-3">
            {answerOptions.map((option) => (
              <div
                key={option.value}
                className="flex items-center"
              >
                <input
                  type="radio"
                  id={`option-${option.value}`}
                  name={`question-${currentQuestion.id}`}
                  checked={answers[currentQuestion.id] === option.value}
                  onChange={() => handleAnswerSelect(currentQuestion.id, option.value as AnswerOption)}
                  className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
                />
                <label
                  htmlFor={`option-${option.value}`}
                  className="ml-3 block text-gray-700 dark:text-gray-300"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // 邮箱提交表单UI
  const getEmailForm = () => {
    const score = calculateScore();
    const isLoggedIn = !!session?.user;

    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-bold mb-4">
          {t('emailForm.title')}
        </h2>

        <p className="mb-6 text-gray-600 dark:text-gray-300">
          {t('emailForm.description')}
        </p>

        <div className="mb-6">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t('emailForm.emailLabel')}
          </label>
          <div className="relative">
            <input
              type="email"
              id="email"
              value={email}
              onChange={handleEmailChange}
              placeholder={t('emailForm.emailPlaceholder')}
              className="w-full p-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              disabled={reportLoading}
            />
            {isLoggedIn && (
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              </div>
            )}
          </div>
          {isLoggedIn && (
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 flex items-center">
              <span className="inline-flex items-center justify-center p-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </span>
              {t('emailForm.loggedInMessage') || "Using your account email"}
            </p>
          )}
          {emailError && (
            <p className="mt-1 text-red-600 dark:text-red-400 text-sm">
              {emailError}
            </p>
          )}
        </div>



        <div className="mb-6">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t('emailForm.privacyNote')}
          </p>
        </div>

        {submitError && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-600 dark:text-red-400 text-sm">
            {submitError}
          </div>
        )}

        <button
          onClick={submitEmail}
          disabled={reportLoading}
          className={`w-full flex items-center justify-center px-4 py-2 rounded-md text-white font-medium ${reportLoading
            ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
            : 'bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600'
            }`}
        >
          {reportLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t('emailForm.processing') || 'Processing...'}
            </>
          ) : (
            <>
              <Send className="mr-2 h-4 w-4" />
              {t('emailForm.submitButton')}
            </>
          )}
        </button>
      </div>
    );
  };

  // 结果蒙层弹窗
  const getResultModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
          <h2 className="text-xl font-bold mb-4 text-center">
            {t('result.modalTitle') || '测试完成'}
          </h2>

          <p className="mb-6 text-center">
            {t('result.modalDescription') || '您的测试结果已在新窗口中打开。如果您没有看到结果页面，请点击下方按钮再次查看。'}
          </p>

          {/* 显示提交消息，如果有的话 */}
          {submitMessage && (
            <div className="mb-6 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md text-green-600 dark:text-green-400 text-sm flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {submitMessage}
            </div>
          )}

          <div className="flex flex-col space-y-4">
            <button
              onClick={() => window.open(resultUrl, '_blank')}
              className="flex items-center justify-center px-4 py-3 rounded-md bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white font-medium"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              {t('result.openResultButton') || '查看测试结果'}
            </button>

            <button
              onClick={restartTest}
              className="flex items-center justify-center px-4 py-3 rounded-md bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium"
            >
              <RefreshCcw className="mr-2 h-4 w-4" />
              {t('result.restartButton') || '重新测试'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <main className={`container mx-auto px-4 ${isIframe ? 'py-4' : 'pt-20 sm:pt-24 md:pt-28 pb-12'} max-w-3xl`}>
      <div id="aq10-test-container">
        <section className="mb-8 text-center">
          <h1 className="text-2xl md:text-3xl font-bold mb-4">
            AQ-10 {t('test.title')}
          </h1>

          {!showEmailForm && !showResultModal && (
            <div className="text-gray-600 dark:text-gray-300 mb-4">
              <p className="mb-1">
                {t('test.questionProgress', { current: currentQuestionIndex + 1, total: questions.length })}
              </p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-4">
                <div
                  className="bg-emerald-600 dark:bg-emerald-500 h-2.5 rounded-full"
                  style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </section>

        {!showEmailForm && !showResultModal ? (
          <>
            {getQuestionSection()}

            <div className="flex justify-between mt-6">
              <button
                onClick={previousQuestion}
                disabled={currentQuestionIndex === 0}
                className={`px-4 py-2 rounded-md flex items-center ${currentQuestionIndex === 0
                  ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                  }`}
              >
                <ChevronLeft className="mr-1 h-5 w-5" />
                {t('test.previousButton') || 'Previous'}
              </button>

              <button
                onClick={nextQuestion}
                disabled={answers[currentQuestion.id] === undefined}
                className={`px-4 py-2 rounded-md flex items-center ${answers[currentQuestion.id] === undefined
                  ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white'
                  }`}
              >
                {t('test.nextButton') || 'Next'}
                <ChevronRight className="ml-1 h-5 w-5" />
              </button>
            </div>
          </>
        ) : showEmailForm && !showResultModal ? (
          getEmailForm()
        ) : null}

        {showResultModal && getResultModal()}
      </div>
    </main>
  );
} 