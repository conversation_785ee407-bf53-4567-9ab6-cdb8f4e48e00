'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { AlertCircle, ArrowLeft } from 'lucide-react';

export default function TestResultNotFound() {
  const t = useTranslations('pages.autism');
  const params = useParams();
  const locale = params?.locale as string || 'en';

  // Helper function to build locale-aware URLs
  const getLocalizedPath = (path: string) => {
    return locale === 'en' ? path : `/${locale}${path}`;
  };

  return (
    <main className="container mx-auto px-4 py-12 flex flex-col items-center justify-center max-w-3xl">
      <div className="w-full text-center">
        <div className="mb-6 inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30">
          <AlertCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>

        <h1 className="text-2xl md:text-3xl font-bold mb-4">
          {t('results.notFound.title') || 'Test Result Not Found'}
        </h1>

        <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-lg mx-auto">
          {t('results.notFound.description') || 'The test result you are looking for does not exist or has expired. Please take the test again to generate a new result.'}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href={getLocalizedPath('/autism')}
            className="inline-flex items-center justify-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md transition-colors shadow-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('results.notFound.backToTests') || 'Back to Tests'}
          </Link>
        </div>
      </div>
    </main>
  );
} 