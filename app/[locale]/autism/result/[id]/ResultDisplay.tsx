"use client"

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

import { AlertTriangle, FileText, CheckCircle, RefreshCw, BrainCircuit, ClipboardList } from 'lucide-react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import type { TestResult } from '@/types/prisma';

// 动态导入报告组件，避免在初始加载时导入
const AutismReport = dynamic(() => import('@/app/components/AutismReport'), {
  loading: () => (
    <div className="bg-white rounded-xl shadow-sm p-4 md:p-6 border border-gray-100 animate-pulse">
      <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    </div>
  ),
  ssr: false
});



interface ResultDisplayProps {
  testResult: TestResult;
  locale: string;
}

export default function ResultDisplay({ testResult, locale }: ResultDisplayProps) {
  const t = useTranslations('pages.autism');
  const { data: session } = useSession();
  const [loaded, setLoaded] = useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [regenerateKey, setRegenerateKey] = useState(0);
  const [shouldRegenerate, setShouldRegenerate] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  const router = useRouter();

  useEffect(() => {
    setLoaded(true);
    const timer = setTimeout(() => {
      setIsFirstLoad(false);
    }, 1000);

    // Trigger pre-generation of report in background
    const preGenerateReport = async () => {
      try {
        const testType = testResult.testType === 'raadsrTest' ? 'raadsr' : 'aq10';
        await fetch('/api/autism/pre-generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            testId: testResult.id,
            testType,
            answers: testResult.answers,
            scores: testResult.scores,
            locale
          })
        });
      } catch (error) {
        console.error('Pre-generation failed:', error);
      }
    };

    preGenerateReport();

    return () => clearTimeout(timer);
  }, [testResult.id, testResult.testType, testResult.answers, testResult.scores, locale]);



  const handleGenerationStateChange = (isGenerating: boolean) => {
    setIsGeneratingReport(isGenerating);
    if (!isGenerating && shouldRegenerate) {
      setShouldRegenerate(false);
    }
  };

  const handleRegenerateReport = () => {
    if (isGeneratingReport) return;
    const newKey = Date.now();
    setShouldRegenerate(true);
    setRegenerateKey(newKey);
  };

  const handleRetakeTest = () => {
    const testType = testResult.testType === 'raadsrTest' ? 'raadsr' : 'aq10';
    router.push(`/${locale}/autism/${testType}`);
  };



  if (!loaded) {
    return (
      <div className="p-4 md:p-6 bg-white border border-gray-100 rounded-xl shadow-sm">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // 渲染测试结果
  const renderTestResults = () => {
    if (testResult.testType === 'raadsrTest') {
      // RAADS-R 测试结果
      const { language, social, sensoryMotor, interests, total } = testResult.scores || {};

      // 如果没有总分数据，显示基本信息
      if (!total) {
        return (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
            <div className="flex items-center gap-3 mb-4 md:mb-6">
              <div className="flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                <BrainCircuit className="h-5 w-5 md:h-6 md:w-6 text-indigo-600" />
              </div>
              <div>
                <h2 className="text-lg md:text-xl lg:text-2xl font-heading font-bold text-gray-900">
                  RAADS-R {t('result.resultTitle')}
                </h2>
                <p className="text-sm md:text-base text-gray-600 mt-1">
                  {t('result.loading')}
                </p>
              </div>
            </div>
          </div>
        );
      }

      return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
          <div className="flex items-center gap-3 mb-6 md:mb-8">
            <div className="flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
              <BrainCircuit className="h-5 w-5 md:h-6 md:w-6 text-indigo-600" />
            </div>
            <div>
              <h2 className="text-lg md:text-xl lg:text-2xl font-heading font-bold text-gray-900">
                RAADS-R {t('result.resultTitle')}
              </h2>
              <p className="text-sm md:text-base text-gray-600 mt-1">
                Ritvo Autism Asperger Diagnostic Scale-Revised
              </p>
            </div>
          </div>

          <div className="space-y-6 md:space-y-8">
            {/* 总分 */}
            <div className={`p-4 md:p-6 rounded-xl border-2 transition-all duration-200 ${total.significant
              ? 'bg-amber-50 border-amber-200 shadow-amber-100/50'
              : 'bg-indigo-50 border-indigo-200 shadow-indigo-100/50'
              } shadow-lg`}>
              <div className="flex items-center gap-2 mb-3 md:mb-4">
                {total.significant ? (
                  <AlertTriangle className="h-5 w-5 md:h-6 md:w-6 text-amber-600" />
                ) : (
                  <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-indigo-600" />
                )}
                <h3 className="text-base md:text-lg lg:text-xl font-semibold text-gray-900">
                  {t('result.totalScore')}
                </h3>
              </div>

              <div className="flex items-baseline gap-2 mb-4">
                <span className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
                  {total.score}
                </span>
                <span className="text-lg md:text-xl text-gray-500">
                  / {total.max}
                </span>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-3 md:h-4 mb-4 overflow-hidden">
                <div
                  className={`h-full rounded-full transition-all duration-1000 ease-out ${total.significant
                    ? 'bg-gradient-to-r from-amber-400 to-amber-600'
                    : 'bg-gradient-to-r from-indigo-400 to-indigo-600'
                    }`}
                  style={{ width: `${total.max ? (total.score / total.max) * 100 : 0}%` }}
                ></div>
              </div>

              <div className={`flex items-start gap-2 p-3 md:p-4 rounded-lg ${total.significant
                ? 'bg-amber-100 border border-amber-200'
                : 'bg-indigo-100 border border-indigo-200'
                }`}>
                <AlertTriangle className={`h-4 w-4 md:h-5 md:w-5 mt-0.5 flex-shrink-0 ${total.significant ? 'text-amber-600' : 'text-indigo-600'
                  }`} />
                <p className={`text-sm md:text-base ${total.significant
                  ? 'text-amber-800'
                  : 'text-indigo-800'
                  }`}>
                  {total.significant
                    ? t('result.raadsr.significant')
                    : t('result.raadsr.notSignificant')}
                </p>
              </div>
            </div>

            {/* 分类得分 */}
            <div>
              <h4 className="text-base md:text-lg font-semibold text-gray-900 mb-4 md:mb-6">
                Category Breakdown
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                {[
                  { title: t('result.raadsr.categoryLanguage'), data: language },
                  { title: t('result.raadsr.categorySocial'), data: social },
                  { title: t('result.raadsr.categorySensoryMotor'), data: sensoryMotor },
                  { title: t('result.raadsr.categoryInterests'), data: interests }
                ].filter((category): category is { title: string; data: { score: number; max: number; significant: boolean } } =>
                  !!category.data && typeof category.data === 'object'
                ).map((category, index) => (
                  <div key={index} className="p-4 md:p-5 rounded-xl bg-gray-50 border border-gray-200 hover:shadow-md transition-shadow duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="text-sm md:text-base font-medium text-gray-900">
                        {category.title}
                      </h5>
                      {category.data.significant && (
                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                      )}
                    </div>
                    <div className="flex items-baseline gap-2">
                      <span className="text-xl md:text-2xl font-bold text-gray-900">
                        {category.data.score}
                      </span>
                      <span className="text-sm md:text-base text-gray-500">
                        / {category.data.max}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-3 overflow-hidden">
                      <div
                        className={`h-full rounded-full transition-all duration-700 ease-out ${category.data.significant
                          ? 'bg-gradient-to-r from-amber-400 to-amber-500'
                          : 'bg-gradient-to-r from-gray-400 to-gray-500'
                          }`}
                        style={{ width: `${category.data.max ? (category.data.score / category.data.max) * 100 : 0}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      );
    } else if (testResult.testType === 'aq10Test') {
      // AQ-10 测试结果
      // AQ-10 的分数直接存储在 score 属性中
      const scoreData = testResult.score;

      // 如果没有分数数据，尝试在 answers.score 对象中查找
      const fallbackScore = testResult.answers && typeof testResult.answers === 'object' && 'score' in testResult.answers
        ? testResult.answers.score
        : undefined;

      // 如果仍然没有找到分数，使用 scores.total 作为最后尝试
      const finalScoreData = scoreData || fallbackScore || (testResult.scores ? testResult.scores.total : undefined);

      // 如果没有任何分数数据，显示基本信息
      if (!finalScoreData) {
        return (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
            <div className="flex items-center gap-3 mb-4 md:mb-6">
              <div className="flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
                <ClipboardList className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
              </div>
              <div>
                <h2 className="text-lg md:text-xl lg:text-2xl font-heading font-bold text-gray-900">
                  AQ-10 {t('result.resultTitle')}
                </h2>
                <p className="text-sm md:text-base text-gray-600 mt-1">
                  {t('result.loading')}
                </p>
              </div>
            </div>
          </div>
        );
      }

      // 类型保护：确保 finalScoreData 有 score, max 和 significant 属性
      const validScoreData = finalScoreData as { score: number; max: number; significant: boolean };
      const isSignificant = validScoreData.significant === true;

      return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
          <div className="flex items-center gap-3 mb-6 md:mb-8">
            <div className="flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
              <ClipboardList className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
            </div>
            <div>
              <h2 className="text-lg md:text-xl lg:text-2xl font-heading font-bold text-gray-900">
                AQ-10 {t('result.resultTitle')}
              </h2>
              <p className="text-sm md:text-base text-gray-600 mt-1">
                Autism Spectrum Quotient-10
              </p>
            </div>
          </div>

          <div className={`p-4 md:p-6 rounded-xl border-2 transition-all duration-200 ${isSignificant
            ? 'bg-amber-50 border-amber-200 shadow-amber-100/50'
            : 'bg-emerald-50 border-emerald-200 shadow-emerald-100/50'
            } shadow-lg`}>
            <div className="flex items-center gap-2 mb-3 md:mb-4">
              {isSignificant ? (
                <AlertTriangle className="h-5 w-5 md:h-6 md:w-6 text-amber-600" />
              ) : (
                <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
              )}
              <h3 className="text-base md:text-lg lg:text-xl font-semibold text-gray-900">
                {t('result.totalScore')}
              </h3>
            </div>

            <div className="flex items-baseline gap-2 mb-4">
              <span className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
                {validScoreData.score}
              </span>
              <span className="text-lg md:text-xl text-gray-500">
                / {validScoreData.max}
              </span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-3 md:h-4 mb-4 overflow-hidden">
              <div
                className={`h-full rounded-full transition-all duration-1000 ease-out ${isSignificant
                  ? 'bg-gradient-to-r from-amber-400 to-amber-600'
                  : 'bg-gradient-to-r from-emerald-400 to-emerald-600'
                  }`}
                style={{ width: `${validScoreData.max ? (validScoreData.score / validScoreData.max) * 100 : 0}%` }}
              ></div>
            </div>

            <div className={`flex items-start gap-2 p-3 md:p-4 rounded-lg ${isSignificant
              ? 'bg-amber-100 border border-amber-200'
              : 'bg-emerald-100 border border-emerald-200'
              }`}>
              {isSignificant ? (
                <AlertTriangle className="h-4 w-4 md:h-5 md:w-5 mt-0.5 flex-shrink-0 text-amber-600" />
              ) : (
                <CheckCircle className="h-4 w-4 md:h-5 md:w-5 mt-0.5 flex-shrink-0 text-emerald-600" />
              )}
              <p className={`text-sm md:text-base ${isSignificant
                ? 'text-amber-800'
                : 'text-emerald-800'
                }`}>
                {isSignificant
                  ? t('result.aq10.significant')
                  : t('result.aq10.notSignificant')}
              </p>
            </div>
          </div>
        </div>
      );
    } else {
      // 未知测试类型
      return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
          <div className="flex items-center gap-3 mb-4 md:mb-6">
            <div className="flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-gray-100 rounded-xl flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 md:h-6 md:w-6 text-gray-600" />
            </div>
            <div>
              <h2 className="text-lg md:text-xl lg:text-2xl font-heading font-bold text-gray-900">
                {t('result.resultTitle')}
              </h2>
              <p className="text-sm md:text-base text-gray-600 mt-1">
                {t('result.unsupportedTestType')}
              </p>
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="space-y-4 md:space-y-6">
      {/* 测试结果摘要 */}
      {renderTestResults()}

      {/* 个性化详细报告 */}
      <div className="bg-white border border-gray-100 rounded-xl shadow-sm">
        <div className="p-4 md:p-6 border-b border-gray-100">
          <div>
            <h2 className="text-lg md:text-xl font-heading font-semibold text-gray-900">
              {t('result.detailedReport')}
            </h2>
            <p className="mt-1 text-sm md:text-base text-gray-600">
              {t('result.reportDisclaimer')}
            </p>
          </div>
        </div>

        <AutismReport
          key={regenerateKey}
          testType={testResult.testType === 'raadsrTest' ? 'raadsr' : 'aq10'}
          answers={testResult.answers}
          scores={testResult.scores}
          score={testResult.score}
          locale={locale}
          testId={testResult.id}
          email={session?.user?.email || undefined}
          userId={session?.user?.id}
          regenerate={shouldRegenerate}
          onReset={handleRegenerateReport}
          onGenerationStateChange={handleGenerationStateChange}
        />
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
        <button
          onClick={handleRetakeTest}
          className={`flex items-center justify-center px-6 py-3 font-medium rounded-lg text-sm md:text-base transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${testResult.testType === 'raadsrTest'
            ? 'bg-indigo-600 hover:bg-indigo-700 text-white focus:ring-indigo-500'
            : 'bg-emerald-600 hover:bg-emerald-700 text-white focus:ring-emerald-500'
            }`}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          {t('buttons.retest')}
        </button>
        <button
          onClick={() => router.push(`/${locale === 'en' ? '' : locale + '/'}reports`)}
          className="flex items-center justify-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg text-sm md:text-base transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
        >
          <FileText className="h-4 w-4 mr-2" />
          {t('result.viewAllReports')}
        </button>
      </div>
    </div>
  );
} 