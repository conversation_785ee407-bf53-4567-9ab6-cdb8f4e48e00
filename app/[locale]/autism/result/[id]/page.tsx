import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { prisma } from '@/utils/prisma';
import { redirect, notFound } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import ResultDisplay from './ResultDisplay';
import Link from 'next/link';
import { ChevronRight, Home, Brain, AlertTriangle, LogIn, Shield } from 'lucide-react';
import { TestResult as PrismaTestResult } from '@prisma/client';
import type { TestResult } from '@/types/prisma';
import ChatConsultationContainer from '@/components/ChatConsultationContainer';
import ReportBindBanner from '@/components/ReportBindBanner';
import { getLocalizedPath } from '@/utils/utils';

// 生成页面元数据
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string; id: string }>
}): Promise<Metadata> {
  // 等待params对象解析
  const resolvedParams = await params;
  const { locale } = resolvedParams;

  const t = await getTranslations('pages.autism');

  return {
    title: t('result.pageTitle'),
    description: t('result.description'),
  };
}

// 获取特定测试结果
async function getTestResult(id: string): Promise<PrismaTestResult | null> {
  try {
    console.log(`获取测试结果，ID: ${id}`);
    const testResult = await prisma.testResult.findUnique({
      where: { id }
    });

    if (!testResult) {
      console.log(`未找到测试结果，ID: ${id}`);
      return null;
    }

    return testResult;
  } catch (error) {
    console.error('Error fetching test result:', error);
    return null;
  }
}

// 测试结果页面
export default async function ResultPage({
  params,
  searchParams,
}: {
  params: Promise<{ id: string, locale: string }>;
  searchParams?: Promise<{ token?: string }>;
}) {
  // 等待params对象解析
  const resolvedParams = await params;
  const resolvedSearchParams = searchParams ? await searchParams : {};
  const { id: testId, locale } = resolvedParams;
  const token = resolvedSearchParams?.token;

  const t = await getTranslations('pages.autism');
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  // 获取测试结果
  let testResult = await getTestResult(testId);

  if (!testResult) {
    notFound();
  }

  // 自动绑定逻辑：如果用户已登录且测试未绑定，尝试自动绑定
  const shouldAutoBind = userId && !testResult!.isUserBound && (
    // 情况1: 提供了正确的token
    testResult!.accessToken === token ||
    // 情况2: 邮箱匹配
    (session?.user?.email && testResult!.email === session.user.email)
  );
  
  if (shouldAutoBind) {
    try {
      // 更新测试结果，绑定到用户账户
      await prisma.testResult.update({
        where: { id: testId },
        data: {
          userId: userId,
          isUserBound: true,
        },
      });
      
      // 重新获取更新后的测试结果
      testResult = await getTestResult(testId);
      
      console.log(`自动绑定测试结果 ${testId} 到用户 ${userId}`);
    } catch (error) {
      console.error('自动绑定测试结果失败:', error);
      // 绑定失败不影响正常流程，继续显示结果
    }
  }

  // 访问控制逻辑
  const canAccess =
    // 情况1: 已登录且报告已绑定到当前用户
    (userId && testResult!.userId === userId) ||
    // 情况2: 报告未绑定且提供了正确的访问令牌
    (!testResult!.isUserBound && testResult!.accessToken === token) ||
    // 情况3: 已登录且邮箱匹配（即使没有token）
    (userId && session?.user?.email && testResult!.email === session.user.email && !testResult!.isUserBound);

  // 如果无权访问，显示权限提示页面
  if (!canAccess) {
    // 如果是已绑定的测试但用户未登录，显示登录提示
    if (testResult!.isUserBound && !userId) {
      return (
        <main className="flex flex-col min-h-screen pt-20 md:pt-24 pb-8 md:pb-12 px-3 md:px-4 bg-gray-50">
          <div className="container mx-auto max-w-4xl">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 md:p-8 text-center">
              <div className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 bg-blue-50 rounded-full flex items-center justify-center">
                <LogIn className="h-8 w-8 md:h-10 md:w-10 text-blue-600" />
              </div>
              <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 font-heading">
                {t('result.signInRequired.title')}
              </h1>
              <p className="text-gray-600 mb-6 md:mb-8 text-sm md:text-base max-w-md mx-auto leading-relaxed">
                {t('result.signInRequired.description')}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href={getLocalizedPath(`/auth/signin?callbackUrl=${encodeURIComponent(getLocalizedPath(`/autism/result/${testId}`, locale))}`, locale)}
                  className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm md:text-base"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  {t('result.signInRequired.signInButton')}
                </Link>
                <Link
                  href={getLocalizedPath('/autism', locale)}
                  className="inline-flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 text-sm md:text-base"
                >
                  {t('result.signInRequired.takeNewTestButton')}
                </Link>
              </div>
            </div>
          </div>
        </main>
      );
    }

    // 如果是未绑定的测试且用户未登录，也显示登录提示（优化用户体验）
    if (!testResult!.isUserBound && !userId) {
      return (
        <main className="flex flex-col min-h-screen pt-20 md:pt-24 pb-8 md:pb-12 px-3 md:px-4 bg-gray-50">
          <div className="container mx-auto max-w-4xl">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 md:p-8 text-center">
              <div className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 bg-blue-50 rounded-full flex items-center justify-center">
                <LogIn className="h-8 w-8 md:h-10 md:w-10 text-blue-600" />
              </div>
              <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 font-heading">
                {t('result.signInRequired.title')}
              </h1>
              <p className="text-gray-600 mb-6 md:mb-8 text-sm md:text-base max-w-md mx-auto leading-relaxed">
                {t('result.signInRequired.description')}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href={getLocalizedPath(`/auth/signin?callbackUrl=${encodeURIComponent(getLocalizedPath(`/autism/result/${testId}${token ? `?token=${token}` : ''}`, locale))}`, locale)}
                  className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm md:text-base"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  {t('result.signInRequired.signInButton')}
                </Link>
                <Link
                  href={getLocalizedPath('/autism', locale)}
                  className="inline-flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 text-sm md:text-base"
                >
                  {t('result.signInRequired.takeNewTestButton')}
                </Link>
              </div>
            </div>
          </div>
        </main>
      );
    }

    // 其他无权访问情况，重定向到未授权页面
    redirect(getLocalizedPath('/unauthorized', locale));
  }

  // 判断是否可以绑定到账户
  const canBind = !testResult!.isUserBound && userId && testResult!.accessToken === token;

  return (
    <main className="flex flex-col min-h-screen pt-20 md:pt-24 pb-8 md:pb-12 px-3 md:px-4 xl:pr-96 bg-gray-50">
      <div className="container mx-auto max-w-6xl">
        {/* 页头部分 - 合并标题和测试信息 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8 mb-4 md:mb-6">
          {/* 绑定账户横幅 */}
          {canBind && (
            <div className="mb-6">
              <ReportBindBanner
                locale={locale}
                testId={testId}
                token={token || ''}
              />
            </div>
          )}

          {/* 导航面包屑 - 移动端优化 */}
          <nav className="flex items-center text-xs md:text-sm text-gray-500 mb-3 md:mb-4 overflow-x-auto">
            <Link
              href={getLocalizedPath('/', locale)}
              className="flex items-center hover:text-blue-600 transition-colors whitespace-nowrap"
            >
              <Home className="h-3 w-3 md:h-4 md:w-4 mr-1" />
              {locale === 'zh' ? '首页' : 'Home'}
            </Link>
            <ChevronRight className="h-3 w-3 md:h-4 md:w-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0" />
            <Link
              href={getLocalizedPath('/autism', locale)}
              className="hover:text-blue-600 transition-colors whitespace-nowrap"
            >
              {locale === 'zh' ? '自闭症测试' : 'Autism Tests'}
            </Link>
            <ChevronRight className="h-3 w-3 md:h-4 md:w-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0" />
            <span className="text-gray-700 truncate">{t('result.pageTitle')}</span>
          </nav>

          {/* 页面标题和描述 */}
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 md:gap-6 mb-6 md:mb-8">
            <div className="flex-1">
              <h1 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2 text-gray-900 flex items-center font-heading">
                <Brain className="mr-2 text-indigo-600 h-5 w-5 md:h-6 md:w-6" />
                {t('result.pageTitle')}
              </h1>
              <p className="text-gray-600 text-sm md:text-base max-w-lg leading-relaxed">
                {t('result.description')}
              </p>
            </div>

            {/* 操作按钮 - 移动端堆叠，桌面端并排 */}
            <div className="flex flex-col sm:flex-row gap-2 md:gap-3 lg:self-start">
              {userId && (
                <Link
                  href={getLocalizedPath('/reports', locale)}
                  className="inline-flex items-center justify-center px-4 py-2.5 md:py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 text-sm md:text-base"
                >
                  {t('result.viewAllReports')}
                </Link>
              )}
              <Link
                href={getLocalizedPath('/autism', locale)}
                className="inline-flex items-center justify-center px-4 py-2.5 md:py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm md:text-base"
              >
                {t('result.takeNewTestButton')}
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </div>

          {/* 测试信息 */}
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3 md:gap-4 pt-4 md:pt-6 border-t border-gray-100">
            <div className="flex items-center">
              <div className="h-5 w-5 text-gray-400 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">
                  {t('result.testDate') || 'TEST DATE'}
                </p>
                <p className="text-sm md:text-base font-medium text-gray-900">
                  {new Date(testResult!.createdAt).toLocaleDateString(locale, {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {testResult!.isUserBound && (
                <div className="flex items-center px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs font-medium">
                  <Shield className="h-3 w-3 mr-1" />
                  {t('result.savedToAccount')}
                </div>
              )}
              <div className="px-3 py-1.5 bg-indigo-50 text-indigo-700 rounded-full text-sm font-medium">
                {testResult!.testType === 'raadsrTest' ? 'RAADS-R Test' : 'AQ-10 Test'}
              </div>
            </div>
          </div>
        </div>

        {/* 测试结果内容 */}
        <ResultDisplay testResult={testResult! as any} locale={locale} />
      </div>

      {/* 会话式咨询 */}
      <ChatConsultationContainer
        testResult={testResult! as PrismaTestResult}
        locale={locale}
      />
    </main>
  );
} 