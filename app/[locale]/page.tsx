import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import Image from 'next/image';
import Link from 'next/link';

export async function generateMetadata({
    params
}: {
    params: Promise<{ locale: string }>
}): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.home.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}`,
        }
    };
}

import FreeResourcesForm from '@/components/FreeResourcesForm';
import HomePageWrapper from './components/HomePageWrapper';

export default async function Home({
    params
}: {
    params: Promise<{ locale: string }>
}) {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.home.content');
    const seo = await getTranslations('pages.home.content.seo.headings');
    const quotes = await getTranslations('pages.home.content.seo.quotes');

    // Helper function to build locale-aware URLs
    const getLocalizedPath = (path: string) => {
        return locale === 'en' ? path : `/${locale}${path}`;
    };

    return (
        <HomePageWrapper>
            <main className="flex flex-col min-h-screen pt-16">
                {/* Hero Section - Improved */}
                <section className="bg-gradient-to-br from-blue-600 to-blue-700 text-white py-16 sm:py-24 px-4">
                    <div className="container mx-auto max-w-6xl">
                        <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-16">
                            <div className="md:w-1/2 max-w-xl">
                                <div className="trust-badge mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                    Trusted by researchers
                                </div>
                                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight font-heading">
                                    {seo('hero')}
                                </h1>
                                <p className="text-lg md:text-xl mb-8 text-blue-50 max-w-md">
                                    {seo('heroDesc')}
                                </p>
                                <div className="flex flex-wrap gap-4">
                                    <Link
                                        href={getLocalizedPath('/autism/raadsr')}
                                        className="inline-flex items-center justify-center py-3 px-6 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition duration-200 shadow-md"
                                    >
                                        {seo('takeTestBtn')}
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    </Link>
                                    <Link
                                        href="https://raadstest.com"
                                        className="inline-flex items-center justify-center py-3 px-6 bg-transparent text-white border border-white/40 font-medium rounded-lg hover:bg-white/10 transition duration-200"
                                    >
                                        {seo('learnAboutBtn')}
                                    </Link>
                                </div>
                            </div>
                            <div className="md:w-1/2 flex justify-center mt-8 md:mt-0">
                                <div className="relative w-72 sm:w-80 md:w-96 aspect-[3/4] bg-blue-500/70 backdrop-blur rounded-2xl shadow-xl overflow-hidden">
                                    <div className="absolute inset-0 bg-gradient-to-b from-transparent to-blue-900/30"></div>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        <Image
                                            src="/images/home/<USER>"
                                            alt="3D illustration of a human brain with glowing neural pathways, representing neurodiversity and cognitive processing."
                                            width={300}
                                            height={400}
                                            className="object-cover w-full h-full opacity-60"
                                            priority
                                        />
                                        <div className="text-center p-8 absolute inset-0 flex flex-col items-center justify-center">
                                            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                                                </svg>
                                            </div>
                                            <h2 className="text-2xl font-bold mb-3 font-heading">{seo('guideTitle')}</h2>
                                            <p className="text-sm text-blue-50 mb-6">{seo('guideDesc')}</p>
                                            <span className="inline-block px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm">
                                                {seo('resourcesLabel')}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Autism Tests Section - Improved */}
                <section className="py-20 px-4 bg-white" id="autism-tests">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center max-w-2xl mx-auto mb-16">
                            <h2 className="text-3xl sm:text-4xl font-bold mb-6">{seo('autismTestsTitle')}</h2>
                            <p className="text-gray-600">{seo('autismTestsDesc')}</p>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 max-w-4xl mx-auto">
                            {/* AQ-10 Test */}
                            <div className="bg-white rounded-xl shadow-md overflow-hidden card-hover border border-gray-100 flex flex-col h-full">
                                <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-0 overflow-hidden">
                                    <div className="relative w-full h-48 rounded-t-lg">
                                        <Image
                                            src="/images/home/<USER>"
                                            alt="Parent and child using a tablet together in a calm setting, representing family-friendly autism screening tools online."
                                            fill
                                            className="object-cover"
                                        />
                                        <div className="absolute bottom-4 left-4 z-10">
                                            <div className="px-3 py-1.5 bg-white/90 backdrop-blur-sm rounded-full shadow-sm">
                                                <span className="text-lg font-bold text-blue-600">{t('autismTests.aq.title')}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-6 flex flex-col flex-grow">
                                    <div className="flex items-center mb-4">
                                        <h3 className="text-xl font-semibold mr-auto">{seo('aq10Title')}</h3>
                                        <span className="text-xs font-medium text-gray-500 bg-gray-100 rounded-full px-2 py-1 whitespace-nowrap">10 min</span>
                                    </div>
                                    <p className="text-gray-600 mb-6 line-clamp-3">{seo('aq10Desc')}</p>
                                    <div className="flex justify-between items-center mt-auto">
                                        <span className="flex items-center text-sm text-gray-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                            </svg>
                                            <span className="truncate">{seo('scientificLabel')}</span>
                                        </span>
                                        <Link
                                            href={getLocalizedPath('/autism/aq10')}
                                            className="inline-flex items-center justify-center py-2 px-4 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition duration-200 shadow-sm whitespace-nowrap"
                                        >
                                            {seo('takeAQTestBtn')}
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            {/* RAADS-R Test */}
                            <div className="bg-white rounded-xl shadow-md overflow-hidden card-hover border border-gray-100 flex flex-col h-full">
                                <div className="bg-gradient-to-r from-indigo-50 to-indigo-100 p-0 overflow-hidden">
                                    <div className="relative w-full h-48 rounded-t-lg">
                                        <Image
                                            src="/images/home/<USER>"
                                            alt="3D-rendered brain with scientific graph overlay, representing autism spectrum analysis and neurological mapping."
                                            fill
                                            className="object-cover"
                                        />
                                        <div className="absolute bottom-4 left-4 z-10">
                                            <div className="px-3 py-1.5 bg-white/90 backdrop-blur-sm rounded-full shadow-sm">
                                                <span className="text-lg font-bold text-indigo-600">{t('autismTests.raads.title')}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-6 flex flex-col flex-grow">
                                    <div className="flex items-center mb-4">
                                        <h3 className="text-xl font-semibold mr-auto">{seo('raadsTitle')}</h3>
                                        <span className="text-xs font-medium text-gray-500 bg-gray-100 rounded-full px-2 py-1 whitespace-nowrap">25 min</span>
                                    </div>
                                    <p className="text-gray-600 mb-6 line-clamp-3">{seo('raadsDesc')}</p>
                                    <div className="flex justify-between items-center mt-auto">
                                        <span className="flex items-center text-sm text-gray-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                            </svg>
                                            <span className="truncate">{seo('comprehensiveLabel')}</span>
                                        </span>
                                        <Link
                                            href={getLocalizedPath('/autism/raadsr')}
                                            className="inline-flex items-center justify-center py-2 px-4 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition duration-200 shadow-sm whitespace-nowrap"
                                        >
                                            {t('takeTest')}
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="mt-12 text-center">
                            <Link
                                href="https://raadstest.com"
                                className="inline-flex items-center justify-center py-3 px-6 bg-blue-50 text-blue-700 font-medium rounded-lg hover:bg-blue-100 transition duration-200"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                                </svg>
                                {seo('learnMoreAutismBtn')}
                            </Link>
                        </div>
                    </div>
                </section>

                {/* ADHD Test Section - Improved */}
                <section className="py-20 px-4 bg-gray-50" id="adhd-tests">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center max-w-2xl mx-auto mb-16">
                            <h2 className="text-3xl sm:text-4xl font-bold mb-6">{seo('adhdTestsTitle')}</h2>
                            <p className="text-gray-600">{seo('adhdTestsDesc')}</p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                            {/* ADHD Test - Adult */}
                            <div className="bg-white rounded-xl shadow-md overflow-hidden card-hover border border-gray-100 flex flex-col h-full">
                                <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-0 overflow-hidden">
                                    <div className="relative w-full h-48 rounded-t-lg">
                                        <Image
                                            src="/images/home/<USER>"
                                            alt="Vector illustration of a young adult looking stressed in a cluttered workspace filled with sticky notes, representing ADHD challenges."
                                            fill
                                            className="object-cover"
                                        />
                                        <div className="absolute bottom-4 left-4 z-10">
                                            <div className="px-3 py-1.5 bg-white/90 backdrop-blur-sm rounded-full shadow-sm">
                                                <span className="text-lg font-bold text-purple-600">{t('adhdTests.adult.shortTitle')}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-6 flex flex-col flex-grow">
                                    <div className="flex items-center mb-4">
                                        <h3 className="text-xl font-semibold mr-auto">{seo('adultAdhdTitle')}</h3>
                                        <span className="text-xs font-medium text-gray-500 bg-gray-100 rounded-full px-2 py-1 whitespace-nowrap">15 min</span>
                                    </div>
                                    <p className="text-gray-600 mb-6 line-clamp-3">{seo('adultAdhdDesc')}</p>
                                    <div className="flex justify-between items-center mt-auto">
                                        <span className="flex items-center text-sm text-gray-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                            </svg>
                                            <span className="truncate">{seo('adultLabel')}</span>
                                        </span>
                                        <Link
                                            href={getLocalizedPath('/adhd')}
                                            className="inline-flex items-center justify-center py-2 px-4 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition duration-200 shadow-sm whitespace-nowrap"
                                        >
                                            {seo('takeAdhdTestBtn')}
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            {/* ADHD Test - Child */}
                            <div className="bg-white rounded-xl shadow-md overflow-hidden card-hover border border-gray-100 flex flex-col h-full">
                                <div className="bg-gradient-to-r from-teal-50 to-teal-100 p-0 overflow-hidden">
                                    <div className="relative w-full h-48 rounded-t-lg">
                                        <Image
                                            src="/images/home/<USER>"
                                            alt="Abstract profile of a fragmented head with floating focus icons, symbolizing attention deficit issues in ADHD diagnosis."
                                            fill
                                            className="object-cover"
                                        />
                                        <div className="absolute bottom-4 left-4 z-10">
                                            <div className="px-3 py-1.5 bg-white/90 backdrop-blur-sm rounded-full shadow-sm">
                                                <span className="text-lg font-bold text-teal-600">{t('adhdTests.child.shortTitle')}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-6 flex flex-col flex-grow">
                                    <div className="flex items-center mb-4">
                                        <h3 className="text-xl font-semibold mr-auto">{seo('childAdhdTitle')}</h3>
                                        <span className="text-xs font-medium text-gray-500 bg-gray-100 rounded-full px-2 py-1 whitespace-nowrap">12 min</span>
                                    </div>
                                    <p className="text-gray-600 mb-6 line-clamp-3">{seo('childAdhdDesc')}</p>
                                    <div className="flex justify-between items-center mt-auto">
                                        <span className="flex items-center text-sm text-gray-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                            </svg>
                                            <span className="truncate">{seo('childLabel')}</span>
                                        </span>
                                        <Link
                                            href={getLocalizedPath('/adhd')}
                                            className="inline-flex items-center justify-center py-2 px-4 bg-teal-600 text-white font-medium rounded-lg hover:bg-teal-700 transition duration-200 shadow-sm whitespace-nowrap"
                                        >
                                            {t('takeTest')}
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="mt-12 text-center">
                            <Link
                                href="https://raadstest.com"
                                className="inline-flex items-center justify-center py-3 px-6 bg-blue-50 text-blue-700 font-medium rounded-lg hover:bg-blue-100 transition duration-200"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                                </svg>
                                {seo('learnMoreAdhdBtn')}
                            </Link>
                        </div>
                    </div>
                </section>

                {/* Strengths and Challenges Section - Improved */}
                <section className="py-20 px-4 bg-white">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center max-w-2xl mx-auto mb-16">
                            <h2 className="text-3xl sm:text-4xl font-bold mb-6">{seo('profileTitle')}</h2>
                            <p className="text-gray-600">{seo('profileDesc')}</p>
                        </div>

                        <div className="mb-12 relative w-full h-48 sm:h-56 md:h-64 rounded-xl overflow-hidden shadow-lg">
                            <Image
                                src="/images/home/<USER>"
                                alt="Split-screen showing strengths and challenges of neurodivergent individuals with focus and confusion symbolism."
                                fill
                                className="object-cover"
                            />
                            <div className="absolute inset-0 bg-gradient-to-r from-green-600/30 to-blue-600/30 flex items-center justify-center">
                                <h3 className="text-white text-2xl md:text-3xl font-bold px-6 py-3 bg-black/30 rounded-lg backdrop-blur-sm">
                                    Neurodiversity Profile
                                </h3>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
                            {/* Strengths */}
                            <div className="bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl p-8 md:p-10 shadow-lg">
                                <div className="flex items-center mb-8">
                                    <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center mr-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold">{t('strengths.title')}</h2>
                                </div>
                                <ul className="space-y-5">
                                    {Array.from({ length: 5 }).map((_, i) => (
                                        <li key={i} className="flex items-start">
                                            <div className="mr-3 mt-1 bg-white/20 rounded-full p-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <p className="text-lg">{t(`strengths.item${i + 1}`)}</p>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            {/* Challenges */}
                            <div className="bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-2xl p-8 md:p-10 shadow-lg">
                                <div className="flex items-center mb-8">
                                    <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center mr-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold">{t('kryptonites.title')}</h2>
                                </div>
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                    {Array.from({ length: 6 }).map((_, i) => (
                                        <div key={i} className="bg-white/10 backdrop-blur-sm rounded-xl p-4 flex flex-col items-center text-center">
                                            <div className="mb-3 bg-white/20 rounded-full p-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <p className="text-sm font-medium">{t(`kryptonites.item${i + 1}`)}</p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Testimonials Section - Apply new font styles */}
                <section className="py-16 sm:py-20 px-4 bg-gray-50">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center max-w-2xl mx-auto mb-12 sm:mb-16">
                            <span className="inline-block px-3 py-1 bg-blue-100 text-blue-700 font-medium rounded-full text-sm mb-4">
                                {t('testimonials.title')}
                            </span>
                            <h2 className="text-3xl sm:text-4xl font-bold mb-4 sm:mb-6 font-heading">{seo('experiencesTitle')}</h2>
                            <p className="text-gray-600">{seo('experiencesDesc')}</p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
                            {/* First Testimonial */}
                            <div className="bg-white p-6 sm:p-8 rounded-2xl shadow-md relative flex flex-col h-full transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
                                <div className="absolute top-0 right-0 transform translate-x-1/4 -translate-y-1/4">
                                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-10 h-10 sm:w-12 sm:h-12">
                                        <path d="M33.75 16.25H28.75V11.25C28.75 8.35 31.1 6 34 6H34.375C35.275 6 36 5.275 36 4.375V1.625C36 0.725 35.275 0 34.375 0H34C27.775 0 22.75 5.025 22.75 11.25V33.125C22.75 35.825 24.925 38 27.625 38H33.75C36.45 38 38.625 35.825 38.625 33.125V21.125C38.625 18.425 36.45 16.25 33.75 16.25ZM12.375 16.25H7.375V11.25C7.375 8.35 9.725 6 12.625 6H13C13.9 6 14.625 5.275 14.625 4.375V1.625C14.625 0.725 13.9 0 13 0H12.625C6.4 0 1.375 7.035 1.375 15.75V46.375C1.375 50.155 4.97 53.2 8.75 53.2H17.325C21.105 53.2 24.15 50.155 24.15 46.375V29.575C24.15 25.795 21.105 22.75 17.325 22.75Z" fill="#DBEAFE" />
                                    </svg>
                                </div>
                                <div className="mb-4 sm:mb-6">
                                    <div className="flex mb-1">
                                        {[...Array(5)].map((_, i) => (
                                            <svg key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        ))}
                                    </div>
                                </div>
                                <p className="testimonial-content flex-grow">&ldquo;{t('testimonials.person1.quote')}&rdquo;</p>
                                <div className="flex items-center mt-auto">
                                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 relative rounded-full overflow-hidden border-2 border-blue-100">
                                        <Image
                                            src="/images/home/<USER>/autism-testimonial-avatar-asian-woman.webp"
                                            alt="User testimonial avatar"
                                            fill
                                            className="object-cover"
                                        />
                                    </div>
                                    <div className="ml-3 sm:ml-4">
                                        <h3 className="font-semibold text-sm sm:text-base font-heading">{t('testimonials.person1.name')}</h3>
                                        <p className="text-xs sm:text-sm text-gray-500">{t('testimonials.person1.role')}</p>
                                    </div>
                                    <div className="ml-auto">
                                        <span className="inline-flex items-center px-2 py-0.5 sm:px-2.5 sm:py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg className="w-3 h-3 mr-1" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            </svg>
                                            {seo('verifiedLabel')}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Second Testimonial */}
                            <div className="bg-white p-6 sm:p-8 rounded-2xl shadow-md relative flex flex-col h-full transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
                                <div className="absolute top-0 right-0 transform translate-x-1/4 -translate-y-1/4">
                                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-10 h-10 sm:w-12 sm:h-12">
                                        <path d="M33.75 16.25H28.75V11.25C28.75 8.35 31.1 6 34 6H34.375C35.275 6 36 5.275 36 4.375V1.625C36 0.725 35.275 0 34.375 0H34C27.775 0 22.75 5.025 22.75 11.25V33.125C22.75 35.825 24.925 38 27.625 38H33.75C36.45 38 38.625 35.825 38.625 33.125V21.125C38.625 18.425 36.45 16.25 33.75 16.25ZM12.375 16.25H7.375V11.25C7.375 8.35 9.725 6 12.625 6H13C13.9 6 14.625 5.275 14.625 4.375V1.625C14.625 0.725 13.9 0 13 0H12.625C6.4 0 1.375 7.035 1.375 15.75V46.375C1.375 50.155 4.97 53.2 8.75 53.2H17.325C21.105 53.2 24.15 50.155 24.15 46.375V29.575C24.15 25.795 21.105 22.75 17.325 22.75Z" fill="#10B981" />
                                    </svg>
                                </div>
                                <div className="mb-4 sm:mb-6">
                                    <div className="flex mb-1">
                                        {[...Array(5)].map((_, i) => (
                                            <svg key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        ))}
                                    </div>
                                </div>
                                <p className="testimonial-content flex-grow">&ldquo;{t('testimonials.person2.quote')}&rdquo;</p>
                                <div className="flex items-center mt-auto">
                                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 relative rounded-full overflow-hidden border-2 border-blue-100">
                                        <Image
                                            src="/images/home/<USER>/adhd-testimonial-avatar-redhead-man.webp"
                                            alt="User testimonial avatar"
                                            fill
                                            className="object-cover"
                                        />
                                    </div>
                                    <div className="ml-3 sm:ml-4">
                                        <h3 className="font-semibold text-sm sm:text-base font-heading">{t('testimonials.person2.name')}</h3>
                                        <p className="text-xs sm:text-sm text-gray-500">{t('testimonials.person2.role')}</p>
                                    </div>
                                    <div className="ml-auto">
                                        <span className="inline-flex items-center px-2 py-0.5 sm:px-2.5 sm:py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg className="w-3 h-3 mr-1" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            </svg>
                                            {seo('verifiedLabel')}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Additional Testimonials */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 mt-6 sm:mt-8">
                            {/* Third Testimonial */}
                            <div className="bg-white p-6 sm:p-8 rounded-2xl shadow-md relative flex flex-col h-full transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
                                <div className="mb-4 sm:mb-6">
                                    <div className="flex mb-1">
                                        {[...Array(5)].map((_, i) => (
                                            <svg key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        ))}
                                    </div>
                                </div>
                                <p className="testimonial-content flex-grow">&ldquo;The ADHD test helped me understand my son&apos;s behavior patterns. Getting this perspective early has made a huge difference in our approach to his education.&rdquo;</p>
                                <div className="flex items-center mt-auto">
                                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 relative rounded-full overflow-hidden border-2 border-blue-100">
                                        <Image
                                            src="/images/home/<USER>/adhd-testimonial-avatar-teen-boy.webp"
                                            alt="User testimonial avatar"
                                            fill
                                            className="object-cover"
                                        />
                                    </div>
                                    <div className="ml-3 sm:ml-4">
                                        <h3 className="font-semibold text-sm sm:text-base font-heading">Michael P.</h3>
                                        <p className="text-xs sm:text-sm text-gray-500">Parent</p>
                                    </div>
                                    <div className="ml-auto">
                                        <span className="inline-flex items-center px-2 py-0.5 sm:px-2.5 sm:py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg className="w-3 h-3 mr-1" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            </svg>
                                            {seo('verifiedLabel')}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Fourth Testimonial */}
                            <div className="bg-white p-6 sm:p-8 rounded-2xl shadow-md relative flex flex-col h-full transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
                                <div className="mb-4 sm:mb-6">
                                    <div className="flex mb-1">
                                        {[...Array(5)].map((_, i) => (
                                            <svg key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        ))}
                                    </div>
                                </div>
                                <p className="testimonial-content flex-grow">&ldquo;Finding this resource at 42 was life-changing. The RAADS-R test results explained so much about my life experiences. I finally have answers and can access proper support.&rdquo;</p>
                                <div className="flex items-center mt-auto">
                                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 relative rounded-full overflow-hidden border-2 border-blue-100">
                                        <Image
                                            src="/images/home/<USER>/neurodivergent-avatar-black-woman-testimonial.webp"
                                            alt="User testimonial avatar"
                                            fill
                                            className="object-cover"
                                        />
                                    </div>
                                    <div className="ml-3 sm:ml-4">
                                        <h3 className="font-semibold text-sm sm:text-base font-heading">Janelle T.</h3>
                                        <p className="text-xs sm:text-sm text-gray-500">Professional</p>
                                    </div>
                                    <div className="ml-auto">
                                        <span className="inline-flex items-center px-2 py-0.5 sm:px-2.5 sm:py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg className="w-3 h-3 mr-1" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            </svg>
                                            {seo('verifiedLabel')}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* View More Testimonials Button */}
                        <div className="mt-10 sm:mt-12 text-center">
                            <a
                                href="https://raadstest.com"
                                className="inline-flex items-center justify-center py-2 px-4 sm:py-3 sm:px-6 bg-blue-50 text-blue-700 font-medium rounded-lg hover:bg-blue-100 transition duration-200 shadow-sm"
                                aria-label="View more testimonials"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                                </svg>
                                View More Stories
                            </a>
                        </div>
                    </div>
                </section>

                {/* Q&A Section - Improved */}
                <section className="py-20 px-4 bg-white">
                    <div className="container mx-auto max-w-6xl">
                        <div className="text-center max-w-2xl mx-auto mb-4">
                            <p>Have more questions about our autism or ADHD tests? We&apos;re here to help.</p>
                            <div className="mt-4">
                                <a
                                    href="mailto:<EMAIL>"
                                    className="inline-flex items-center justify-center py-3 px-6 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition duration-200 shadow-sm"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                    Get Support
                                </a>
                            </div>
                        </div>

                        <div className="text-center max-w-2xl mx-auto mb-16 relative">
                            <span className="inline-block px-3 py-1 bg-blue-100 text-blue-700 font-medium rounded-full text-sm mb-4">
                                {t('faq.title')}
                            </span>
                            <h2 className="text-3xl sm:text-4xl font-bold mb-6">{seo('faqTitle')}</h2>
                            <p className="text-gray-600">{seo('faqDesc')}</p>

                            <div className="absolute opacity-10 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-64 sm:h-80 md:h-96 z-0 pointer-events-none">
                                <Image
                                    src="/images/home/<USER>"
                                    alt="Friendly chatbot and human in conversation surrounded by FAQ icons"
                                    fill
                                    className="object-contain transform rotate-6"
                                />
                            </div>
                        </div>

                        <div className="relative max-w-5xl mx-auto">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6 relative z-10">
                                {Array.from({ length: 6 }).map((_, i) => (
                                    <div key={i} className="bg-gray-50 rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300 backdrop-blur-sm">
                                        <h3 className="text-xl font-semibold mb-4 flex items-start">
                                            <span className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-3">
                                                Q
                                            </span>
                                            <span className="mt-1">{t(`faq.q${i + 1}.question`)}</span>
                                        </h3>
                                        <div className="pl-11">
                                            <p className="text-gray-600">{t(`faq.q${i + 1}.answer`)}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="mt-16 text-center">
                            <p className="text-gray-600 mb-6">{seo('moreQuestionsText')}</p>
                            <a
                                href="mailto:<EMAIL>"
                                className="inline-flex items-center justify-center py-3 px-6 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition duration-200 shadow-sm"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                </svg>
                                <EMAIL>
                            </a>
                        </div>
                    </div>
                </section>

                {/* Embrace Section - Improved */}
                <section className="py-20 px-4 bg-gradient-to-br from-green-600 to-green-700 text-white relative overflow-hidden">
                    <div className="absolute left-0 top-0 w-2/3 h-full opacity-15 pointer-events-none">
                        <div className="absolute -left-24 -top-24 w-full h-full transform -rotate-12">
                            <Image
                                src="/images/home/<USER>"
                                alt="Neurodiversity symbol"
                                fill
                                className="object-contain object-left-top"
                            />
                        </div>
                    </div>
                    <div className="container mx-auto max-w-5xl relative z-10">
                        <div className="flex flex-col md:flex-row items-center justify-between gap-12">
                            <div className="md:w-1/2">
                                <span className="inline-block px-3 py-1 bg-white/20 text-white backdrop-blur-sm font-medium rounded-full text-sm mb-4">
                                    {t('autismTests.title')}
                                </span>
                                <h2 className="text-3xl sm:text-4xl font-bold mb-6">{seo('embraceTitle')}</h2>
                                <p className="text-xl mb-6 text-green-50">{seo('embraceDesc')}</p>
                                <Link
                                    href={getLocalizedPath('/autism/raadsr')}
                                    className="inline-flex items-center justify-center py-3 px-8 bg-white text-green-600 font-medium rounded-lg hover:bg-green-50 transition duration-200 shadow-md"
                                >
                                    {seo('takeFreeAutismBtn')}
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </Link>
                            </div>
                            <div className="md:w-1/2">
                                <div className="relative bg-white text-gray-800 p-8 rounded-2xl shadow-lg backdrop-blur-sm">
                                    <div className="absolute top-0 right-0 transform translate-x-1/4 -translate-y-1/4">
                                        <svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M47.25 22.75H40.25V15.75C40.25 11.69 43.54 8.4 47.6 8.4H48.125C49.385 8.4 50.4 7.385 50.4 6.125V2.275C50.4 1.015 49.385 0 48.125 0H47.6C38.885 0 31.85 7.035 31.85 15.75V46.375C31.85 50.155 34.895 53.2 38.675 53.2H47.25C51.03 53.2 54.075 50.155 54.075 46.375V29.575C54.075 25.795 51.03 22.75 47.25 22.75ZM17.325 22.75H10.325V15.75C10.325 11.69 13.615 8.4 17.675 8.4H18.2C19.46 8.4 20.475 7.385 20.475 6.125V2.275C20.475 1.015 19.46 0 18.2 0H17.675C8.96 0 1.925 7.035 1.925 15.75V46.375C1.925 50.155 4.97 53.2 8.75 53.2H17.325C21.105 53.2 24.15 50.155 24.15 46.375V29.575C24.15 25.795 21.105 22.75 17.325 22.75Z" fill="#10B981" />
                                        </svg>
                                    </div>
                                    <p className="text-xl leading-relaxed font-medium text-gray-700 mb-6">
                                        {quotes('testimonialQuote')}
                                    </p>
                                    <div className="flex items-center">
                                        <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-medium">{seo('adultAutismLabel')}</h4>
                                            <p className="text-sm text-gray-500">{seo('diagnosedLabel')}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Newsletter Section - Improved */}
                <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                    <div className="container mx-auto max-w-4xl text-center">
                        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-10 shadow-lg relative overflow-hidden">
                            <div className="absolute -right-24 -bottom-16 w-2/3 h-full rotate-12">
                                <Image
                                    src="/images/home/<USER>"
                                    alt="Email resources background"
                                    fill
                                    className="object-contain object-right-bottom opacity-20"
                                />
                            </div>
                            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/40 to-transparent pointer-events-none"></div>

                            <div className="relative z-10">
                                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-white text-blue-600 mb-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                </div>
                                <h2 className="text-3xl font-bold mb-4">{seo('newsletterTitle')}</h2>
                                <p className="text-lg mb-8 max-w-xl mx-auto text-blue-50">{seo('newsletterDesc')}</p>
                                <FreeResourcesForm locale={locale} />
                                <p className="text-sm mt-6 text-blue-100">
                                    {seo('privacyNote')}
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </HomePageWrapper>
    );
}