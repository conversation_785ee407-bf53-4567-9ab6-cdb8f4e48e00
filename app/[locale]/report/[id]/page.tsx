import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { Brain, Home } from 'lucide-react';
import { getLocalizedPath } from '@/utils/utils';

export async function generateMetadata({
    params
}: {
    params: Promise<{ locale: string; id: string }>
}): Promise<Metadata> {
    const resolvedParams = await params;
    const { locale } = resolvedParams;

    const t = await getTranslations('pages.report');

    return {
        title: t('title'),
        description: t('description'),
    };
}

export default async function ReportRedirectPage({
    params,
}: {
    params: Promise<{ locale: string; id: string }>;
}) {
    const resolvedParams = await params;
    const { locale, id } = resolvedParams;

    const t = await getTranslations('pages.report');

    return (
        <main className="flex flex-col min-h-screen pt-20 md:pt-24 pb-8 md:pb-12 px-3 md:px-4 bg-gray-50">
            <div className="container mx-auto max-w-4xl flex-1 flex items-center justify-center">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 md:p-8 text-center max-w-lg w-full">
                    <div className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 bg-blue-50 rounded-full flex items-center justify-center">
                        <Brain className="h-8 w-8 md:h-10 md:w-10 text-blue-600" />
                    </div>

                    <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 font-heading">
                        Report Moved
                    </h1>

                    <p className="text-gray-600 mb-6 md:mb-8 text-sm md:text-base max-w-md mx-auto leading-relaxed">
                        This report URL format has been updated. Please use the new test-specific URLs below to access your reports.
                    </p>

                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <Link
                            href={getLocalizedPath('/autism', locale)}
                            className="inline-flex items-center justify-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 text-sm md:text-base"
                        >
                            <Brain className="mr-2 h-4 w-4" />
                            Autism Tests
                        </Link>
                        <Link
                            href={getLocalizedPath('/adhd', locale)}
                            className="inline-flex items-center justify-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 text-sm md:text-base"
                        >
                            <Brain className="mr-2 h-4 w-4" />
                            ADHD Tests
                        </Link>
                        <Link
                            href={getLocalizedPath('/', locale)}
                            className="inline-flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 text-sm md:text-base"
                        >
                            <Home className="mr-2 h-4 w-4" />
                            Home
                        </Link>
                    </div>
                </div>
            </div>
        </main>
    );
} 