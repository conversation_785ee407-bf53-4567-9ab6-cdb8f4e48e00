import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { redirect } from 'next/navigation';
import { prisma } from '@/utils/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import { formatDate } from '@/utils/formatters';
import Link from 'next/link';
import Image from 'next/image';
import ProfileActions from '@/components/profile/ProfileActions';
import TestHistorySection from '@/components/profile/TestHistorySection';
import PreferencesForm from '@/components/profile/PreferencesForm';
import UnboundTestsBanner from '@/components/UnboundTestsBanner';
import { TestBindingService } from '@/utils/testBindingService';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  const t = await getTranslations('pages.profile.metadata');

  return {
    title: t('title'),
    description: t('description'),
  };
}

export default async function ProfilePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  const t = await getTranslations('pages.profile.content');
  const session = await getServerSession(authOptions);

  // 如果用户未登录，重定向到登录页面
  if (!session?.user) {
    redirect(`/${locale === 'en' ? '' : locale + '/'}auth/signin?callbackUrl=/${locale === 'en' ? '' : locale + '/'}profile`);
  }

  // 获取用户信息 - 只查询已绑定的测试
  const user = await prisma.user.findUnique({
    where: {
      id: session.user.id,
    },
    include: {
      testResults: {
        where: {
          userId: session.user.id,
          isUserBound: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 3, // 只获取最近的3个测试
      },
    },
  });

  if (!user) {
    redirect(`/${locale === 'en' ? '' : locale + '/'}auth/signin`);
  }

  // 用户注册日期格式化
  const joinDate = formatDate(user.createdAt, locale);

  // 从翻译对象中提取所需的字段
  const accountActionsTranslations = {
    title: t('accountActions.title'),
    exportData: t('accountActions.exportData'),
    deleteAccount: t('accountActions.deleteAccount')
  };

  const testHistoryTranslations = {
    title: t('testHistory.title'),
    subtitle: t('testHistory.subtitle'),
    viewAll: t('testHistory.viewAll'),
    viewAllReports: t('testHistory.viewAllReports'),
    noTests: t('testHistory.noTests'),
    startTest: t('testHistory.startTest'),
    takeFirstTest: t('testHistory.takeFirstTest'),
    status: {
      analyzed: t('testHistory.status.analyzed'),
      locked: t('testHistory.status.locked'),
      completed: t('testHistory.status.completed')
    }
  };

  const preferencesTranslations = {
    title: t('preferences.title'),
    notifications: t('preferences.notifications'),
    dataPrivacy: t('preferences.dataPrivacy'),
    saveChanges: t('preferences.saveChanges'),
    saved: t('preferences.saved'),
    optIn: t('preferences.optIn'),
    optOut: t('preferences.optOut')
  };

  return (
    <main className="flex flex-col min-h-screen pt-20 md:pt-24 pb-12 px-4 bg-gray-50">
      <div className="container mx-auto max-w-6xl">
        {/* Page Header with improved visual hierarchy */}
        <div className="mb-6 md:mb-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6 md:p-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold mb-2 text-gray-900">{t('heading')}</h1>
              <p className="text-gray-600 text-sm md:text-base max-w-xl">{t('subtitle')}</p>
            </div>
            {/* Only show "Take your first test" button if user has no test results */}
            {user.testResults.length === 0 && (
              <div className="flex items-center mt-2 md:mt-0">
                <Link
                  href={`/${locale === 'en' ? '' : locale + '/'}`}
                  className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                  </svg>
                  {t('testHistory.startTest')}
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* 未绑定测试横幅 */}
        <UnboundTestsBanner locale={locale} />

        {/* 主要内容区域 - 优化的响应式布局 */}
        <div className="space-y-6">
          {/* 测试历史 - 移动到顶部最显眼位置 */}
          <div className="w-full">
            <TestHistorySection
              testResults={user.testResults}
              locale={locale}
              translations={testHistoryTranslations}
            />
          </div>

          {/* 下方内容区域 - 响应式布局：移动端堆叠，桌面端并排 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 左侧：个人信息 */}
            <div className="space-y-6">
              {/* 个人信息卡片 - 优化布局和视觉层次 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 transition-shadow hover:shadow-md">
                <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                  {t('personalInfo.title')}
                </h2>

                <div className="flex flex-col items-center mb-6">
                  <div className="relative w-20 h-20 rounded-full overflow-hidden bg-blue-100 border-2 border-blue-200 mb-4 shadow-sm">
                    {user.image ? (
                      <Image
                        src={user.image}
                        alt={user.name || 'User'}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-600 text-3xl font-medium">
                        {user.name?.[0]?.toUpperCase() || user.email?.[0]?.toUpperCase() || 'U'}
                      </div>
                    )}
                  </div>

                  <h3 className="text-lg font-medium text-center">{user.name || 'User'}</h3>
                  <p className="text-gray-500 text-sm text-center break-all max-w-full truncate">{user.email}</p>
                </div>

                <div className="space-y-3 border-t border-gray-100 pt-4">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                    <span className="text-gray-600 text-sm">{t('personalInfo.email')}</span>
                    <span className="font-medium text-gray-900 text-sm truncate max-w-full break-all">{user.email}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                    <span className="text-gray-600 text-sm">{t('personalInfo.joinedOn')}</span>
                    <span className="font-medium text-gray-900 text-sm">{joinDate}</span>
                  </div>
                </div>
              </div>

              {/* 账户操作卡片 */}
              <ProfileActions
                locale={locale}
                translations={accountActionsTranslations}
              />
            </div>

            {/* 右侧：偏好设置 */}
            <div className="space-y-6">
              <PreferencesForm
                user={user}
                locale={locale}
                translations={preferencesTranslations}
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  );
} 