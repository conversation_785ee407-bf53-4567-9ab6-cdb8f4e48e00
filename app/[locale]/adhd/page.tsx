import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import AdhdPreScreening from './components/AdhdPreScreening';
import AdhdPageWrapper from './components/AdhdPageWrapper';

export async function generateMetadata({
    params
}: {
    params: Promise<{ locale: string }>
}): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.adhd.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/adhd`,
        }
    };
}

export default async function AdhdPage() {
    const t = await getTranslations('pages.adhd.content');

    return (
        <AdhdPageWrapper>
            <div className="pt-20 sm:pt-24 md:pt-28 pb-12 min-h-screen">
                <div className="container mx-auto px-4 sm:px-6">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center space-y-4 sm:space-y-6 mb-6 sm:mb-8">
                            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tight text-gradient">
                                {t('title')}
                            </h1>
                            <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto">
                                {t('description')}
                            </p>
                        </div>

                        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6 md:p-8">
                            <AdhdPreScreening />
                        </div>
                    </div>
                </div>
            </div>
        </AdhdPageWrapper>
    );
} 