'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { useLocale } from 'next-intl';
import { ChevronLeft, ChevronRight, ExternalLink, RefreshCcw, User } from 'lucide-react';
import IframeResizer from '@/components/IframeResizer';
import { useSession } from 'next-auth/react';

// Dynamically import the AdhdReport component to avoid SSR issues
const AdhdReport = dynamic(() => import('@/app/components/AdhdReport'), {
  ssr: false,
  loading: () => <div className="py-8 text-center text-gray-500">Loading report generator...</div>
});

// Define the question type
interface Question {
  id: number;
  text: string;
  isScreener: boolean; // First 6 questions are part of the screener
  category: 'inattention' | 'hyperactivity';
}

// Define the answer options
const answerOptions = [
  { value: 0, label: 'never' },
  { value: 1, label: 'rarely' },
  { value: 2, label: 'sometimes' },
  { value: 3, label: 'often' },
  { value: 4, label: 'veryOften' }
];

export default function AdhdAdultTest() {
  const t = useTranslations('pages.adhd');
  const router = useRouter();
  const params = useParams();
  const locale = (params?.locale as string) || 'en';
  const localeObj = useLocale();
  const { data: session } = useSession();

  // State for user answers
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [reportLoading, setReportLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [submitError, setSubmitError] = useState<string>('');
  const [isIframe, setIsIframe] = useState(false);
  const [showResultModal, setShowResultModal] = useState(false);
  const [resultUrl, setResultUrl] = useState('');
  const [submitMessage, setSubmitMessage] = useState('');

  // ASRS test questions (18-item version)
  const questions: Question[] = [
    // Part A: Screener questions (1-6)
    { id: 1, text: 'adultQuestions.q1', isScreener: true, category: 'inattention' },
    { id: 2, text: 'adultQuestions.q2', isScreener: true, category: 'inattention' },
    { id: 3, text: 'adultQuestions.q3', isScreener: true, category: 'inattention' },
    { id: 4, text: 'adultQuestions.q4', isScreener: true, category: 'hyperactivity' },
    { id: 5, text: 'adultQuestions.q5', isScreener: true, category: 'hyperactivity' },
    { id: 6, text: 'adultQuestions.q6', isScreener: true, category: 'hyperactivity' },

    // Part B: Additional questions (7-18)
    { id: 7, text: 'adultQuestions.q7', isScreener: false, category: 'inattention' },
    { id: 8, text: 'adultQuestions.q8', isScreener: false, category: 'inattention' },
    { id: 9, text: 'adultQuestions.q9', isScreener: false, category: 'inattention' },
    { id: 10, text: 'adultQuestions.q10', isScreener: false, category: 'inattention' },
    { id: 11, text: 'adultQuestions.q11', isScreener: false, category: 'inattention' },
    { id: 12, text: 'adultQuestions.q12', isScreener: false, category: 'inattention' },
    { id: 13, text: 'adultQuestions.q13', isScreener: false, category: 'hyperactivity' },
    { id: 14, text: 'adultQuestions.q14', isScreener: false, category: 'hyperactivity' },
    { id: 15, text: 'adultQuestions.q15', isScreener: false, category: 'hyperactivity' },
    { id: 16, text: 'adultQuestions.q16', isScreener: false, category: 'hyperactivity' },
    { id: 17, text: 'adultQuestions.q17', isScreener: false, category: 'hyperactivity' },
    { id: 18, text: 'adultQuestions.q18', isScreener: false, category: 'hyperactivity' }
  ];

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];

  // Handle answer selection
  const handleAnswerSelect = (questionId: number, value: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // Calculate scores
  const calculateScores = () => {
    // ASRS scoring is based on threshold values for each question
    // For the screener (Part A), questions have specific thresholds
    const screenerThresholds = {
      1: 2, // "Sometimes" or higher
      2: 3, // "Often" or higher
      3: 3, // "Often" or higher
      4: 3, // "Often" or higher
      5: 3, // "Often" or higher
      6: 3  // "Often" or higher
    };

    // Count positive screener items
    const screenerPositive = questions
      .filter(q => q.isScreener)
      .filter(q => answers[q.id] >= screenerThresholds[q.id as keyof typeof screenerThresholds])
      .length;

    // Calculate category scores
    const inattentionQuestions = questions.filter(q => q.category === 'inattention');
    const hyperactivityQuestions = questions.filter(q => q.category === 'hyperactivity');

    const inattentionScore = inattentionQuestions.reduce((sum, q) => sum + (answers[q.id] || 0), 0);
    const hyperactivityScore = hyperactivityQuestions.reduce((sum, q) => sum + (answers[q.id] || 0), 0);
    const totalScore = inattentionScore + hyperactivityScore;

    // Calculate mean scores
    const inattentionMean = inattentionScore / inattentionQuestions.length;
    const hyperactivityMean = hyperactivityScore / hyperactivityQuestions.length;

    // Determine risk level based on screener
    let riskLevel: 'low' | 'moderate' | 'high' = 'low';
    if (screenerPositive >= 4) {
      riskLevel = 'high';
    } else if (screenerPositive >= 2) {
      riskLevel = 'moderate';
    }

    return {
      screener: {
        positiveItems: screenerPositive,
        total: 6,
        riskLevel
      },
      inattention: {
        score: inattentionScore,
        mean: inattentionMean,
        max: inattentionQuestions.length * 4
      },
      hyperactivity: {
        score: hyperactivityScore,
        mean: hyperactivityMean,
        max: hyperactivityQuestions.length * 4
      },
      total: {
        score: totalScore,
        max: questions.length * 4
      }
    };
  };

  // Move to next question or show email form when all questions are answered
  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      // Scroll to top of test container
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          const testContainer = document.getElementById('adhd-adult-test-container');
          if (testContainer) {
            testContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
    } else {
      // All questions answered, show email form
      setShowEmailForm(true);
    }
  };

  // Go back to previous question
  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Reset the test
  const resetTest = () => {
    setAnswers({});
    setCurrentQuestionIndex(0);
    setReportLoading(false);
    setEmail('');
    setShowEmailForm(false);
    setEmailError('');
    setSubmitError('');
    setShowResultModal(false);
    setResultUrl('');
    setSubmitMessage('');
  };

  // Check if current question is the first in its part (A or B)
  const isFirstInPart = (question: Question) => {
    return question.id === 1 || question.id === 7;
  };

  // Handle email change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
  };

  // Validate email format
  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  // Check if user is logged in and set email
  useEffect(() => {
    if (session?.user?.email) {
      setEmail(session.user.email);
    }
  }, [session]);

  // Submit email and save test results
  const submitEmail = async () => {
    if (!email) {
      setEmailError(t('email.required'));
      return;
    }

    if (!validateEmail(email)) {
      setEmailError(t('email.invalid'));
      return;
    }

    setReportLoading(true);

    try {
      // 计算分数
      const { total, inattention, hyperactivity, screener } = calculateScores();

      // 构建分数对象
      const scores = {
        total: {
          score: total.score,
          max: total.max,
          percentage: (total.score / total.max) * 100,
        },
        inattention: {
          score: inattention.score,
          max: inattention.max,
          mean: inattention.mean,
        },
        hyperactivity: {
          score: hyperactivity.score,
          max: hyperactivity.max,
          mean: hyperactivity.mean,
        },
        screener: {
          positiveItems: screener.positiveItems,
          total: screener.total,
          riskLevel: screener.riskLevel,
        }
      };

      // 调用 API 保存测试结果
      const requestData = {
        testType: 'adult',
        email: email,
        answers: answers,
        scores: scores,
        userId: session?.user?.id,
        metadata: {
          locale: locale,
        },
      };

      console.log('Sending data to API:', JSON.stringify(requestData, null, 2));

      const response = await fetch('/api/adhd/save-result', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();
      console.log('API response:', data);

      if (!response.ok) {
        throw new Error(data.error || data.details || 'Failed to save test result');
      }

      // 获取结果页面URL
      if (data.id) {
        console.log(`Got test ID: ${data.id}`);

        // 使用后端返回的 reportUrl（带 accessToken）
        const resultPageUrl = data.reportUrl;

        // 保存结果URL
        setResultUrl(resultPageUrl);

        // 检查是否已自动绑定到用户账户
        const isUserLoggedIn = !!session?.user;

        // 设置提示信息
        if (isUserLoggedIn) {
          setSubmitMessage(t('email.resultSaved') || "Your test result has been automatically saved to your account.");
        }

        // 在新窗口中打开结果页面
        window.open(resultPageUrl, '_blank');

        // 显示结果弹窗
        setShowResultModal(true);
        setReportLoading(false);
      } else {
        throw new Error('No test ID returned from server');
      }

    } catch (error: any) {
      console.error('Error saving test result:', error);
      setSubmitError(t('email.submitError') + (error.message ? `: ${error.message}` : ''));
      setReportLoading(false);
    }
  };

  // 重新开始测试
  const restartTest = () => {
    setCurrentQuestionIndex(0);
    setAnswers({});
    setShowEmailForm(false);
    setEmail('');
    setEmailError('');
    setSubmitError('');
    setShowResultModal(false);
    setResultUrl('');
    setSubmitMessage('');
  };

  // 结果蒙层弹窗
  const renderResultModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
          <h2 className="text-xl font-bold mb-4 text-center">
            {t('result.modalTitle') || '测试完成'}
          </h2>

          <p className="mb-6 text-center">
            {t('result.modalDescription') || '您的测试结果已在新窗口中打开。如果您没有看到结果页面，请点击下方按钮再次查看。'}
          </p>

          {/* 显示提交消息，如果有的话 */}
          {submitMessage && (
            <div className="mb-6 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md text-green-600 dark:text-green-400 text-sm flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {submitMessage}
            </div>
          )}

          <div className="flex flex-col space-y-4">
            <button
              onClick={() => window.open(resultUrl, '_blank')}
              className="flex items-center justify-center px-4 py-3 rounded-lg bg-blue-600 hover:bg-blue-700 text-white font-medium"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              {t('result.openResultButton') || '查看测试结果'}
            </button>

            <button
              onClick={restartTest}
              className="flex items-center justify-center px-4 py-3 rounded-lg bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium"
            >
              <RefreshCcw className="mr-2 h-4 w-4" />
              {t('result.restartButton') || '重新测试'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render email collection form
  const renderEmailForm = () => {
    const score = calculateScores();
    const isLoggedIn = !!session?.user;

    return (
      <div className="space-y-6" id="adhd-adult-test-container">
        <h2 className="text-xl sm:text-2xl font-semibold text-center mb-4">
          {t('email.title')}
        </h2>

        <div className="p-5 sm:p-6 bg-gray-50 rounded-lg shadow-sm">
          <p className="text-base mb-5">
            {t('email.description')}
          </p>

          <div className="space-y-5">
            <div className="mb-6">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('email.label')}
              </label>
              <div className="relative">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={handleEmailChange}
                  placeholder={t('email.placeholder')}
                  className={`w-full p-2 pl-10 border-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                    ${emailError ? 'border-red-500' : 'border-gray-300'}`}
                  disabled={reportLoading}
                />
                {isLoggedIn && (
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  </div>
                )}
              </div>
              {isLoggedIn && (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <span className="inline-flex items-center justify-center p-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </span>
                  {t('email.loggedInMessage') || "Using your account email"}
                </p>
              )}
              {emailError && (
                <p className="mt-1 text-red-600 dark:text-red-400 text-sm">
                  {emailError}
                </p>
              )}
            </div>

            <div className="text-sm text-gray-500 bg-gray-100 p-3 rounded-md">
              <p>{t('email.privacy')}</p>
            </div>

            <div className="pt-3">
              <button
                onClick={submitEmail}
                disabled={reportLoading}
                className={`w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${reportLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                {reportLoading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('email.processing')}
                  </span>
                ) : t('email.submit')}
              </button>

              <button
                onClick={resetTest}
                disabled={reportLoading}
                className="w-full mt-3 py-2.5 px-6 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
              >
                {t('buttons.previous')}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Check if we're in iframe mode
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsIframe(window.location.pathname.includes('/i/adhd'));
    }
  }, []);

  if (showResultModal) {
    return (
      <div id="adhd-adult-test-container">
        {renderResultModal()}
        {isIframe && (
          <IframeResizer
            testType="adhd"
            observeElement="#adhd-adult-test-container"
            compactMode={true}
            resizeInterval={300}
          />
        )}
      </div>
    );
  }

  if (showEmailForm) {
    return (
      <>
        {renderEmailForm()}
        {isIframe && (
          <IframeResizer
            testType="adhd"
            observeElement="#adhd-adult-test-container"
            compactMode={true}
            resizeInterval={300}
          />
        )}
      </>
    );
  }

  return (
    <>
      <div className="space-y-6" id="adhd-adult-test-container">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
          <h2 className="text-lg sm:text-xl font-semibold">{t('adultTest.title')}</h2>
          <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
            {t('test.questionProgress', { current: currentQuestionIndex + 1, total: questions.length })}
          </span>
        </div>

        {currentQuestionIndex === 0 && (
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm font-medium text-blue-800">
              {t('adultTest.instructions')}
            </p>
          </div>
        )}

        <div className="p-4 sm:p-6 bg-gray-50 rounded-lg space-y-6">
          {/* Show section header for the first question in each part */}
          {isFirstInPart(currentQuestion) && (
            <div className="mb-4">
              <h3 className="font-medium text-blue-600 text-sm sm:text-base uppercase tracking-wider px-3 py-2 bg-blue-50 rounded-md inline-block">
                {currentQuestion.id === 1 ? t('adultTest.sections.partA') : t('adultTest.sections.partB')}
              </h3>

              <div className="mt-2 text-sm text-gray-600">
                {currentQuestion.id === 1 ?
                  t('adultTest.sectionDescriptions.partA') :
                  t('adultTest.sectionDescriptions.partB')}
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div className="relative">
              <div className="flex flex-wrap items-start gap-2 mb-2">
                <p className="font-medium text-base sm:text-lg pr-12">
                  {currentQuestion.id}. {t(currentQuestion.text)}
                </p>
                {currentQuestion.isScreener && (
                  <div className="absolute right-0 top-0">
                    <span className="px-2.5 py-1 text-xs font-medium bg-amber-100 text-amber-800 rounded-md inline-block">
                      {t('adultTest.screener')}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="grid gap-2.5">
              {answerOptions.map(option => (
                <button
                  key={option.value}
                  onClick={() => handleAnswerSelect(currentQuestion.id, option.value)}
                  className={`py-3.5 px-4 text-left rounded-lg border-2 transition-colors
                  ${answers[currentQuestion.id] === option.value
                      ? 'bg-blue-100 border-blue-500 text-blue-700 font-medium'
                      : 'bg-white border-gray-300 hover:bg-gray-50'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 active:scale-99 touch-manipulation`}
                  aria-label={t(`options.${option.label}`)}
                >
                  {t(`options.${option.label}`)}
                </button>
              ))}
            </div>
          </div>

          <div className="mt-6">
            <div className="flex flex-wrap justify-between items-center mb-2 text-xs text-gray-500">
              <div className="flex items-center gap-1 mb-1 sm:mb-0">
                <span className={`inline-block ${currentQuestion.category === 'inattention'
                  ? 'text-indigo-600'
                  : 'text-purple-600'
                  }`}>
                  {t(`adultResults.categories.${currentQuestion.category}`)}
                </span>
                {currentQuestion.isScreener && (
                  <span className="ml-1 text-amber-600">• {t('adultTest.screener')}</span>
                )}
              </div>
              <span className="font-medium">{currentQuestionIndex + 1}/{questions.length}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="flex justify-between gap-3 pt-4 sticky bottom-0 bg-white pb-2">
          <button
            onClick={previousQuestion}
            disabled={currentQuestionIndex === 0}
            className={`py-3 flex-1 rounded-lg font-medium transition-colors
            ${currentQuestionIndex === 0
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300 active:bg-gray-400'
              } focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-1`}
          >
            {t('buttons.previous')}
          </button>

          <button
            onClick={nextQuestion}
            disabled={answers[currentQuestion.id] === undefined}
            className={`py-3 flex-1 rounded-lg font-medium transition-colors
            ${answers[currentQuestion.id] === undefined
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
              } text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
          >
            {currentQuestionIndex === questions.length - 1 ? t('buttons.submit') : t('buttons.next')}
          </button>
        </div>
      </div>
      {isIframe && (
        <IframeResizer
          testType="adhd"
          observeElement="#adhd-adult-test-container"
          compactMode={true}
          resizeInterval={300}
        />
      )}
    </>
  );
} 