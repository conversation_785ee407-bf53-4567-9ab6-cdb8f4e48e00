'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { useReferralTracking } from '@/hooks/useReferralTracking';

// Dynamically import the AdhdReport component to avoid SSR issues
const AdhdReport = dynamic(() => import('@/app/components/AdhdReport'), {
  ssr: false,
  loading: () => <div className="py-8 text-center text-gray-500">Loading report generator...</div>
});

// Define the question type
interface Question {
  id: number;
  text: string;
}

// Define the answer options
const answerOptions = [
  { value: 0, label: 'never' },
  { value: 1, label: 'rarely' },
  { value: 2, label: 'sometimes' },
  { value: 3, label: 'often' },
  { value: 4, label: 'veryOften' }
];

export default function AdhdTest() {
  const t = useTranslations('pages.adhd');
  const params = useParams();
  const locale = (params?.locale as string) || 'en';
  const router = useRouter();
  const { getReferralForSubmission } = useReferralTracking();

  // State for user answers
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [email, setEmail] = useState('');
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [reportLoading, setReportLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string>('');

  // ADHD test questions with sections
  const questions: (Question & { section?: string })[] = [
    // Basic symptoms (1-6)
    { id: 1, text: 'questions.q1', section: 'basic' },
    { id: 2, text: 'questions.q2', section: 'basic' },
    { id: 3, text: 'questions.q3', section: 'basic' },
    { id: 4, text: 'questions.q4', section: 'basic' },
    { id: 5, text: 'questions.q5', section: 'basic' },
    { id: 6, text: 'questions.q6', section: 'basic' },

    // Attention problems (7-12)
    { id: 7, text: 'questions.q7', section: 'attention' },
    { id: 8, text: 'questions.q8', section: 'attention' },
    { id: 9, text: 'questions.q9', section: 'attention' },
    { id: 10, text: 'questions.q10', section: 'attention' },
    { id: 11, text: 'questions.q11', section: 'attention' },
    { id: 12, text: 'questions.q12', section: 'attention' },

    // Hyperactivity/Impulsivity (13-18)
    { id: 13, text: 'questions.q13', section: 'hyperactivity' },
    { id: 14, text: 'questions.q14', section: 'hyperactivity' },
    { id: 15, text: 'questions.q15', section: 'hyperactivity' },
    { id: 16, text: 'questions.q16', section: 'hyperactivity' },
    { id: 17, text: 'questions.q17', section: 'hyperactivity' },
    { id: 18, text: 'questions.q18', section: 'hyperactivity' },
  ];

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];

  // Handle answer selection
  const handleAnswerSelect = (questionId: number, value: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // Calculate score
  const calculateScore = () => {
    return Object.values(answers).reduce((sum, value) => sum + value, 0);
  };

  // Get result interpretation
  const getResultInterpretation = (score: number) => {
    const maxScore = questions.length * 4;
    const percentage = (score / maxScore) * 100;

    if (percentage >= 70) {
      return 'results.high';
    } else if (percentage >= 40) {
      return 'results.moderate';
    } else {
      return 'results.low';
    }
  };

  // Move to next question or show email form when all questions are answered
  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      // Scroll to top of test container
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          const testContainer = document.getElementById('adhd-test-container');
          if (testContainer) {
            testContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
    } else {
      // All questions answered, show email form
      setShowEmailForm(true);
    }
  };

  // Go back to previous question
  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Handle email change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
  };

  // Validate email format
  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  // Submit email and save test results
  const submitEmail = async () => {
    if (!email) {
      setEmailError(t('email.required'));
      return;
    }

    if (!validateEmail(email)) {
      setEmailError(t('email.invalid'));
      return;
    }

    setReportLoading(true);

    try {
      // 计算总分
      const score = calculateScore();
      const interpretation = getResultInterpretation(score);

      // 构建分数对象
      const scores = {
        total: score,
        maxScore: questions.length * 4,
        percentage: (score / (questions.length * 4)) * 100,
        interpretation: t(interpretation),
      };

      // 调用 API 保存测试结果
      const requestData = {
        testType: 'general',
        email: email,
        answers: answers,
        scores: scores,
        metadata: {
          locale: locale,
          ...(getReferralForSubmission() && { referredBy: getReferralForSubmission() })
        },
      };

      console.log('Sending data to API:', JSON.stringify(requestData, null, 2));

      const response = await fetch('/api/adhd/save-result', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();
      console.log('API response:', data);

      if (!response.ok) {
        throw new Error(data.error || data.details || 'Failed to save test result');
      }

      // 使用后端返回的 reportUrl（带 accessToken）
      const resultPageUrl = data.reportUrl;
      router.push(resultPageUrl);

    } catch (error: any) {
      console.error('Error saving test result:', error);
      setSubmitError(t('email.submitError') + (error.message ? `: ${error.message}` : ''));
      setReportLoading(false);
    }
  };

  // Reset the test
  const resetTest = () => {
    setAnswers({});
    setCurrentQuestionIndex(0);
    setEmail('');
    setShowEmailForm(false);
    setEmailError('');
    setReportLoading(false);
    setSubmitError('');
  };

  // Render email collection form
  const renderEmailForm = () => {
    return (
      <div className="space-y-6" id="adhd-test-container">
        <h2 className="text-xl sm:text-2xl font-semibold text-center mb-4">
          {t('email.title')}
        </h2>

        <div className="p-5 sm:p-6 bg-gray-50 rounded-lg shadow-sm">
          <p className="text-base mb-5">
            {t('email.description')}
          </p>

          <div className="space-y-5">
            <div>
              <label htmlFor="email-input" className="block text-sm font-medium mb-2">
                {t('email.label')}
              </label>
              <input
                id="email-input"
                type="email"
                value={email}
                onChange={handleEmailChange}
                className={`w-full px-4 py-3 border-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                  ${emailError ? 'border-red-500' : 'border-gray-300'}`}
                placeholder={t('email.placeholder')}
                autoComplete="email"
                inputMode="email"
              />
              {emailError && (
                <p className="mt-2 text-sm text-red-600 font-medium">{emailError}</p>
              )}
              {submitError && (
                <p className="mt-2 text-sm text-red-600 font-medium">{submitError}</p>
              )}
            </div>

            <div className="text-sm text-gray-500 bg-gray-100 p-3 rounded-md">
              <p>{t('email.privacy')}</p>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:justify-between">
          <button
            onClick={() => setShowEmailForm(false)}
            className="w-full sm:w-auto order-2 sm:order-1 py-2.5 px-6 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
          >
            {t('buttons.previous')}
          </button>

          <button
            onClick={submitEmail}
            disabled={reportLoading}
            className={`w-full sm:w-auto order-1 sm:order-2 py-3 px-8 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${reportLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {reportLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('email.processing')}
              </span>
            ) : t('email.submit')}
          </button>
        </div>
      </div>
    );
  };

  // Check if current question is the first in its section
  const isFirstInSection = (index: number) => {
    if (index === 0) return true;

    const currentSection = questions[index].section;
    const previousSection = questions[index - 1].section;

    return currentSection !== previousSection;
  };

  // Render email form if all questions answered
  if (showEmailForm) {
    return renderEmailForm();
  }

  // Render single question
  return (
    <div className="space-y-6" id="adhd-test-container">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
        <h2 className="text-lg sm:text-xl font-semibold">{t('test.title')}</h2>
        <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
          {t('test.questionProgress', { current: currentQuestionIndex + 1, total: questions.length })}
        </span>
      </div>

      {isFirstInSection(currentQuestionIndex) && (
        <div className="mb-4">
          <h3 className="font-medium text-blue-600 text-sm sm:text-base uppercase tracking-wider px-3 py-2 bg-blue-50 rounded-md inline-block">
            {t(`test.sections.${currentQuestion.section}`)}
          </h3>
        </div>
      )}

      <div className="p-4 sm:p-6 bg-gray-50 rounded-lg space-y-6">
        <div className="space-y-4">
          <p className="font-medium text-base sm:text-lg">
            {currentQuestionIndex + 1}. {t(currentQuestion.text)}
          </p>

          <div className="grid gap-2.5">
            {answerOptions.map(option => (
              <button
                key={option.value}
                onClick={() => handleAnswerSelect(currentQuestion.id, option.value)}
                className={`py-3.5 px-4 text-left rounded-lg border-2 transition-colors
                  ${answers[currentQuestion.id] === option.value
                    ? 'bg-blue-100 border-blue-500 text-blue-700 font-medium'
                    : 'bg-white border-gray-300 hover:bg-gray-50'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 active:scale-99 touch-manipulation`}
                aria-label={t(`options.${option.label}`)}
              >
                {t(`options.${option.label}`)}
              </button>
            ))}
          </div>
        </div>

        <div className="mt-6">
          <div className="flex flex-wrap justify-between items-center mb-2 text-xs text-gray-500">
            <span className="inline-block text-blue-600 font-medium">
              {t(`test.sections.${currentQuestion.section}`)}
            </span>
            <span className="font-medium">{currentQuestionIndex + 1}/{questions.length}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>

      <div className="flex justify-between gap-3 pt-4 sticky bottom-0 bg-white pb-2">
        <button
          onClick={previousQuestion}
          disabled={currentQuestionIndex === 0}
          className={`py-3 flex-1 rounded-lg font-medium transition-colors
            ${currentQuestionIndex === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300 active:bg-gray-400'
            } focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-1`}
        >
          {t('buttons.previous')}
        </button>

        <button
          onClick={nextQuestion}
          disabled={answers[currentQuestion.id] === undefined}
          className={`py-3 flex-1 rounded-lg font-medium transition-colors
            ${answers[currentQuestion.id] === undefined
              ? 'bg-blue-300 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
            } text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
        >
          {currentQuestionIndex === questions.length - 1 ? t('buttons.submit') : t('buttons.next')}
        </button>
      </div>
    </div>
  );
} 