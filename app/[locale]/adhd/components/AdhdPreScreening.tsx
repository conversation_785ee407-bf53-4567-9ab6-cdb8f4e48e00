'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useReferralTracking } from '@/hooks/useReferralTracking';
import AdhdTest from './AdhdTest';
import AdhdChildTest from './AdhdChildTest';
import AdhdAdultTest from './AdhdAdultTest';

type TestType = 'none' | 'child' | 'adult' | 'general';

export default function AdhdPreScreening() {
  const t = useTranslations('pages.adhd');
  const [currentStep, setCurrentStep] = useState(1);
  const [answers, setAnswers] = useState<Record<string, string | number>>({});
  const [selectedTest, setSelectedTest] = useState<TestType>('none');

  // 初始化推荐追踪
  useReferralTracking();

  // Handle answer selection
  const handleAnswer = (question: string, answer: string | number) => {
    setAnswers(prev => ({
      ...prev,
      [question]: answer
    }));
  };

  // Move to next step
  const nextStep = () => {
    setCurrentStep(prev => prev + 1);
  };

  // Determine which test to show based on answers
  const determineTest = () => {
    const age = answers.age as number;
    const purpose = answers.purpose as string;
    const symptoms = answers.symptoms as string;

    if (age < 18) {
      // Child/Adolescent test
      setSelectedTest('child');
    } else {
      // Adult test
      setSelectedTest('adult');
    }

    // If we want to use the general test for certain cases
    if (purpose === 'initial_screening' && symptoms === 'mixed') {
      setSelectedTest('general');
    }

    nextStep();
  };

  // Render the appropriate test based on selection
  const renderSelectedTest = () => {
    switch (selectedTest) {
      case 'child':
        return <AdhdChildTest />;
      case 'adult':
        return <AdhdAdultTest />;
      case 'general':
      default:
        return <AdhdTest />;
    }
  };

  // Step 1: Age selection
  const renderAgeStep = () => {
    return (
      <div className="space-y-6">
        <h2 className="text-xl sm:text-2xl font-semibold text-center mb-4">
          {t('prescreening.age.title')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('prescreening.age.description')}
        </p>

        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <label htmlFor="age-input" className="text-base font-medium min-w-[120px]">
              {t('prescreening.age.label')}
            </label>
            <input
              id="age-input"
              type="number"
              min="3"
              max="99"
              value={answers.age || ''}
              onChange={(e) => handleAnswer('age', parseInt(e.target.value) || 0)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg
                        bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={t('prescreening.age.placeholder')}
              aria-label="Age of the person taking the test"
            />
          </div>
          <p className="text-sm text-gray-500 italic">
            {t('prescreening.note')}
          </p>
        </div>

        <div className="flex justify-end mt-8">
          <button
            onClick={nextStep}
            disabled={!answers.age || (answers.age as number) < 3}
            className={`py-2 px-6 rounded-lg font-medium transition-colors
              ${!answers.age || (answers.age as number) < 3
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
              } text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
          >
            {t('buttons.next')}
          </button>
        </div>
      </div>
    );
  };

  // Step 2: Purpose selection
  const renderPurposeStep = () => {
    const options = [
      { value: 'initial_screening', label: 'prescreening.purpose.options.initial' },
      { value: 'diagnosis', label: 'prescreening.purpose.options.diagnosis' },
      { value: 'treatment_tracking', label: 'prescreening.purpose.options.tracking' }
    ];

    return (
      <div className="space-y-6">
        <h2 className="text-xl sm:text-2xl font-semibold text-center mb-4">
          {t('prescreening.purpose.title')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('prescreening.purpose.description')}
        </p>

        <div className="space-y-3">
          {options.map(option => (
            <button
              key={option.value}
              onClick={() => handleAnswer('purpose', option.value)}
              className={`w-full py-3 px-4 text-left rounded-lg border transition-colors
                ${answers.purpose === option.value
                  ? 'bg-blue-100 border-blue-500 text-blue-700'
                  : 'bg-white border-gray-300 hover:bg-gray-50'
                } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
              aria-label={`Select purpose: ${t(option.label)}`}
            >
              {t(option.label)}
            </button>
          ))}
        </div>

        <div className="flex justify-between mt-8">
          <button
            onClick={() => setCurrentStep(1)}
            className={`py-2 px-4 rounded-lg font-medium transition-colors
              ${!answers.purpose
                ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                : 'bg-gray-200'
              } focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2`}
          >
            {t('buttons.previous')}
          </button>

          <button
            onClick={nextStep}
            disabled={!answers.purpose}
            className={`py-2 px-6 rounded-lg font-medium transition-colors
              ${!answers.purpose
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
              } text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
          >
            {t('buttons.next')}
          </button>
        </div>
      </div>
    );
  };

  // Step 3: Symptoms selection
  const renderSymptomsStep = () => {
    const options = [
      { value: 'inattention', label: 'prescreening.symptoms.options.inattention' },
      { value: 'hyperactivity', label: 'prescreening.symptoms.options.hyperactivity' },
      { value: 'mixed', label: 'prescreening.symptoms.options.mixed' },
      { value: 'comorbid', label: 'prescreening.symptoms.options.comorbid' }
    ];

    return (
      <div className="space-y-6">
        <h2 className="text-xl sm:text-2xl font-semibold text-center mb-4">
          {t('prescreening.symptoms.title')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('prescreening.symptoms.description')}
        </p>

        <div className="space-y-3">
          {options.map(option => (
            <button
              key={option.value}
              onClick={() => handleAnswer('symptoms', option.value)}
              className={`w-full py-3 px-4 text-left rounded-lg border transition-colors
                ${answers.symptoms === option.value
                  ? 'bg-blue-100 border-blue-500 text-blue-700'
                  : 'bg-white border-gray-300 hover:bg-gray-50'
                } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
              aria-label={`Select symptom type: ${t(option.label)}`}
            >
              {t(option.label)}
            </button>
          ))}
        </div>

        <div className="flex justify-between mt-8">
          <button
            onClick={() => setCurrentStep(2)}
            className={`py-2 px-4 rounded-lg font-medium transition-colors
              ${!answers.symptoms
                ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                : 'bg-gray-200'
              } focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2`}
          >
            {t('buttons.previous')}
          </button>

          <button
            onClick={determineTest}
            disabled={!answers.symptoms}
            className={`py-2 px-6 rounded-lg font-medium transition-colors
              ${!answers.symptoms
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
              } text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
          >
            {t('buttons.start_test')}
          </button>
        </div>
      </div>
    );
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return renderAgeStep();
      case 2:
        return renderPurposeStep();
      case 3:
        return renderSymptomsStep();
      case 4:
        return renderSelectedTest();
      default:
        return renderAgeStep();
    }
  };

  return (
    <div className="space-y-6" id="adhd-prescreening-container">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <p className="text-yellow-800 text-sm">
          {t('prescreening.disclaimer')}
        </p>
      </div>

      {currentStep < 4 && (
        <div className="flex justify-center mb-6">
          <div className="flex items-center space-x-2">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${currentStep >= step
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                    }`}
                >
                  {step}
                </div>
                {step < 3 && (
                  <div
                    className={`w-10 h-1 sm:w-16 
                      ${currentStep > step
                        ? 'bg-blue-600'
                        : 'bg-gray-200'
                      }`}
                  ></div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {renderStep()}
    </div>
  );
} 