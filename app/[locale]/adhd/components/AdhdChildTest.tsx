"use client";

import { useState, useEffect, FormEvent } from 'react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { ChevronRight, ExternalLink, RefreshCcw, User } from 'lucide-react';
import IframeResizer from '@/components/IframeResizer';
import { useSession } from 'next-auth/react';
import { useLocale } from 'next-intl';

// Dynamically import the AdhdReport component to avoid SSR issues
const AdhdReport = dynamic(() => import('@/app/components/AdhdReport'), {
  ssr: false,
  loading: () => <div className="py-8 text-center text-gray-500">Loading report generator...</div>
});

// Define the question type
interface Question {
  id: number;
  text: string;
  category: 'inattention' | 'hyperactivity' | 'opposition';
}

// Define the answer options
const answerOptions = [
  { value: 0, label: 'never' },
  { value: 1, label: 'rarely' },
  { value: 2, label: 'sometimes' },
  { value: 3, label: 'often' },
  { value: 4, label: 'veryOften' }
];

export default function AdhdChildTest({ params }: { params?: { locale?: string } }) {
  const t = useTranslations('pages.adhd');
  const router = useRouter();
  const locale = (params?.locale as string) || 'en';
  const localeObj = useLocale();
  const { data: session } = useSession();

  // State for user answers
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [respondent, setRespondent] = useState<'parent' | 'teacher' | 'self'>('parent');
  const [showRespondentSelection, setShowRespondentSelection] = useState(true);
  const [email, setEmail] = useState('');
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [reportLoading, setReportLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string>('');
  const [showResultModal, setShowResultModal] = useState(false);
  const [resultUrl, setResultUrl] = useState('');
  const [isIframe, setIsIframe] = useState(false);

  // Check if user is logged in and set email
  useEffect(() => {
    if (session?.user?.email) {
      setEmail(session.user.email);
    }
  }, [session]);

  // 添加iframe检测
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsIframe(window.location.pathname.includes('/i/adhd'));
    }
  }, []);

  // SNAP-IV test questions (26-item version)
  const questions: Question[] = [
    // Inattention items (1-9)
    { id: 1, text: 'childQuestions.q1', category: 'inattention' },
    { id: 2, text: 'childQuestions.q2', category: 'inattention' },
    { id: 3, text: 'childQuestions.q3', category: 'inattention' },
    { id: 4, text: 'childQuestions.q4', category: 'inattention' },
    { id: 5, text: 'childQuestions.q5', category: 'inattention' },
    { id: 6, text: 'childQuestions.q6', category: 'inattention' },
    { id: 7, text: 'childQuestions.q7', category: 'inattention' },
    { id: 8, text: 'childQuestions.q8', category: 'inattention' },
    { id: 9, text: 'childQuestions.q9', category: 'inattention' },

    // Hyperactivity/Impulsivity items (10-18)
    { id: 10, text: 'childQuestions.q10', category: 'hyperactivity' },
    { id: 11, text: 'childQuestions.q11', category: 'hyperactivity' },
    { id: 12, text: 'childQuestions.q12', category: 'hyperactivity' },
    { id: 13, text: 'childQuestions.q13', category: 'hyperactivity' },
    { id: 14, text: 'childQuestions.q14', category: 'hyperactivity' },
    { id: 15, text: 'childQuestions.q15', category: 'hyperactivity' },
    { id: 16, text: 'childQuestions.q16', category: 'hyperactivity' },
    { id: 17, text: 'childQuestions.q17', category: 'hyperactivity' },
    { id: 18, text: 'childQuestions.q18', category: 'hyperactivity' },

    // Oppositional Defiant Disorder items (19-26)
    { id: 19, text: 'childQuestions.q19', category: 'opposition' },
    { id: 20, text: 'childQuestions.q20', category: 'opposition' },
    { id: 21, text: 'childQuestions.q21', category: 'opposition' },
    { id: 22, text: 'childQuestions.q22', category: 'opposition' },
    { id: 23, text: 'childQuestions.q23', category: 'opposition' },
    { id: 24, text: 'childQuestions.q24', category: 'opposition' },
    { id: 25, text: 'childQuestions.q25', category: 'opposition' },
    { id: 26, text: 'childQuestions.q26', category: 'opposition' }
  ];

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];

  // Handle answer selection
  const handleAnswerSelect = (questionId: number, value: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // Handle respondent selection
  const handleRespondentChange = (value: 'parent' | 'teacher' | 'self') => {
    setRespondent(value);
  };

  // Start the test after selecting respondent
  const startTest = () => {
    setShowRespondentSelection(false);
  };

  // Calculate scores by category
  const calculateScores = () => {
    const inattentionQuestions = questions.filter(q => q.category === 'inattention');
    const hyperactivityQuestions = questions.filter(q => q.category === 'hyperactivity');
    const oppositionQuestions = questions.filter(q => q.category === 'opposition');

    const inattentionScore = inattentionQuestions.reduce((sum, q) => sum + (answers[q.id] || 0), 0);
    const hyperactivityScore = hyperactivityQuestions.reduce((sum, q) => sum + (answers[q.id] || 0), 0);
    const oppositionScore = oppositionQuestions.reduce((sum, q) => sum + (answers[q.id] || 0), 0);
    const totalScore = inattentionScore + hyperactivityScore + oppositionScore;

    const inattentionMean = inattentionScore / inattentionQuestions.length;
    const hyperactivityMean = hyperactivityScore / hyperactivityQuestions.length;
    const oppositionMean = oppositionScore / oppositionQuestions.length;

    return {
      inattention: {
        score: inattentionScore,
        mean: inattentionMean,
        max: inattentionQuestions.length * 4,
        significant: inattentionMean >= 2.56 // Threshold for clinical significance
      },
      hyperactivity: {
        score: hyperactivityScore,
        mean: hyperactivityMean,
        max: hyperactivityQuestions.length * 4,
        significant: hyperactivityMean >= 1.78
      },
      opposition: {
        score: oppositionScore,
        mean: oppositionMean,
        max: oppositionQuestions.length * 4,
        significant: oppositionMean >= 1.88
      },
      total: {
        score: totalScore,
        max: questions.length * 4
      },
      respondent: respondent
    };
  };

  // Get ADHD subtype based on scores
  const getAdhdSubtype = (scores: ReturnType<typeof calculateScores>) => {
    const { inattention, hyperactivity } = scores;

    if (inattention.significant && hyperactivity.significant) {
      return 'combined';
    } else if (inattention.significant) {
      return 'inattentive';
    } else if (hyperactivity.significant) {
      return 'hyperactive';
    } else {
      return 'none';
    }
  };

  // Move to next question or show email form when all questions are answered
  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      // Scroll to top of test container
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          const testContainer = document.getElementById('adhd-child-test-container');
          if (testContainer) {
            testContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
    } else {
      // All questions answered, show email form
      setShowEmailForm(true);
    }
  };

  // Go back to previous question
  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    } else if (!showRespondentSelection) {
      // Go back to respondent selection
      setShowRespondentSelection(true);
    }
  };

  // Handle email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
  };

  // Validate email format
  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  // Submit email and save test results
  const handleSubmit = async () => {
    if (!email) {
      setEmailError(t('email.required'));
      return;
    }

    if (!validateEmail(email)) {
      setEmailError(t('email.invalid'));
      return;
    }

    setReportLoading(true);

    try {
      const scores = calculateScores();
      // Add subtype to scores
      const subtype = getAdhdSubtype(scores);
      const scoresWithSubtype = { ...scores, subtype };

      // Prepare request data
      const requestData = {
        testType: 'child',
        email: email,
        answers: answers,
        scores: scoresWithSubtype,
        userId: session?.user?.id, // Add user ID if logged in
        metadata: {
          locale: locale,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        }
      };

      console.log('Sending data to API:', JSON.stringify(requestData, null, 2));

      const response = await fetch('/api/adhd/save-result', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();
      console.log('API response:', data);

      if (!response.ok) {
        throw new Error(data.error || data.details || 'Failed to save test result');
      }

      // 获取结果页面URL
      if (data.id) {
        console.log(`Got test ID: ${data.id}`);

        // 使用后端返回的 reportUrl（带 accessToken）
        const resultPageUrl = data.reportUrl;

        setResultUrl(resultPageUrl);
        window.open(resultPageUrl, '_blank');
        setShowResultModal(true);
        setReportLoading(false);
      } else {
        throw new Error('No test ID returned from server');
      }

    } catch (error: any) {
      console.error('Error saving test result:', error);
      setSubmitError(t('email.submitError') + (error.message ? `: ${error.message}` : ''));
      setReportLoading(false);
    }
  };

  // Reset the test
  const resetTest = () => {
    setAnswers({});
    setCurrentQuestionIndex(0);
    setShowRespondentSelection(true);
    setEmail('');
    setShowEmailForm(false);
    setEmailError('');
    setReportLoading(false);
    setSubmitError('');
  };

  // 添加重新测试函数
  const restartTest = () => {
    setCurrentQuestionIndex(0);
    setShowEmailForm(false);
    setEmail('');
    setEmailError('');
    setReportLoading(false);
    setSubmitError('');
    setShowResultModal(false);
    setResultUrl('');
  };

  // 添加结果蒙层弹窗
  const renderResultModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
          <h2 className="text-xl font-bold mb-4 text-center">
            {t('result.modalTitle') || '测试完成'}
          </h2>

          <p className="mb-6 text-center">
            {t('result.modalDescription') || '孩子的测试结果已在新窗口中打开。如果您没有看到结果页面，请点击下方按钮再次查看。'}
          </p>

          <div className="flex flex-col space-y-4">
            <button
              onClick={() => window.open(resultUrl, '_blank')}
              className="flex items-center justify-center px-4 py-3 rounded-lg bg-blue-600 hover:bg-blue-700 text-white font-medium"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              {t('result.openResultButton') || '查看测试结果'}
            </button>

            <button
              onClick={restartTest}
              className="flex items-center justify-center px-4 py-3 rounded-lg bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium"
            >
              <RefreshCcw className="mr-2 h-4 w-4" />
              {t('result.restartButton') || '重新测试'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render respondent selection
  const renderRespondentSelection = () => {
    return (
      <div className="space-y-6">
        <h3 className="text-lg font-medium mb-4">{t('childTest.respondent.title')}</h3>

        <div className="space-y-3">
          <button
            onClick={() => handleRespondentChange('parent')}
            className={`w-full py-3 px-4 text-left rounded-lg border transition-colors
              ${respondent === 'parent'
                ? 'bg-blue-100 border-blue-500 text-blue-700'
                : 'bg-white border-gray-300 hover:bg-gray-50'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
          >
            {t('childTest.respondent.parent')}
          </button>

          <button
            onClick={() => handleRespondentChange('teacher')}
            className={`w-full py-3 px-4 text-left rounded-lg border transition-colors
              ${respondent === 'teacher'
                ? 'bg-blue-100 border-blue-500 text-blue-700'
                : 'bg-white border-gray-300 hover:bg-gray-50'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
          >
            {t('childTest.respondent.teacher')}
          </button>

          <button
            onClick={() => handleRespondentChange('self')}
            className={`w-full py-3 px-4 text-left rounded-lg border transition-colors
              ${respondent === 'self'
                ? 'bg-blue-100 border-blue-500 text-blue-700'
                : 'bg-white border-gray-300 hover:bg-gray-50'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
          >
            {t('childTest.respondent.self')}
          </button>
        </div>

        <div className="flex justify-end mt-4">
          <button
            onClick={startTest}
            className="py-2 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {t('buttons.next')}
          </button>
        </div>
      </div>
    );
  };

  // Render email collection form
  const renderEmailForm = () => {
    const scores = calculateScores();
    const isLoggedIn = !!session?.user;

    return (
      <div className="space-y-6" id="adhd-child-test-container">
        <h2 className="text-xl sm:text-2xl font-semibold text-center mb-4">
          {t('email.title')}
        </h2>

        <div className="p-5 sm:p-6 bg-gray-50 rounded-lg shadow-sm">
          <p className="text-base mb-5">
            {t('email.description')}
          </p>

          <div className="space-y-5">
            <div className="mb-6">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('email.label')}
              </label>
              <div className="relative">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={handleEmailChange}
                  placeholder={t('email.placeholder')}
                  className={`w-full p-2 pl-10 border-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                    ${emailError ? 'border-red-500' : 'border-gray-300'}`}
                  disabled={reportLoading}
                />
                {isLoggedIn && (
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  </div>
                )}
              </div>
              {isLoggedIn && (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <span className="inline-flex items-center justify-center p-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </span>
                  {t('email.loggedInMessage') || "Using your account email"}
                </p>
              )}
              {emailError && (
                <p className="mt-1 text-red-600 dark:text-red-400 text-sm">
                  {emailError}
                </p>
              )}
              {submitError && (
                <p className="mt-1 text-red-600 dark:text-red-400 text-sm">
                  {submitError}
                </p>
              )}
            </div>

            <div className="text-sm text-gray-500 bg-gray-100 p-3 rounded-md">
              <p>{t('email.privacy')}</p>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:justify-between">
          <button
            onClick={() => setShowEmailForm(false)}
            className="w-full sm:w-auto order-2 sm:order-1 py-2.5 px-6 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
          >
            {t('buttons.previous')}
          </button>

          <button
            onClick={handleSubmit}
            disabled={reportLoading}
            className={`w-full sm:w-auto order-1 sm:order-2 py-3 px-8 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${reportLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {reportLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('email.processing')}
              </span>
            ) : t('email.submit')}
          </button>
        </div>
      </div>
    );
  };

  // Render email form if all questions answered
  if (showEmailForm) {
    return (
      <>
        <div id="adhd-child-test-container">
          {renderEmailForm()}
        </div>
        {isIframe && (
          <IframeResizer
            testType="adhd"
            observeElement="#adhd-child-test-container"
            compactMode={true}
            resizeInterval={300}
          />
        )}
      </>
    );
  }

  // 修改组件渲染部分，添加对showResultModal的判断
  if (showResultModal) {
    return (
      <>
        <div id="adhd-child-test-container">
          {renderResultModal()}
        </div>
        {isIframe && (
          <IframeResizer
            testType="adhd"
            observeElement="#adhd-child-test-container"
            compactMode={true}
            resizeInterval={300}
          />
        )}
      </>
    );
  }

  return (
    <>
      <div className="space-y-6" id="adhd-child-test-container">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
          <h2 className="text-lg sm:text-xl font-semibold">{t('childTest.title')}</h2>
          <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
            {t('test.questionProgress', { current: currentQuestionIndex + 1, total: questions.length })}
          </span>
        </div>

        {showRespondentSelection ? (
          renderRespondentSelection()
        ) : (
          <>
            <div className="p-4 sm:p-6 bg-gray-50 rounded-lg space-y-6">
              {/* Category heading if first question in category */}
              {questions.findIndex(q => q.category === currentQuestion.category) === currentQuestionIndex && (
                <div className="mb-4">
                  <h3 className="font-medium text-blue-600 text-sm sm:text-base uppercase tracking-wider px-3 py-2 bg-blue-50 rounded-md inline-block">
                    {t(`childTest.categories.${currentQuestion.category}`)}
                  </h3>

                  <div className="mt-2 text-sm text-gray-600">
                    {currentQuestion.category === 'inattention' && t('childTest.categoryDescriptions.inattention')}
                    {currentQuestion.category === 'hyperactivity' && t('childTest.categoryDescriptions.hyperactivity')}
                    {currentQuestion.category === 'opposition' && t('childTest.categoryDescriptions.opposition')}
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <p className="font-medium text-base sm:text-lg">
                  {currentQuestion.id}. {t(currentQuestion.text)}
                </p>

                <div className="grid gap-2.5">
                  {answerOptions.map(option => (
                    <button
                      key={option.value}
                      onClick={() => handleAnswerSelect(currentQuestion.id, option.value)}
                      className={`py-3.5 px-4 text-left rounded-lg border-2 transition-colors
                      ${answers[currentQuestion.id] === option.value
                          ? 'bg-blue-100 border-blue-500 text-blue-700 font-medium'
                          : 'bg-white border-gray-300 hover:bg-gray-50'
                        } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 active:scale-99 touch-manipulation`}
                      aria-label={t(`options.${option.label}`)}
                    >
                      {t(`options.${option.label}`)}
                    </button>
                  ))}
                </div>
              </div>

              <div className="mt-6">
                <div className="flex flex-wrap justify-between items-center mb-2 text-xs text-gray-500">
                  <div className="flex items-center gap-1 mb-1 sm:mb-0">
                    {currentQuestion.category && (
                      <span className={`inline-block ${currentQuestion.category === 'inattention'
                        ? 'text-indigo-600'
                        : currentQuestion.category === 'hyperactivity'
                          ? 'text-purple-600'
                          : 'text-orange-600'
                        }`}>
                        {t(`childTest.categories.${currentQuestion.category}`)}
                      </span>
                    )}
                  </div>
                  <span className="font-medium">{currentQuestionIndex + 1}/{questions.length}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="flex justify-between gap-3 pt-4 sticky bottom-0 bg-white pb-2">
              <button
                onClick={previousQuestion}
                className="py-3 flex-1 bg-gray-200 text-gray-700 hover:bg-gray-300 active:bg-gray-400 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-1"
              >
                {t('buttons.previous')}
              </button>

              <button
                onClick={nextQuestion}
                disabled={answers[currentQuestion.id] === undefined}
                className={`py-3 flex-1 rounded-lg font-medium transition-colors
                ${answers[currentQuestion.id] === undefined
                    ? 'bg-blue-300 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
                  } text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
              >
                {t('buttons.next')}
              </button>
            </div>
          </>
        )}
      </div>
      {isIframe && (
        <IframeResizer
          testType="adhd"
          observeElement="#adhd-child-test-container"
          compactMode={true}
          resizeInterval={300}
        />
      )}
    </>
  );
} 