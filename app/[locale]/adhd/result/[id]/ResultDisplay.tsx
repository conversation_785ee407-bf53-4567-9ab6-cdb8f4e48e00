'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import type { TestResult } from '@/types/prisma';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { BarChart3, Brain, RefreshCw, AlertTriangle, CheckCircle, Clock, User, Users } from 'lucide-react';

// 动态导入 AdhdReport 组件(避免 SSR 问题)
const AdhdReport = dynamic(() => import('@/app/components/AdhdReport'), {
  ssr: false,
  loading: () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 animate-pulse">
      <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    </div>
  )
});

interface ResultDisplayProps {
  testResult: TestResult;
  locale: string;
}

export default function ResultDisplay({ testResult, locale }: ResultDisplayProps) {
  const t = useTranslations('pages.adhd');
  const { data: session } = useSession();
  const [loaded, setLoaded] = useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [regenerateKey, setRegenerateKey] = useState(0); // 使用key触发组件重新挂载
  const [shouldRegenerate, setShouldRegenerate] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true); // 跟踪是否是首次加载
  const router = useRouter();

  useEffect(() => {
    setLoaded(true);

    // 组件挂载完成后，不再是首次加载
    const timer = setTimeout(() => {
      setIsFirstLoad(false);
    }, 1000); // 给予足够时间完成初始化

    // Trigger pre-generation of report in background
    const preGenerateReport = async () => {
      try {
        const testType = testResult.testType.includes('adult') ? 'adult' : 'child';
        await fetch('/api/adhd/pre-generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            testId: testResult.id,
            testType,
            answers: testResult.answers,
            scores: testResult.scores,
            locale
          })
        });
      } catch (error) {
        console.error('Pre-generation failed:', error);
      }
    };

    preGenerateReport();

    return () => clearTimeout(timer);
  }, [testResult.id, testResult.testType, testResult.answers, testResult.scores, locale]);

  // 处理报告生成状态变化
  const handleGenerationStateChange = (isGenerating: boolean) => {
    setIsGeneratingReport(isGenerating);

    // 当生成完成时，重置重新生成标志
    if (!isGenerating && shouldRegenerate) {
      console.log('[ResultDisplay] 报告生成完成，重置状态');
      setShouldRegenerate(false);
    }
  };

  // 重新生成报告
  const handleRegenerateReport = () => {
    // 如果正在生成，不做任何操作
    if (isGeneratingReport) return;

    console.log('[ResultDisplay] 强制触发报告重新生成，忽略缓存和数据库');

    // 强制重置 key，确保组件完全重新挂载
    const newKey = Date.now();

    // 设置状态
    setShouldRegenerate(true);
    setRegenerateKey(newKey);

    // 确保在新的渲染周期中，regenerate 标志为 true
    setTimeout(() => {
      console.log('[ResultDisplay] 重新挂载组件，regenerate=true, key=', newKey);
    }, 0);
  };

  if (!loaded) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // 根据测试类型渲染不同的结果页面
  const renderTestResults = () => {
    // 获取测试类型
    const { testType, answers, scores } = testResult;
    
    // 临时调试：检查scores的结构
    console.log('[ResultDisplay] Debug scores structure:', {
      testType,
      scoresType: typeof scores,
      scoresKeys: scores ? Object.keys(scores) : null,
      scores: scores
    });

    switch (testType) {
      case 'adult':
        return renderAdultResults(scores);
      case 'child':
        return renderChildResults(scores);
      case 'general':
      default:
        return renderGeneralResults(scores);
    }
  };

  // 安全渲染助手 - 确保所有值都是原始类型
  const safeRender = (value: any): string | number => {
    if (value === null || value === undefined) return 0;
    if (typeof value === 'object') {
      console.warn('[ResultDisplay] Attempted to render object as child:', value);
      return JSON.stringify(value); // 作为最后手段
    }
    return value;
  };

  // 渲染成人 ADHD 测试结果
  const renderAdultResults = (scores: any) => {
    const { total = {}, inattention = {}, hyperactivity = {}, screener = {} } = scores || {};

    return (
      <div className="space-y-4 md:space-y-6">
        {/* 测试结果卡片 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
          <div className="flex items-center mb-4 md:mb-6">
            <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center mr-3 md:mr-4">
              <User className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900 font-heading">
                {t('adultResults.title')}
              </h2>
              <p className="text-sm md:text-base text-gray-600">
                Adult ADHD Self-Report Scale (ASRS)
              </p>
            </div>
          </div>

          {/* 筛查结果摘要 */}
          <div className="mb-6 md:mb-8">
            <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-4 text-gray-900">
              {t('adultResults.summary')}
            </h3>

            <div className="p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4 md:mb-6">
              <p className="text-sm md:text-base text-blue-800 leading-relaxed">
                {t('adultResults.screenerExplanation', {
                  positive: screener.positiveItems || 0,
                  total: screener.total || 6
                })}
              </p>
            </div>

            {/* 分数详情 - 响应式网格布局 */}
            <div className="space-y-4 md:space-y-6">
              <h4 className="text-base md:text-lg font-medium text-gray-900 mb-3 md:mb-4">
                {t('adultResults.scores')}
              </h4>

              {/* 注意力不集中结果 */}
              <div className="p-4 md:p-5 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
                  <div className="flex items-center">
                    <h5 className="font-medium text-sm md:text-base text-gray-900">
                      {t('adultResults.categories.inattention')}
                    </h5>
                    {(inattention.mean || 0) >= 2.0 && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {t('childResults.significant')}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-sm md:text-base">
                    <span className="font-bold text-gray-900">
                      {safeRender(inattention.score)}/{safeRender(inattention.max)}
                    </span>
                    <span className="text-gray-500 ml-2">
                      (Mean: {(inattention.mean || 0).toFixed(2)})
                    </span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 md:h-3 overflow-hidden">
                  <div
                    className={`h-2.5 md:h-3 rounded-full transition-all duration-500 ${(inattention.mean || 0) >= 2.0 ? 'bg-red-500' : 'bg-blue-600'
                      }`}
                    style={{ width: `${((inattention.score || 0) / (inattention.max || 1)) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* 多动冲动结果 */}
              <div className="p-4 md:p-5 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
                  <div className="flex items-center">
                    <h5 className="font-medium text-sm md:text-base text-gray-900">
                      {t('adultResults.categories.hyperactivity')}
                    </h5>
                    {(hyperactivity.mean || 0) >= 2.0 && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {t('childResults.significant')}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-sm md:text-base">
                    <span className="font-bold text-gray-900">
                      {safeRender(hyperactivity.score)}/{safeRender(hyperactivity.max)}
                    </span>
                    <span className="text-gray-500 ml-2">
                      (Mean: {(hyperactivity.mean || 0).toFixed(2)})
                    </span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 md:h-3 overflow-hidden">
                  <div
                    className={`h-2.5 md:h-3 rounded-full transition-all duration-500 ${(hyperactivity.mean || 0) >= 2.0 ? 'bg-red-500' : 'bg-blue-600'
                      }`}
                    style={{ width: `${((hyperactivity.score || 0) / (hyperactivity.max || 1)) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* 总分 */}
              <div className="p-4 md:p-6 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-3">
                  <h5 className="font-semibold text-base md:text-lg text-gray-900 flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                    {t('adultResults.categories.total')}
                  </h5>
                  <span className="text-lg md:text-xl font-bold text-purple-700">
                    {total.score}/{total.max}
                  </span>
                </div>
                <div className="w-full bg-purple-200 rounded-full h-3 md:h-4 overflow-hidden">
                  <div
                    className="h-3 md:h-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-500"
                    style={{ width: `${(total.score / total.max) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* 解释和建议 */}
          <div className="border-t border-gray-200 pt-4 md:pt-6">
            <div className="p-4 md:p-5 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-yellow-800 mb-2">
                    {t('adultResults.interpretation')}
                  </h4>
                  <p className="text-sm md:text-base text-yellow-700 leading-relaxed">
                    {t('adultResults.disclaimer')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染儿童 ADHD 测试结果
  const renderChildResults = (scores: any) => {
    const { total = {}, inattention = {}, hyperactivity = {}, opposition = {}, respondent } = scores || {};

    return (
      <div className="space-y-4 md:space-y-6">
        {/* 测试结果卡片 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
          <div className="flex items-center mb-4 md:mb-6">
            <div className="w-12 h-12 rounded-lg bg-teal-100 flex items-center justify-center mr-3 md:mr-4">
              <Users className="h-6 w-6 text-teal-600" />
            </div>
            <div>
              <h2 className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900 font-heading">
                {t('childResults.title')}
              </h2>
              <p className="text-sm md:text-base text-gray-600">
                SNAP-IV Assessment Results
              </p>
            </div>
          </div>

          {/* 评估者信息 */}
          <div className="mb-6 md:mb-8">
            <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-4 text-gray-900">
              {t('childResults.summary')}
            </h3>

            <div className="p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4 md:mb-6">
              <p className="text-sm md:text-base text-blue-800 leading-relaxed">
                {respondent ?
                  t('childResults.respondentInfo', { respondent: t(`childTest.respondent.${respondent}`) }) :
                  t('childResults.respondentInfo', { respondent: t('childTest.respondent.parent') })
                }
              </p>
            </div>

            {/* 分数详情 - 响应式网格布局 */}
            <div className="space-y-4 md:space-y-6">
              {/* 注意力不集中结果 */}
              <div className="p-4 md:p-5 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
                  <div className="flex items-center">
                    <h4 className="font-medium text-sm md:text-base text-gray-900">
                      {t('childResults.categories.inattention')}
                    </h4>
                    {inattention.significant && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {t('childResults.significant')}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-sm md:text-base">
                    <span className="font-bold text-gray-900">
                      {safeRender(inattention.score)}/{safeRender(inattention.max)}
                    </span>
                    <span className="text-gray-500 ml-2">
                      (Mean: {(inattention.mean || 0).toFixed(2)})
                    </span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 md:h-3 overflow-hidden">
                  <div
                    className={`h-2.5 md:h-3 rounded-full transition-all duration-500 ${inattention.significant ? 'bg-red-500' : 'bg-teal-600'
                      }`}
                    style={{ width: `${((inattention.score || 0) / (inattention.max || 1)) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* 多动冲动结果 */}
              <div className="p-4 md:p-5 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
                  <div className="flex items-center">
                    <h4 className="font-medium text-sm md:text-base text-gray-900">
                      {t('childResults.categories.hyperactivity')}
                    </h4>
                    {hyperactivity.significant && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {t('childResults.significant')}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-sm md:text-base">
                    <span className="font-bold text-gray-900">
                      {safeRender(hyperactivity.score)}/{safeRender(hyperactivity.max)}
                    </span>
                    <span className="text-gray-500 ml-2">
                      (Mean: {(hyperactivity.mean || 0).toFixed(2)})
                    </span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 md:h-3 overflow-hidden">
                  <div
                    className={`h-2.5 md:h-3 rounded-full transition-all duration-500 ${hyperactivity.significant ? 'bg-red-500' : 'bg-teal-600'
                      }`}
                    style={{ width: `${((hyperactivity.score || 0) / (hyperactivity.max || 1)) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* 对立违抗结果 */}
              <div className="p-4 md:p-5 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
                  <div className="flex items-center">
                    <h4 className="font-medium text-sm md:text-base text-gray-900">
                      {t('childResults.categories.opposition')}
                    </h4>
                    {opposition.significant && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {t('childResults.significant')}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-sm md:text-base">
                    <span className="font-bold text-gray-900">
                      {opposition.score || 0}/{opposition.max || 0}
                    </span>
                    <span className="text-gray-500 ml-2">
                      (Mean: {(opposition.mean || 0).toFixed(2)})
                    </span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 md:h-3 overflow-hidden">
                  <div
                    className={`h-2.5 md:h-3 rounded-full transition-all duration-500 ${opposition.significant ? 'bg-red-500' : 'bg-teal-600'
                      }`}
                    style={{ width: `${((opposition.score || 0) / (opposition.max || 1)) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* 总分 */}
              <div className="p-4 md:p-6 bg-gradient-to-r from-teal-50 to-blue-50 rounded-lg border border-teal-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-3">
                  <h4 className="font-semibold text-base md:text-lg text-gray-900 flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2 text-teal-600" />
                    {t('childResults.categories.total')}
                  </h4>
                  <span className="text-lg md:text-xl font-bold text-teal-700">
                    {total.score || 0}/{total.max || 0}
                  </span>
                </div>
                <div className="w-full bg-teal-200 rounded-full h-3 md:h-4 overflow-hidden">
                  <div
                    className="h-3 md:h-4 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full transition-all duration-500"
                    style={{ width: `${((total.score || 0) / (total.max || 1)) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* 解释和建议 */}
          <div className="border-t border-gray-200 pt-4 md:pt-6 mt-6 md:mt-8">
            <div className="p-4 md:p-5 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-yellow-800 mb-2">
                    Important Information
                  </h4>
                  <p className="text-sm md:text-base text-yellow-700 leading-relaxed mb-2">
                    {t('childResults.disclaimers.professional')}
                  </p>
                  <p className="text-xs md:text-sm text-yellow-600">
                    {t('childResults.disclaimers.resources')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染通用 ADHD 测试结果
  const renderGeneralResults = (scores: any) => {
    const { total = 0, maxScore = 0, percentage = 0, interpretation = '' } = scores || {};

    return (
      <div className="space-y-4 md:space-y-6">
        {/* 测试结果卡片 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6 lg:p-8">
          <div className="flex items-center mb-4 md:mb-6">
            <div className="w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center mr-3 md:mr-4">
              <Brain className="h-6 w-6 text-gray-600" />
            </div>
            <div>
              <h2 className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900 font-heading">
                {t('results.title')}
              </h2>
              <p className="text-sm md:text-base text-gray-600">
                General ADHD Screening Results
              </p>
            </div>
          </div>

          {/* 分数详情 */}
          <div className="mb-6 md:mb-8">
            <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-4 text-gray-900">
              {t('results.summary')}
            </h3>

            {/* 总分 */}
            <div className="p-4 md:p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-3">
                <h4 className="font-semibold text-base md:text-lg text-gray-900 flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-gray-600" />
                  {t('results.totalScore')}
                </h4>
                <div className="flex items-center gap-2">
                  <span className="text-lg md:text-xl font-bold text-gray-700">
                    {total || 0}/{maxScore || 0}
                  </span>
                  <span className="text-sm text-gray-500">
                    ({Math.round(percentage || 0)}%)
                  </span>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 md:h-4 overflow-hidden">
                <div
                  className={`h-3 md:h-4 rounded-full transition-all duration-500 ${(percentage || 0) >= 70 ? 'bg-red-500' :
                    (percentage || 0) >= 40 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                  style={{ width: `${percentage || 0}%` }}
                ></div>
              </div>
            </div>

            {/* 解释部分 */}
            <div className="mt-4 md:mt-6 p-4 md:p-5 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2 flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                {t('results.interpretation')}
              </h4>
              <p className="text-sm md:text-base text-blue-700 leading-relaxed">
                {interpretation || 'No interpretation available'}
              </p>
            </div>
          </div>

          {/* 免责声明 */}
          <div className="border-t border-gray-200 pt-4 md:pt-6">
            <div className="p-4 md:p-5 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-yellow-800 mb-2">
                    Important Notice
                  </h4>
                  <p className="text-xs md:text-sm text-yellow-600">
                    {t('results.disclaimer')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4 md:space-y-6">
      {/* 测试结果 */}
      {renderTestResults()}

      {/* 详细评估报告部分 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-6">
        <div className="mb-4 md:mb-6">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg md:text-xl lg:text-2xl font-bold mb-2 text-gray-900 font-heading">
              {t((testResult.testType as unknown as string) === 'child'
                ? 'childResults.aiReport'
                : (testResult.testType as unknown as string) === 'adult'
                  ? 'adultResults.aiReport'
                  : 'results.aiReport'
              )}
            </h2>
            <p className="text-sm md:text-base text-gray-600 leading-relaxed">
              {t('aiReport.disclaimer')}
            </p>
          </div>
        </div>

        {/* AI 报告内容 */}
        {loaded && (
          <AdhdReport
            key={`adhd-report-${regenerateKey}`}
            testType={testResult.testType as 'adult' | 'child'}
            testId={testResult.id}
            answers={testResult.answers}
            scores={testResult.scores}
            locale={locale}
            email={session?.user?.email || undefined}
            userId={session?.user?.id}
            regenerate={shouldRegenerate}
            onGenerationStateChange={handleGenerationStateChange}
          />
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
        <button
          onClick={() => router.push(`/${locale === 'en' ? '' : locale + '/'}adhd`)}
          className="flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg text-sm md:text-base transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          {t('buttons.retake')}
        </button>
        <button
          onClick={() => router.push(`/${locale === 'en' ? '' : locale + '/'}reports`)}
          className="flex items-center justify-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg text-sm md:text-base transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
        >
          <BarChart3 className="h-4 w-4 mr-2" />
          {t('result.viewAllReports')}
        </button>
      </div>
    </div>
  );
} 