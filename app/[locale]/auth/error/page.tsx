import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  const t = await getTranslations('pages.auth');
  
  return {
    title: t('error.title'),
    description: t('error.description'),
  };
}

export default async function AuthErrorPage({
  params,
  searchParams,
}: {
  params: Promise<{ locale: string }>;
  searchParams?: Promise<{ error?: string }>;
}) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const locale = resolvedParams.locale;
  const t = await getTranslations('pages.auth');
  
  // 解析错误类型
  const errorType = resolvedSearchParams?.error || 'default';
  
  // 获取基于错误类型的错误消息
  const errorMessage = t(`error.messages.${errorType}`, {
    defaultValue: t('error.messages.default')
  });
  
  return (
    <main className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50 pt-20 md:pt-24">
      <div className="w-full max-w-md">
        <div className="text-center">
          <h1 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
            {t('error.heading')}
          </h1>
          <div className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200">
            <p className="text-red-700">{errorMessage}</p>
          </div>
          
          <div className="mt-6">
            <Link
              href={`/${locale === 'en' ? '' : locale}/auth/signin`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t('error.backToSignIn')}
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
} 