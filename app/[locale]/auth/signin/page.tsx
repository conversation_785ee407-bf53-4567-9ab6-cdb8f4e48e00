import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/utils/auth';
import { redirect } from 'next/navigation';
import SignInForm from '@/components/SignInForm';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  const t = await getTranslations('pages.auth');
  
  return {
    title: t('signin.title'),
    description: t('signin.description'),
  };
}

export default async function SignInPage({
  params,
  searchParams,
}: {
  params: Promise<{ locale: string }>;
  searchParams?: Promise<{ callbackUrl?: string; error?: string; testId?: string; token?: string }>;
}) {
  const t = await getTranslations('pages.auth');
  const resolvedParams = await params;
  const resolvedSearchParams = searchParams ? await searchParams : {};
  const session = await getServerSession(authOptions);
  
  // 如果用户已经登录，重定向到适当的页面
  if (session?.user) {
    // 如果有回调URL，重定向到回调URL
    if (resolvedSearchParams.callbackUrl) {
      redirect(resolvedSearchParams.callbackUrl);
    }
    
    // 如果有测试ID和令牌，重定向到报告页面
    if (resolvedSearchParams.testId && resolvedSearchParams.token) {
      const locale = resolvedParams.locale;
      redirect(`/${locale === 'en' ? '' : locale + '/'}report/${resolvedSearchParams.testId}?token=${resolvedSearchParams.token}`);
    }
    
    // 否则重定向到个人资料页面（这是登录用户最可能需要的页面）
    const locale = resolvedParams.locale;
    redirect(`/${locale === 'en' ? '' : locale + '/'}profile`);
  }
  
  return (
    <main className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50 pt-20 md:pt-24">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
            {t('signin.heading')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            {t('signin.subheading')}
          </p>
        </div>
        
        <SignInForm 
          locale={resolvedParams.locale} 
          callbackUrl={resolvedSearchParams.callbackUrl}
          error={resolvedSearchParams.error}
          testId={resolvedSearchParams.testId}
          token={resolvedSearchParams.token}
        />
      </div>
    </main>
  );
} 