{"name": "raads_plus", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_ENV=development NODE_OPTIONS='--no-deprecation' next dev", "build": "NODE_ENV=production next build", "start": "NODE_ENV=production next start", "lint": "next lint"}, "dependencies": {"@google-cloud/vertexai": "^1.9.3", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.17.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@types/stripe": "^8.0.416", "axios": "^1.7.9", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "framer-motion": "^11.15.0", "lucide-react": "^0.483.0", "nanoid": "^5.1.5", "next": "^15.1.2", "next-auth": "^4.24.11", "next-intl": "^3.25.0", "next-share": "^0.27.0", "node-fetch": "^3.3.2", "react": "^18", "react-dom": "^18", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "resend": "^4.5.1", "stripe": "^17.5.0", "tailwind-merge": "^2.5.4", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "prisma": "^5.17.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}