generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String    @id @default(uuid())
  email              String    @unique
  name               String?
  googleId           String?   @unique
  image              String?
  emailVerified      DateTime?
  stripeCustomerId   String?
  emailNotifications Boolean   @default(true)

  // Subscription fields
  subscriptionType   String? // 'single', 'pro', 'premium'
  subscriptionStatus String? // 'active', 'canceled', 'past_due', 'incomplete'
  subscriptionId     String? // Stripe subscription ID
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?

  // Usage tracking for subscription limits
  monthlyReportsUsed       Int       @default(0)
  monthlyConsultationsUsed Int       @default(0)
  lastResetDate            DateTime? // Track when monthly usage was last reset

  // Premium features
  hasHistoryAccess Boolean @default(false)
  hasVipFeatures   Boolean @default(false)

  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  testResults      TestResult[]
  accounts         Account[]
  sessions         Session[]
  ChatConversation ChatConversation[]
}

model Account {
  id                String   @id @default(uuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?  @db.Text
  access_token      String?  @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?  @db.Text
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(uuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model TestResult {
  id             String  @id @default(uuid())
  testType       String // "raadsrTest", "aq10Test" 等
  email          String
  answers        Json // 存储回答的 JSON 对象
  scores         Json // 存储分数的 JSON 对象
  prescreening   Json? // 预筛选选择的答案
  metadata       Json? // 额外信息（语言、设备类型等）
  reports        Json? // 存储不同语言的 AI 生成报告 {en: "...", zh: "..."}
  isReportLocked Boolean @default(true) // 报告是否锁定，默认为锁定状态

  // 新增：单独报告解锁跟踪
  unlockedByPayment Boolean @default(false) // 是否通过一次性付费解锁
  paymentIntentId   String? // Stripe PaymentIntent ID，用于跟踪具体的付费解锁

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 新增字段
  userId           String?
  user             User?             @relation(fields: [userId], references: [id])
  isUserBound      Boolean           @default(false) // 是否已绑定用户
  accessToken      String? // 用于未登录访问的唯一令牌
  ChatConversation ChatConversation?

  @@index([email])
  @@index([testType])
  @@index([accessToken])
  @@index([userId])
  @@index([paymentIntentId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// AI咨询聊天记录模型
model ChatConversation {
  id           String  @id @default(uuid())
  testResultId String // 关联的测试结果ID
  userId       String? // 用户ID（可能为空，支持未登录用户）
  messages     Json // 存储聊天消息数组 [{role: 'user'|'assistant', content: string, timestamp: DateTime}]

  // 使用统计
  messageCount     Int @default(0) // 总消息数量
  userMessageCount Int @default(0) // 用户消息数量（用于统计使用次数）

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  testResult TestResult @relation(fields: [testResultId], references: [id], onDelete: Cascade)
  user       User?      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([testResultId]) // 每个测试结果只能有一个聊天记录
  @@index([testResultId])
  @@index([userId])
}
