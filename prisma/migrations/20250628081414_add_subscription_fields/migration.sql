-- AlterTable
ALTER TABLE "User" ADD COLUMN     "currentPeriodEnd" TIMESTAMP(3),
ADD COLUMN     "currentPeriodStart" TIMESTAMP(3),
ADD COLUMN     "hasHistoryAccess" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "hasVipFeatures" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "lastResetDate" TIMESTAMP(3),
ADD COLUMN     "monthlyConsultationsUsed" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "monthlyReportsUsed" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "subscriptionId" TEXT,
ADD COLUMN     "subscriptionStatus" TEXT,
ADD COLUMN     "subscriptionType" TEXT;
