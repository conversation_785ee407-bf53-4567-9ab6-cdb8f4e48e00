-- CreateTable
CREATE TABLE "TestResult" (
    "id" TEXT NOT NULL,
    "testType" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "answers" JSONB NOT NULL,
    "scores" JSONB NOT NULL,
    "prescreening" J<PERSON>N<PERSON>,
    "metadata" JSONB,
    "reports" JSONB,
    "isReportLocked" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TestResult_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TestResult_email_idx" ON "TestResult"("email");

-- CreateIndex
CREATE INDEX "TestResult_testType_idx" ON "TestResult"("testType");
