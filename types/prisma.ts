// 测试结果类型
export interface TestResult {
  id: string;
  testType: 'raadsrTest' | 'aq10Test' | 'adult' | 'child' | 'general';
  email: string;
  answers: Record<number, number> | any;
  scores: {
    language?: { score: number; max: number; significant: boolean };
    social?: { score: number; max: number; significant: boolean };
    sensoryMotor?: { score: number; max: number; significant: boolean };
    interests?: { score: number; max: number; significant: boolean };
    total: { score: number; max: number; significant: boolean };
  } | any;
  // AQ-10 测试结果可能将得分存储在 score 属性中
  score?: {
    score: number;
    max: number;
    significant: boolean;
  };
  prescreening?: any;
  metadata?: any;
  reports?: any;
  isReportLocked: boolean;
  createdAt: Date;
  updatedAt: Date;
} 