// Type declarations for component directories
declare module '@/components/shared' {
  export const LanguageSelector: React.ComponentType;
}

declare module '@/components/layout' {
  export const Header: React.ComponentType;
  export const Footer: React.ComponentType;
  export const ClientLayout: React.ComponentType<{
    children: React.ReactNode;
    header: React.ReactNode;
    footer: React.ReactNode;
  }>;
}

declare module '@/components/features/adhd/tests' {
  export const AdhdTest: React.ComponentType;
  export const AdhdChildTest: React.ComponentType;
  export const AdhdAdultTest: React.ComponentType;
  export const AdhdPreScreening: React.ComponentType;
}

declare module '@/app/components' {
  export const AdhdReport: React.ComponentType<{
    testType: 'adult' | 'child';
    answers: Record<number, number>;
    scores: any;
    locale: string;
    onReset: () => void;
  }>;
}

declare module '@/components/core' {
  export const Button: React.ComponentType<{
    children: React.ReactNode;
    variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
    size?: 'sm' | 'md' | 'lg';
    fullWidth?: boolean;
    isLoading?: boolean;
    [key: string]: any;
  }>;
  
  export const Input: React.ComponentType<{
    label?: string;
    helperText?: string;
    error?: string;
    fullWidth?: boolean;
    [key: string]: any;
  }>;
} 