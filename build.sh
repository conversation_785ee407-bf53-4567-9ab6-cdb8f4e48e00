#!/bin/bash

# 设置错误处理
set -e

# 获取脚本所在目录的绝对路径（也是项目根目录）
PROJECT_ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 从 package.json 获取项目名称
if [ ! -f "package.json" ]; then
    echo "错误：找不到 package.json 文件"
    exit 1
fi

PROJECT_NAME=$(grep -m 1 '"name":' package.json | cut -d '"' -f 4)
if [ -z "$PROJECT_NAME" ]; then
    echo "错误：无法从 package.json 获取项目名称"
    exit 1
fi

# 获取 Docker Hub 用户名（默认为 blowxian）
DOCKER_USER=${DOCKER_USER:-blowxian}

# 检查 Docker 登录状态
if ! docker info > /dev/null 2>&1; then
    echo "错误：Docker 未运行"
    echo "请确保 Docker 已启动"
    exit 1
fi

# 检查 Docker Hub 登录状态
if ! docker buildx ls > /dev/null 2>&1; then
    echo "错误：Docker buildx 不可用"
    echo "请确保 Docker 已更新到支持 buildx 的版本"
    exit 1
fi

# 检查是否登录到 Docker Hub
# 首先检查凭证存储，如果失败再尝试测试推送权限
login_status=false

# 检查凭证存储中是否有Docker Hub的登录信息
if command -v docker-credential-desktop > /dev/null 2>&1 && \
   docker-credential-desktop list 2>/dev/null | grep -q "https://index.docker.io/v1/"; then
    login_status=true
fi

# 如果凭证检查失败，则尝试一个小型测试以验证是否有推送权限
if [ "$login_status" = false ]; then
    echo "正在检查 Docker Hub 登录状态..."
    if docker buildx build --platform linux/amd64 -t ${DOCKER_USER}/login-test --push -f - . <<EOF 2>/dev/null
FROM busybox:1.36
CMD ["echo", "login test"]
EOF
    then
        login_status=true
        # 清理测试镜像
        docker buildx imagetools rm ${DOCKER_USER}/login-test &>/dev/null || true
    fi
fi

# 如果未登录，询问用户是否仅构建不推送
if [ "$login_status" = false ]; then
    echo "警告：未检测到 Docker Hub 登录"
    echo "是否继续构建但不推送镜像？[y/N]"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        # 仅构建不推送
        echo "正在构建 Docker 镜像 ${DOCKER_USER}/${PROJECT_NAME}:latest..."
        if docker build --build-arg NODE_ENV=production -t ${DOCKER_USER}/${PROJECT_NAME}:latest .; then
            echo "Docker 镜像构建成功。"
            echo "要推送镜像，请先登录 Docker Hub:"
            echo "docker login"
            exit 0
        else
            echo "Docker 镜像构建失败！"
            exit 1
        fi
    else
        echo "请先登录 Docker Hub:"
        echo "docker login"
        exit 1
    fi
fi

# 构建并推送Docker镜像
echo "正在构建并推送 Docker 镜像 ${DOCKER_USER}/${PROJECT_NAME}:latest..."

# 添加构建日期和版本标签
BUILD_DATE=$(date +"%Y%m%d")
VERSION=$(grep -m 1 '"version":' package.json | cut -d '"' -f 4 || echo "1.0.0")
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

echo "版本信息:"
echo "- 版本号: $VERSION"
echo "- 构建日期: $BUILD_DATE"
echo "- Git 提交: $GIT_COMMIT"

# 构建配置
BUILD_ARGS=()
BUILD_ARGS+=("--platform" "linux/amd64")
BUILD_ARGS+=("--build-arg" "NODE_ENV=production")
BUILD_ARGS+=("--build-arg" "BUILD_DATE=$BUILD_DATE")
BUILD_ARGS+=("--build-arg" "VERSION=$VERSION")
BUILD_ARGS+=("--build-arg" "GIT_COMMIT=$GIT_COMMIT")

# 从 .env.production 读取 NEXT_PUBLIC 变量（如果文件存在）
if [ -f ".env.production" ]; then
    echo "读取 .env.production 中的 NEXT_PUBLIC 变量..."
    
    # 提取所有 NEXT_PUBLIC_ 开头的环境变量
    while IFS='=' read -r key value || [ -n "$key" ]; do
        # 跳过注释行和空行
        [[ $key == \#* ]] && continue
        [ -z "$key" ] && continue
        
        # 如果是 NEXT_PUBLIC_ 开头的变量，添加到构建参数中
        if [[ $key == NEXT_PUBLIC_* ]]; then
            # 移除可能的注释部分
            value=$(echo "$value" | cut -d'#' -f1 | xargs)
            # 处理引号
            value=$(echo "$value" | sed -e 's/^"//' -e 's/"$//' -e "s/^'//" -e "s/'$//")
            # 添加到构建参数数组
            BUILD_ARGS+=("--build-arg" "$key=$value")
            echo "添加构建参数: $key=$value"
        fi
    done < .env.production
    
    echo "构建环境变量准备完成"
else
    echo "警告: .env.production 文件不存在，使用以下默认环境变量:"
    
    # 设置飞书 Webhook 默认值
    FEISHU_NOTIFY_URL="https://open.feishu.cn/open-apis/bot/v2/hook/4cfea443-da77-40cb-b677-bed2f5fcecd6"
    FEISHU_KEY_URL="https://open.feishu.cn/open-apis/bot/v2/hook/4cfea443-da77-40cb-b677-bed2f5fcecd6"
    
    # 设置基本值
    BUILD_ARGS+=("--build-arg" "NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL=$FEISHU_NOTIFY_URL")
    BUILD_ARGS+=("--build-arg" "NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL=$FEISHU_KEY_URL")
    BUILD_ARGS+=("--build-arg" "NEXT_PUBLIC_APP_NAME=RAADS Autism Test")
    BUILD_ARGS+=("--build-arg" "NEXT_PUBLIC_APP_DESCRIPTION=Scientifically validated autism assessment tools")
    BUILD_ARGS+=("--build-arg" "NEXT_PUBLIC_PROJECT_IDENTIFIER=raads_plus")
    BUILD_ARGS+=("--build-arg" "NEXT_PUBLIC_API_URL=https://api.tmpl-lite.com")
    BUILD_ARGS+=("--build-arg" "NEXT_PUBLIC_SITE_URL=https://autism.raadstest.com")
    
    echo "使用默认环境变量设置构建参数"
fi

# 添加标签
BUILD_ARGS+=("-t" "${DOCKER_USER}/${PROJECT_NAME}:latest")
BUILD_ARGS+=("-t" "${DOCKER_USER}/${PROJECT_NAME}:${VERSION}")
BUILD_ARGS+=("-t" "${DOCKER_USER}/${PROJECT_NAME}:${BUILD_DATE}")
BUILD_ARGS+=("--push" ".")

# 输出调试信息
echo "========================="
echo "DEBUG: 完整的构建命令参数"
echo "========================="
echo "docker buildx build ${BUILD_ARGS[@]}"
echo "========================="

# 构建多个标签
if docker buildx build "${BUILD_ARGS[@]}"; then
    echo "Docker 镜像构建并推送成功。"
    echo "已推送的标签:"
    echo "- ${DOCKER_USER}/${PROJECT_NAME}:latest"
    echo "- ${DOCKER_USER}/${PROJECT_NAME}:${VERSION}"
    echo "- ${DOCKER_USER}/${PROJECT_NAME}:${BUILD_DATE}"
else
    echo "Docker 镜像构建或推送失败！"
    exit 1
fi

echo "所有操作成功完成。"
echo "要部署此镜像，请运行:"
echo "./deploy.sh" 