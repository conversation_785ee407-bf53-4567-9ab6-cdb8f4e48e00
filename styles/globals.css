/* 支付相关的移动端优化 */
@media (max-width: 640px) {

    /* 支付弹层移动端优化 */
    [class*="payment"] {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    /* 确保支付弹层在移动端有足够的空间 */
    .fixed[role="dialog"],
    .fixed[class*="payment"] {
        padding: 12px !important;
    }

    /* 支付按钮移动端触摸优化 */
    button[type="submit"] {
        min-height: 48px !important;
        font-size: 16px !important;
        /* 防止 iOS 自动缩放 */
        -webkit-appearance: none;
        appearance: none;
        user-select: none;
        touch-action: manipulation;
        border-radius: 12px !important;
    }

    /* 输入框移动端优化 */
    input,
    select,
    .StripeElement {
        font-size: 16px !important;
        /* 防止 iOS 自动缩放 */
        -webkit-appearance: none;
        appearance: none;
        min-height: 44px !important;
    }

    /* Stripe Elements 移动端优化 */
    .StripeElement {
        padding: 14px 16px !important;
        border-radius: 8px !important;
    }

    /* 支付弹层内容区域优化 */
    .space-y-6>*+* {
        margin-top: 1rem !important;
    }

    .space-y-4>*+* {
        margin-top: 0.75rem !important;
    }

    /* 移动端错误信息优化 */
    .bg-red-50 {
        padding: 12px !important;
        border-radius: 8px !important;
    }

    /* 移动端金额显示优化 */
    .bg-gray-50 {
        padding: 16px !important;
        border-radius: 8px !important;
    }

    /* 移动端标题字体调整 */
    .text-lg {
        font-size: 1rem !important;
    }

    .text-xl {
        font-size: 1.125rem !important;
    }
}

/* iOS 特定优化 */
@supports (-webkit-touch-callout: none) {

    /* iOS Safari 优化 */
    button,
    input,
    select,
    .StripeElement {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    /* 防止 iOS 缩放 */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    select,
    .StripeElement {
        font-size: 16px !important;
        transform: translateZ(0);
        /* 硬件加速 */
    }

    /* iOS 支付按钮优化 */
    button[type="submit"] {
        -webkit-appearance: none;
        border-radius: 12px !important;
        will-change: transform;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }

    /* iOS 滚动优化 */
    .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .StripeElement {
        border-width: 2px !important;
        border-color: #000 !important;
    }

    .StripeElement:focus {
        border-color: #10b981 !important;
        box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3) !important;
    }

    .bg-gradient-to-r {
        background: #059669 !important;
        background-image: none !important;
    }

    .border {
        border-width: 2px !important;
        border-color: #000 !important;
    }
}

/* 减少动画偏好用户优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .animate-spin {
        animation: none !important;
    }

    .transform {
        transform: none !important;
    }

    .hover\:scale-\[1\.01\]:hover {
        transform: none !important;
    }
}

/* 暗色模式下的支付组件优化 */
@media (prefers-color-scheme: dark) {
    .StripeElement {
        background-color: #374151 !important;
        border-color: #4b5563 !important;
        color: #f9fafb !important;
    }

    .StripeElement:focus {
        border-color: #10b981 !important;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2) !important;
    }

    .StripeElement--invalid {
        border-color: #ef4444 !important;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
    }

    /* 暗色模式下的 Stripe Elements 样式调整 */
    .StripeElement iframe {
        color-scheme: dark;
    }
}

/* 小屏幕设备（iPhone SE 等）特殊优化 */
@media (max-width: 375px) {

    /* 支付弹层在小屏幕上的适配 */
    .max-w-md {
        max-width: calc(100vw - 24px) !important;
    }

    /* 更紧凑的间距 */
    .space-y-6>*+* {
        margin-top: 0.75rem !important;
    }

    .space-y-4>*+* {
        margin-top: 0.5rem !important;
    }

    /* 调整字体大小 */
    .text-base {
        font-size: 0.875rem !important;
    }

    .text-sm {
        font-size: 0.75rem !important;
    }

    /* 支付按钮小屏优化 */
    button[type="submit"] {
        padding: 12px 16px !important;
        font-size: 14px !important;
        min-height: 44px !important;
    }

    /* Stripe Elements 小屏优化 */
    .StripeElement {
        padding: 12px 14px !important;
        font-size: 15px !important;
    }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {

    /* 横屏时支付弹层高度优化 */
    .max-h-\[95vh\] {
        max-height: 98vh !important;
    }

    .max-h-\[90vh\] {
        max-height: 95vh !important;
    }

    /* 减少垂直间距 */
    .space-y-6>*+* {
        margin-top: 0.5rem !important;
    }

    .space-y-4>*+* {
        margin-top: 0.375rem !important;
    }

    .py-8 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }

    .py-6 {
        padding-top: 0.75rem !important;
        padding-bottom: 0.75rem !important;
    }

    /* 横屏时的支付按钮 */
    button[type="submit"] {
        padding: 10px 16px !important;
        min-height: 40px !important;
    }
}

/* 键盘导航优化 */
.StripeElement:focus-visible {
    outline: 2px solid #10b981 !important;
    outline-offset: 2px !important;
}

/* 触摸设备通用优化 */
@media (hover: none) and (pointer: coarse) {

    /* 触摸设备上的元素优化 */
    button,
    .StripeElement,
    input,
    select {
        min-height: 44px !important;
        padding: 12px 16px !important;
    }

    /* 触摸友好的间距 */
    .space-y-6>*+* {
        margin-top: 1.25rem !important;
    }

    /* 触摸设备上的悬停效果移除 */
    .hover\:shadow-xl:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }
}

/* 加载状态优化 */
.payment-element-container:has(.StripeElement--loading) {
    opacity: 0.7;
    pointer-events: none;
}

/* 动画优化 */
.payment-element-container .StripeElement {
    transition: all 0.2s ease-in-out;
}

.payment-element-container .StripeElement:not(.StripeElement--loading) {
    animation: fadeInPayment 0.3s ease-out;
}

@keyframes fadeInPayment {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 国家选择器和邮编字段的特殊处理 */
.StripeElement [data-elements-stable-field-name="countryCode"],
.StripeElement [data-elements-stable-field-name="postalCode"] {
    width: 100% !important;
}

/* 支付方式标签页优化 */
.StripeElement [role="tablist"] {
    margin-bottom: 16px !important;
}

.StripeElement [role="tab"] {
    min-height: 44px !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
}

/* 错误信息样式优化 */
.StripeElement [role="alert"] {
    font-size: 14px !important;
    color: #ef4444 !important;
    margin-top: 8px !important;
}

/* 成功状态样式 */
.StripeElement--complete {
    border-color: #10b981 !important;
}

/* 聚焦状态优化 */
.StripeElement:focus-within {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* ==============================================
   支付界面按钮优化 - 提升转化率设计
   ============================================== */

/* 支付按钮强化样式 */
.payment-primary-button {
    /* 基础样式 */
    position: relative;
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    color: white;
    font-weight: 700;
    font-size: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* 阴影和立体效果 */
    box-shadow:
        0 4px 6px -1px rgba(16, 185, 129, 0.2),
        0 2px 4px -1px rgba(16, 185, 129, 0.1),
        0 0 0 0 rgba(16, 185, 129, 0.4);

    /* 触感优化 */
    min-height: 56px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    /* 动画准备 */
    overflow: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.payment-primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent);
    transition: left 0.5s;
}

.payment-primary-button:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%);
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 10px 15px -3px rgba(16, 185, 129, 0.3),
        0 4px 6px -2px rgba(16, 185, 129, 0.2),
        0 0 0 3px rgba(16, 185, 129, 0.1);
}

.payment-primary-button:hover::before {
    left: 100%;
}

.payment-primary-button:active {
    transform: translateY(0) scale(0.98);
    transition: transform 0.1s;
}

.payment-primary-button:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 取消按钮弱化样式 */
.payment-secondary-button {
    background: transparent;
    color: #6b7280;
    font-weight: 400;
    font-size: 0.875rem;
    padding: 0.625rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 40px;

    /* 弱化视觉重要性 */
    opacity: 0.8;

    /* 减少触感面积 */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.payment-secondary-button:hover {
    background: #f9fafb;
    color: #374151;
    border-color: #d1d5db;
    opacity: 1;
    transform: none;
    /* 不添加放大效果 */
}

.payment-secondary-button:active {
    background: #f3f4f6;
    transform: scale(0.99);
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .payment-secondary-button {
        color: #9ca3af;
        border-color: #4b5563;
    }

    .payment-secondary-button:hover {
        background: rgba(55, 65, 81, 0.3);
        color: #d1d5db;
        border-color: #6b7280;
    }

    .payment-secondary-button:active {
        background: rgba(55, 65, 81, 0.5);
    }
}

/* 移动端支付按钮优化 */
@media (max-width: 640px) {
    .payment-primary-button {
        font-size: 1.125rem;
        padding: 1.25rem 1.5rem;
        min-height: 60px;
        border-radius: 1rem;

        /* 移动端震动反馈（支持的设备） */
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    .payment-secondary-button {
        min-height: 44px;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 375px) {
    .payment-primary-button {
        padding: 1.125rem 1rem;
        font-size: 1rem;
    }
}

/* 按钮布局容器优化 */
.payment-button-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.payment-button-container .payment-primary-button {
    order: 1;
    flex: 1;
}

.payment-button-container .payment-secondary-button {
    order: 2;
    flex: none;
}

/* 支付表单特殊样式 */
.payment-form-container {
    /* Stripe Elements 特殊优化 */
    --stripe-color-primary: #10b981;
    --stripe-color-background: #ffffff;
    --stripe-color-text: #374151;
    --stripe-border-radius: 8px;
    --stripe-font-size-base: 16px;
    --stripe-spacing-unit: 6px;
}

.payment-form-container .StripeElement {
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
    min-height: 44px;
}

.payment-form-container .StripeElement:focus,
.payment-form-container .StripeElement--focus {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    outline: none;
}

.payment-form-container .StripeElement--invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 安全标识优化 */
.payment-security-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.25rem 0.75rem;
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
}

.payment-security-badge svg {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}

/* 价值主张突出 */
.payment-value-highlight {
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    border: 1px solid #a7f3d0;
    border-radius: 0.75rem;
    padding: 1rem;
    position: relative;
}

.payment-value-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #10b981, #059669, #10b981);
    border-radius: 0.75rem 0.75rem 0 0;
}

/* 加载状态优化 */
.payment-loading-overlay {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    z-index: 10;
}

.payment-loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #10b981;
    border-radius: 50%;
    animation: payment-spin 1s linear infinite;
}

@keyframes payment-spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 成功状态动画 */
.payment-success-animation {
    animation: payment-success-pulse 2s ease-in-out;
}

@keyframes payment-success-pulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* 错误状态样式 */
.payment-error-container {
    background: rgba(254, 242, 242, 1);
    border: 1px solid rgba(252, 165, 165, 1);
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.payment-error-container .error-icon {
    color: #dc2626;
    flex-shrink: 0;
}

.payment-error-container .error-text {
    color: #b91c1c;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

/* 暗色模式支付界面优化 */
@media (prefers-color-scheme: dark) {
    .payment-form-container {
        --stripe-color-background: #1f2937;
        --stripe-color-text: #f9fafb;
    }

    .payment-value-highlight {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
        border-color: rgba(16, 185, 129, 0.3);
    }

    .payment-loading-overlay {
        background: rgba(31, 41, 55, 0.95);
    }

    .payment-error-container {
        background: rgba(127, 29, 29, 0.2);
        border-color: rgba(185, 28, 28, 0.3);
    }

    .payment-error-container .error-text {
        color: #fca5a5;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .payment-primary-button {
        border: 2px solid #000000;
        background: #000000;
    }

    .payment-secondary-button {
        border: 2px solid #666666;
        background: #ffffff;
        color: #000000;
    }

    .payment-security-badge {
        border: 2px solid #059669;
        background: #ffffff;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {

    .payment-primary-button,
    .payment-secondary-button,
    .payment-form-container .StripeElement {
        transition: none;
        animation: none;
        transform: none;
    }

    .payment-primary-button:hover {
        transform: none;
    }

    .payment-loading-spinner {
        animation: none;
        border-top-color: transparent;
    }

    .payment-success-animation {
        animation: none;
    }
}