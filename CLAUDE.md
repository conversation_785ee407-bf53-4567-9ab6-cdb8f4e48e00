# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

RAADS Plus is a Next.js-based psychological assessment platform that provides ADHD and autism screening tests with AI-powered consultation. The application features multi-language support, subscription-based access control, and Stripe payment integration.

## Development Commands

```bash
# Development
npm run dev                 # Start development server
npm install                 # Install dependencies

# Build and Production
npm run build              # Build for production
npm run start              # Start production server

# Linting and Code Quality
npm run lint               # Run ESLint

# Database Operations
npx prisma generate        # Generate Prisma client
npx prisma migrate dev     # Run database migrations
npx prisma studio          # Open database browser

# Docker Development
docker-compose up -d       # Start full development environment
docker-compose up -d raads_plus_db  # Start only database
```

## Architecture Overview

### Core Technologies
- **Next.js 15** with App Router and TypeScript
- **Prisma ORM** with PostgreSQL database
- **NextAuth.js** for authentication (Google OAuth, Email magic links)
- **Stripe** for payment processing and subscription management
- **Google Vertex AI** for AI consultation features
- **next-intl** for internationalization (EN/ZH)
- **Tailwind CSS** for styling

### API Architecture

**Main API Route Categories:**
- `/api/auth/` - Authentication via NextAuth.js
- `/api/autism/` & `/api/adhd/` - Test result management
- `/api/subscription/` - Subscription management
- `/api/chat-consultation/` - AI consultation with Vertex AI
- `/api/referral/` - Referral system
- `/api/stripe-webhook` - Payment webhooks

**Authentication Patterns:**
- Session-based auth for logged-in users
- Token-based access for anonymous test results
- Subscription-based access control for premium features

**Database Integration:**
- Prisma singleton pattern with environment-specific logging
- Transaction support for complex operations
- Connection pooling and proper disconnection handling

### Key Components

**Report Components:**
- `AdhdReport.tsx` - ADHD test report with AI generation
- `AutismReport.tsx` - Autism test report with AI generation
- `LockedReport.tsx` - Access control overlay for locked reports

**Core Services:**
- `SubscriptionService` - Subscription management and usage tracking
- `ChatService` - AI consultation access control
- `utils/prisma.ts` - Database singleton with logging configuration

### Database Schema

**Key Models:**
- `User` - User accounts with subscription info and usage tracking
- `TestResult` - Test results with metadata, scoring, and AI reports
- `ChatConversation` - AI chat history per test result
- `Account`/`Session` - NextAuth.js authentication tables

**Subscription System:**
- Three tiers: Free (limited), Pro (monthly quotas), Premium (unlimited)
- Usage tracking: `monthlyReportsUsed`, `monthlyConsultationsUsed`
- Automatic monthly reset based on `lastResetDate`

### Frontend Architecture

**Layout Structure:**
- `app/[locale]/layout.tsx` - Main layout with i18n support
- `app/[locale]/` - Internationalized pages
- `components/` - Reusable React components

**State Management:**
- React hooks for component state
- Context providers for global state (auth, subscriptions)
- Server components for data fetching

## Environment Configuration

The application uses environment-specific configuration:

**Required Environment Variables:**
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - NextAuth.js secret
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - OAuth credentials
- `STRIPE_SECRET_KEY` & `STRIPE_WEBHOOK_SECRET` - Payment processing
- `GOOGLE_APPLICATION_CREDENTIALS` - Path to Google Cloud service account JSON

**Development vs Production:**
- Use `.env` for local development
- Use `.env.production` for production deployment
- Docker configurations handle environment-specific settings

## Key Development Patterns

### Error Handling
- Structured API responses with `success`, `data`, and `error` fields
- Environment-specific error details
- Comprehensive logging with context

### Internationalization
- `next-intl` for multi-language support
- Locale-based routing with `[locale]` segments
- Translation files in `locales/` directory

### Payment Integration
- Stripe Payment Intents for single purchases
- Stripe Checkout Sessions for subscriptions
- Webhook handling for real-time payment updates

### AI Integration
- Google Vertex AI (Gemini) for consultation features
- Streaming responses for real-time chat
- Context-aware prompts based on test results
- Multi-language AI responses

## Common Development Tasks

### Adding New Test Types
1. Create new API routes in `/api/[testType]/`
2. Add test result component in `/app/components/`
3. Update database schema with new test type
4. Add translations for new test content

### Modifying Subscription Tiers
1. Update `pricingConfig.ts` with new pricing
2. Modify subscription logic in `SubscriptionService`
3. Update Stripe product configurations
4. Test webhook handling for new subscription types

### Database Schema Changes
1. Modify `prisma/schema.prisma`
2. Run `npx prisma migrate dev --name description`
3. Update TypeScript types if needed
4. Test migrations in development environment

## Testing and Debugging

### AI Consultation Debugging
- Use browser console for frontend logs
- Check server logs for backend processing
- Monitor `userMessageCount` and subscription limits
- Verify conversation persistence in database

### Payment Testing
- Use Stripe test mode for development
- Test webhook handling with ngrok or similar
- Verify subscription status updates
- Check payment intent completion

### Database Debugging
- Use `npx prisma studio` for visual database inspection
- Check connection strings and environment variables
- Monitor query performance and connection pooling
- Verify data consistency across related tables

## Docker Development

The project includes Docker configuration for development and production:

```bash
# Development with hot reload
docker-compose up -d

# Production testing
./test-prod.sh

# Build production image
./build.sh
```

## Performance Considerations

- Database query optimization with proper indexing
- Connection pooling for database connections
- Caching strategies (currently disabled in `reportCache`)
- Streaming responses for AI consultations
- Image optimization for user avatars and assets