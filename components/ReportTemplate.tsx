'use client';

import { Lock, Brain, Target, Users, BookOpen, Lightbulb } from 'lucide-react';

interface ReportTemplateProps {
    testType: 'raadsrTest' | 'aq10Test' | 'adhdAdultTest' | 'adhdChildTest';
    locale: string;
}

interface TemplateChapter {
    icon: React.ReactNode;
    title: string;
    description: string;
    wordCount: string;
    sampleContent: string[];
}

export default function ReportTemplate({ testType, locale }: ReportTemplateProps) {
    // 根据测试类型定义详细的模板章节
    const getTemplateChapters = (): TemplateChapter[] => {
        switch (testType) {
            case 'raadsrTest':
                return [
                    {
                        icon: <Brain className="w-5 h-5" />,
                        title: locale === 'zh' ? '结果摘要' : 'Summary of Results',
                        description: locale === 'zh' ? '概括性总结RAADS-R测试结果、总分意义及主要发现' : 'Comprehensive summary of RAADS-R test results, total score significance, and key findings',
                        wordCount: locale === 'zh' ? '150-200字' : '150-200 words',
                        sampleContent: locale === 'zh' ? [
                            '• 您的RAADS-R总分为XX/240分，处于XX范围，达到/未达到临床显著水平',
                            '• 整体评估结果显示在自闭症谱系特征方面的表现程度为XX',
                            '• 四个维度的详细分析：语言XX/65分，社交关系XX/39分',
                            '• 感知运动XX/43分，兴趣与规则性XX/93分',
                            '• 与年龄组平均水平对比：您的得分位于XX百分位',
                            '• 测试结果的可靠性评估：内部一致性良好，结果具有参考价值',
                            '• 重要提醒：此测试为筛查工具，不能替代专业诊断',
                            '• 建议结合临床评估和其他评估工具进行综合判断'
                        ] : [
                            '• Your RAADS-R total score is XX/240 points, falling in the XX range, reaching/not reaching clinical significance',
                            '• Overall assessment indicates XX level of performance in autism spectrum characteristics',
                            '• Detailed analysis across four dimensions: Language XX/65 points, Social Relations XX/39 points',
                            '• Sensory Motor XX/43 points, Circumscribed Interests XX/93 points',
                            '• Comparison with age-group average: Your score is at the XX percentile',
                            '• Assessment reliability: Good internal consistency, results have reference value',
                            '• Important note: This test is a screening tool and cannot replace professional diagnosis',
                            '• Recommend combining with clinical assessment and other evaluation tools'
                        ]
                    },
                    {
                        icon: <Target className="w-5 h-5" />,
                        title: locale === 'zh' ? '测试结果总览' : 'Test Results Overview',
                        description: locale === 'zh' ? '详细分析RAADS-R测试分数和各维度表现' : 'Detailed analysis of RAADS-R test scores and dimensional performance',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 总分解读：XX分表示您在自闭症特征方面的整体表现',
                            '• 语言维度(XX/65分)：字面理解、隐喻困难、语言使用模式分析',
                            '• 社交关系维度(XX/39分)：人际互动、情感理解、社交技能评估',
                            '• 感知运动维度(XX/43分)：感官敏感性、运动协调、刺激反应分析',
                            '• 兴趣规则性维度(XX/93分)：特殊兴趣、重复行为、规律性需求',
                            '• 临床意义：各维度得分的临床解释和意义说明',
                            '• 常模比较：与同龄群体的对比分析结果',
                            '• 得分模式：识别您的优势领域和挑战领域'
                        ] : [
                            '• Total score interpretation: XX points indicates your overall performance in autism characteristics',
                            '• Language dimension (XX/65): Literal understanding, metaphor difficulties, language use patterns',
                            '• Social Relations dimension (XX/39): Interpersonal interaction, emotional understanding, social skills',
                            '• Sensory Motor dimension (XX/43): Sensory sensitivity, motor coordination, stimulus response',
                            '• Circumscribed Interests dimension (XX/93): Special interests, repetitive behaviors, need for routine',
                            '• Clinical significance: Clinical interpretation and meaning of each dimension score',
                            '• Normative comparison: Comparative analysis with same-age groups',
                            '• Score patterns: Identifying your strength areas and challenge areas'
                        ]
                    },
                    {
                        icon: <Brain className="w-5 h-5" />,
                        title: locale === 'zh' ? '核心特征分析' : 'Core Characteristics Analysis',
                        description: locale === 'zh' ? '深入解读自闭症谱系相关的核心特征表现' : 'In-depth interpretation of core autism spectrum-related characteristics',
                        wordCount: locale === 'zh' ? '300-400字' : '300-400 words',
                        sampleContent: locale === 'zh' ? [
                            '• 语言沟通特点：字面理解倾向、隐喻和讽刺理解困难',
                            '• 非语言沟通：眼神接触模式、面部表情识别能力分析',
                            '• 社交互动模式：主动社交意愿、社交技能运用情况',
                            '• 情感理解能力：他人情绪识别、共情能力评估',
                            '• 感知觉敏感性：听觉、视觉、触觉、嗅觉的特殊反应模式',
                            '• 感官处理：感官信息整合和处理方式的个体差异',
                            '• 兴趣集中度：特殊兴趣的深度、广度和持续性',
                            '• 重复行为模式：刻板行为、仪式化行为的表现形式',
                            '• 变化适应能力：对环境变化、计划调整的反应和适应',
                            '• 认知灵活性：思维转换、问题解决策略的灵活程度',
                            '• 执行功能：计划制定、任务执行、注意力控制能力',
                            '• 情绪调节：压力反应、情绪表达和管理方式'
                        ] : [
                            '• Language communication patterns: Literal interpretation tendencies, metaphor and sarcasm comprehension difficulties',
                            '• Non-verbal communication: Eye contact patterns, facial expression recognition abilities',
                            '• Social interaction styles: Initiative in social situations, social skills application',
                            '• Emotional understanding: Recognition of others\' emotions, empathy assessment',
                            '• Sensory sensitivities: Special response patterns to auditory, visual, tactile, olfactory stimuli',
                            '• Sensory processing: Individual differences in sensory information integration and processing',
                            '• Interest focus: Depth, breadth, and persistence of special interests',
                            '• Repetitive behavior patterns: Manifestations of stereotyped and ritualistic behaviors',
                            '• Change adaptation: Responses and adaptation to environmental changes and plan adjustments',
                            '• Cognitive flexibility: Flexibility in thinking transitions and problem-solving strategies',
                            '• Executive function: Planning, task execution, attention control abilities',
                            '• Emotional regulation: Stress responses, emotional expression and management styles'
                        ]
                    },
                    {
                        icon: <BookOpen className="w-5 h-5" />,
                        title: locale === 'zh' ? '发展轨迹评估' : 'Developmental Trajectory Assessment',
                        description: locale === 'zh' ? '分析发展模式和潜在成长路径' : 'Analysis of developmental patterns and potential growth pathways',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 早期发展特征：童年期的社交、语言、行为发展模式回顾',
                            '• 青少年期变化：青春期社交需求增加时的适应情况',
                            '• 成年期表现：工作、学习、人际关系中的特征表现',
                            '• 适应性发展：个人在不同环境中的适应策略和能力',
                            '• 优势识别：个人特质中的积极方面和潜在优势',
                            '• 成长潜力：基于当前表现预测的发展可能性',
                            '• 支持需求：不同发展阶段可能需要的支持类型',
                            '• 长期规划：个人发展的长期目标和实现路径'
                        ] : [
                            '• Early developmental characteristics: Review of social, language, and behavioral development patterns in childhood',
                            '• Adolescent changes: Adaptation during increased social demands in adolescence',
                            '• Adult manifestations: Characteristic expressions in work, study, and interpersonal relationships',
                            '• Adaptive development: Personal adaptation strategies and abilities in different environments',
                            '• Strength identification: Positive aspects and potential advantages in personal traits',
                            '• Growth potential: Predicted developmental possibilities based on current performance',
                            '• Support needs: Types of support that may be needed at different developmental stages',
                            '• Long-term planning: Long-term goals and pathways for personal development'
                        ]
                    },
                    {
                        icon: <Users className="w-5 h-5" />,
                        title: locale === 'zh' ? '社交互动建议' : 'Social Interaction Recommendations',
                        description: locale === 'zh' ? '针对性的社交技能提升和人际关系建议' : 'Targeted social skills improvement and interpersonal relationship recommendations',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 社交技能训练：非语言沟通技巧的系统性练习方法',
                            '• 对话技巧：主动发起对话、维持话题、适时结束的策略',
                            '• 人际关系建立：寻找共同兴趣、建立友谊的实用技巧',
                            '• 社交场合应对：聚会、会议、工作场合的具体应对策略',
                            '• 情感表达：适当表达自己情感和理解他人情感的方法',
                            '• 冲突解决：处理误解、分歧和人际冲突的有效方式',
                            '• 边界设定：在人际关系中设定和维护个人边界',
                            '• 支持网络构建：寻找和维护理解性社群和支持系统'
                        ] : [
                            '• Social skills training: Systematic practice methods for non-verbal communication skills',
                            '• Conversation techniques: Strategies for initiating conversations, maintaining topics, and ending appropriately',
                            '• Relationship building: Practical skills for finding common interests and building friendships',
                            '• Social situation management: Specific coping strategies for parties, meetings, and workplace scenarios',
                            '• Emotional expression: Methods for appropriately expressing own emotions and understanding others\' emotions',
                            '• Conflict resolution: Effective ways to handle misunderstandings, disagreements, and interpersonal conflicts',
                            '• Boundary setting: Setting and maintaining personal boundaries in interpersonal relationships',
                            '• Support network building: Finding and maintaining understanding communities and support systems'
                        ]
                    },
                    {
                        icon: <Target className="w-5 h-5" />,
                        title: locale === 'zh' ? '生活适应策略' : 'Life Adaptation Strategies',
                        description: locale === 'zh' ? '实用的日常生活管理和适应技巧' : 'Practical daily life management and adaptation techniques',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 日常规律建立：创建和维护有助于稳定的日常作息',
                            '• 环境优化：调整生活和工作环境以减少感官过载',
                            '• 压力管理：识别压力源和开发个性化的应对策略',
                            '• 时间管理：制定现实可行的时间安排和任务规划',
                            '• 感官调节：管理感官敏感性的实用工具和技巧',
                            '• 变化适应：逐步适应变化和提高灵活性的方法',
                            '• 自我倡导：在不同环境中为自己争取需要的支持',
                            '• 健康维护：保持身心健康的综合性生活方式建议'
                        ] : [
                            '• Daily routine establishment: Creating and maintaining daily schedules that promote stability',
                            '• Environment optimization: Adjusting living and work environments to reduce sensory overload',
                            '• Stress management: Identifying stressors and developing personalized coping strategies',
                            '• Time management: Creating realistic and achievable time arrangements and task planning',
                            '• Sensory regulation: Practical tools and techniques for managing sensory sensitivities',
                            '• Change adaptation: Methods for gradually adapting to changes and improving flexibility',
                            '• Self-advocacy: Advocating for needed support in different environments',
                            '• Health maintenance: Comprehensive lifestyle recommendations for maintaining physical and mental health'
                        ]
                    },
                    {
                        icon: <Lightbulb className="w-5 h-5" />,
                        title: locale === 'zh' ? '个性化建议' : 'Personalized Recommendations',
                        description: locale === 'zh' ? '基于测试结果的专业建议和资源推荐' : 'Professional advice and resource recommendations based on test results',
                        wordCount: locale === 'zh' ? '150-200字' : '150-200 words',
                        sampleContent: locale === 'zh' ? [
                            '• 专业咨询建议：何时寻求专业心理健康服务',
                            '• 进一步评估：推荐的额外评估工具和专业测试',
                            '• 治疗选择：适合的心理治疗方法和干预策略',
                            '• 教育资源：相关书籍、在线课程和学习材料',
                            '• 社区支持：本地和在线的支持群体和组织',
                            '• 职业发展：适合个人特质的职业选择和发展建议',
                            '• 家庭支持：为家庭成员提供的理解和支持指导'
                        ] : [
                            '• Professional consultation advice: When to seek professional mental health services',
                            '• Further assessment: Recommended additional evaluation tools and professional tests',
                            '• Treatment options: Suitable psychotherapy methods and intervention strategies',
                            '• Educational resources: Relevant books, online courses, and learning materials',
                            '• Community support: Local and online support groups and organizations',
                            '• Career development: Career choices and development advice suited to personal traits',
                            '• Family support: Understanding and support guidance for family members'
                        ]
                    }
                ];

            case 'aq10Test':
                return [
                    {
                        icon: <Brain className="w-5 h-5" />,
                        title: locale === 'zh' ? '结果摘要' : 'Summary of Results',
                        description: locale === 'zh' ? '概括性总结AQ-10测试结果、得分意义及主要发现' : 'Comprehensive summary of AQ-10 test results, score significance, and key findings',
                        wordCount: locale === 'zh' ? '120-150字' : '120-150 words',
                        sampleContent: locale === 'zh' ? [
                            '• 您的AQ-10得分为X/10分，6分以上建议进一步专业评估',
                            '• 筛查结果显示您在自闭症特征方面的倾向程度为XX',
                            '• 与一般人群对比：您的得分处于XX百分位',
                            '• 测试的局限性：AQ-10仅为快速筛查工具，非诊断依据',
                            '• 结果解释：高分表示可能存在自闭症特征，需专业确认',
                            '• 重要提醒：筛查阳性不等于确诊，需结合临床评估'
                        ] : [
                            '• Your AQ-10 score is X/10 points, scores of 6+ suggest further professional evaluation',
                            '• Screening results indicate your tendency level in autism characteristics as XX',
                            '• Comparison with general population: your score is at the XX percentile',
                            '• Test limitations: AQ-10 is a rapid screening tool only, not diagnostic',
                            '• Result interpretation: High scores suggest possible autism traits, requiring professional confirmation',
                            '• Important note: Positive screening does not equal diagnosis, clinical assessment needed'
                        ]
                    },
                    {
                        icon: <Target className="w-5 h-5" />,
                        title: locale === 'zh' ? '关键指标分析' : 'Key Indicators Analysis',
                        description: locale === 'zh' ? '重点关注的自闭症特征分析和表现评估' : 'Analysis and assessment of key autism characteristics and manifestations',
                        wordCount: locale === 'zh' ? '250-350字' : '250-350 words',
                        sampleContent: locale === 'zh' ? [
                            '• 社交沟通能力：对话维持、非语言沟通理解能力分析',
                            '• 社交想象力：理解他人观点、预测他人行为的能力',
                            '• 注意力转换：从一个活动转向另一个活动的灵活性',
                            '• 细节关注：对细节的关注程度和模式识别能力',
                            '• 重复行为模式：刻板行为和固定兴趣的表现倾向',
                            '• 感官敏感性：对环境刺激的反应和处理方式',
                            '• 社交互动偏好：独处与群体活动的偏好模式',
                            '• 变化适应：对日常规律改变的反应和适应能力',
                            '• 特殊兴趣：专注于特定主题或活动的程度',
                            '• 沟通风格：直接性、字面理解倾向的表现'
                        ] : [
                            '• Social communication abilities: Conversation maintenance, non-verbal communication comprehension',
                            '• Social imagination: Ability to understand others\' perspectives and predict behaviors',
                            '• Attention switching: Flexibility in transitioning between activities',
                            '• Detail focus: Level of attention to details and pattern recognition abilities',
                            '• Repetitive behavior patterns: Tendencies toward stereotyped behaviors and fixed interests',
                            '• Sensory sensitivity: Responses to and processing of environmental stimuli',
                            '• Social interaction preferences: Preference patterns for solitude vs. group activities',
                            '• Change adaptation: Responses and adaptation to changes in daily routines',
                            '• Special interests: Degree of focus on specific topics or activities',
                            '• Communication style: Directness and literal interpretation tendencies'
                        ]
                    },
                    {
                        icon: <BookOpen className="w-5 h-5" />,
                        title: locale === 'zh' ? '筛查结果解读' : 'Screening Results Interpretation',
                        description: locale === 'zh' ? '详细解读筛查结果的临床意义和后续建议' : 'Detailed interpretation of screening results\' clinical significance and follow-up recommendations',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 得分范围解释：0-3分(低风险)、4-5分(中等风险)、6-10分(高风险)',
                            '• 临床意义：您的得分在XX范围，表示XX风险水平',
                            '• 假阳性考虑：高分可能由其他因素引起，如焦虑、抑郁等',
                            '• 文化因素：不同文化背景可能影响测试结果的解释',
                            '• 年龄考虑：成年期的自闭症特征可能与儿童期有所不同',
                            '• 性别差异：女性的自闭症表现可能更加隐蔽和内化',
                            '• 共病可能：可能伴随的其他神经发育或心理健康状况'
                        ] : [
                            '• Score range interpretation: 0-3 points (low risk), 4-5 points (moderate risk), 6-10 points (high risk)',
                            '• Clinical significance: Your score in XX range indicates XX risk level',
                            '• False positive considerations: High scores may be caused by other factors like anxiety, depression',
                            '• Cultural factors: Different cultural backgrounds may affect test result interpretation',
                            '• Age considerations: Adult autism characteristics may differ from childhood presentations',
                            '• Gender differences: Female autism presentations may be more subtle and internalized',
                            '• Comorbidity possibilities: Potential co-occurring neurodevelopmental or mental health conditions'
                        ]
                    },
                    {
                        icon: <Lightbulb className="w-5 h-5" />,
                        title: locale === 'zh' ? '后续建议' : 'Follow-up Recommendations',
                        description: locale === 'zh' ? '基于筛查结果的专业建议和资源推荐' : 'Professional recommendations and resource suggestions based on screening results',
                        wordCount: locale === 'zh' ? '150-200字' : '150-200 words',
                        sampleContent: locale === 'zh' ? [
                            '• 专业评估建议：寻求有自闭症诊断经验的专业人士',
                            '• 进一步测试：ADOS-2、ADI-R等金标准诊断工具',
                            '• 多学科评估：心理学家、精神科医生、语言治疗师协作',
                            '• 资源获取途径：自闭症支持组织、专业诊断中心',
                            '• 自我了解：阅读相关资料，增进对自闭症的理解',
                            '• 支持网络：寻找理解和支持的社群和朋友',
                            '• 生活调整：根据个人特点调整生活和工作方式'
                        ] : [
                            '• Professional assessment recommendations: Seek professionals experienced in autism diagnosis',
                            '• Further testing: Gold standard diagnostic tools like ADOS-2, ADI-R',
                            '• Multidisciplinary evaluation: Collaboration of psychologists, psychiatrists, speech therapists',
                            '• Resource access pathways: Autism support organizations, professional diagnostic centers',
                            '• Self-understanding: Reading relevant materials to enhance autism understanding',
                            '• Support networks: Finding understanding and supportive communities and friends',
                            '• Life adjustments: Adapting lifestyle and work methods based on personal characteristics'
                        ]
                    }
                ];

            case 'adhdAdultTest':
                return [
                    {
                        icon: <Brain className="w-5 h-5" />,
                        title: locale === 'zh' ? '结果摘要' : 'Summary of Results',
                        description: locale === 'zh' ? '概括性总结ADHD测试结果、症状严重程度及主要发现' : 'Comprehensive summary of ADHD test results, symptom severity, and key findings',
                        wordCount: locale === 'zh' ? '150-200字' : '150-200 words',
                        sampleContent: locale === 'zh' ? [
                            '• 您的ADHD评估总分为XX/72分，处于XX严重程度范围',
                            '• 筛查结果：6项核心症状中有XX项呈阳性，风险等级为XX',
                            '• 症状严重程度评估：注意力不集中XX分，多动冲动XX分',
                            '• 症状对日常生活各领域的具体影响：工作XX、学习XX、人际关系XX',
                            '• 与同龄群体的对比分析：您的得分位于XX百分位',
                            '• 共病风险评估：可能伴随焦虑、抑郁等其他心理健康问题',
                            '• 重要提醒：此为筛查工具，确诊需要专业临床评估'
                        ] : [
                            '• Your ADHD assessment total score is XX/72 points, in XX severity range',
                            '• Screening results: XX out of 6 core symptoms are positive, risk level XX',
                            '• Symptom severity assessment: Inattention XX points, Hyperactivity-Impulsivity XX points',
                            '• Specific impact on life domains: Work XX, Study XX, Relationships XX',
                            '• Comparison with age-matched peers: Your score is at XX percentile',
                            '• Comorbidity risk assessment: May co-occur with anxiety, depression, other mental health issues',
                            '• Important note: This is a screening tool, diagnosis requires professional clinical assessment'
                        ]
                    },
                    {
                        icon: <Target className="w-5 h-5" />,
                        title: locale === 'zh' ? 'ADHD成人测试结果' : 'Adult ADHD Test Results',
                        description: locale === 'zh' ? '详细分析成人ADHD症状评估和各维度表现' : 'Detailed analysis of adult ADHD symptom assessment and dimensional performance',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 总分解读：XX/72分表示您的ADHD症状严重程度为XX级别',
                            '• 注意力缺陷维度：专注困难、易分心、组织能力不足的表现',
                            '• 多动冲动维度：坐立不安、冲动决策、情绪调节困难',
                            '• 核心症状筛查：6项关键症状的具体表现和影响程度',
                            '• 功能损害评估：症状对工作、学习、社交的具体影响',
                            '• 症状持续性：症状在不同环境和时间的一致性表现',
                            '• 发病年龄：成年期症状与儿童期表现的连续性分析',
                            '• 严重程度分级：轻度、中度、重度症状的区分和意义'
                        ] : [
                            '• Total score interpretation: XX/72 points indicates your ADHD symptom severity at XX level',
                            '• Inattention dimension: Manifestations of focus difficulties, distractibility, organizational deficits',
                            '• Hyperactivity-Impulsivity dimension: Restlessness, impulsive decisions, emotional regulation difficulties',
                            '• Core symptom screening: Specific manifestations and impact levels of 6 key symptoms',
                            '• Functional impairment assessment: Specific impacts of symptoms on work, study, social functioning',
                            '• Symptom persistence: Consistent manifestation of symptoms across different environments and times',
                            '• Age of onset: Continuity analysis between adult symptoms and childhood presentations',
                            '• Severity grading: Distinction and significance of mild, moderate, severe symptom levels'
                        ]
                    },
                    {
                        icon: <Target className="w-5 h-5" />,
                        title: locale === 'zh' ? '注意力与专注度分析' : 'Attention and Focus Analysis',
                        description: locale === 'zh' ? '深入分析注意力缺陷和专注度相关表现' : 'In-depth analysis of attention deficit and focus-related performance',
                        wordCount: locale === 'zh' ? '250-300字' : '250-300 words',
                        sampleContent: locale === 'zh' ? [
                            '• 注意力维持困难：长时间专注单一任务的挑战和表现',
                            '• 选择性注意：在多重刺激中筛选重要信息的能力',
                            '• 工作记忆影响：短期信息保持和多任务处理能力分析',
                            '• 分心易感性：外界刺激干扰程度和注意力转移模式',
                            '• 注意力转换：任务切换和重新聚焦的困难程度',
                            '• 执行注意：目标导向行为的计划和监控能力',
                            '• 认知疲劳：长期注意力负荷对认知功能的影响',
                            '• 注意力波动：一天中不同时段的注意力变化模式',
                            '• 环境因素：不同环境对注意力表现的影响分析',
                            '• 补偿策略：个人已发展的注意力管理技巧评估'
                        ] : [
                            '• Attention maintenance difficulties: Challenges and manifestations in sustained focus on single tasks',
                            '• Selective attention: Ability to filter important information from multiple stimuli',
                            '• Working memory impacts: Analysis of short-term information retention and multitasking abilities',
                            '• Distractibility: Degree of external stimulus interference and attention shifting patterns',
                            '• Attention switching: Difficulty level in task transitions and refocusing',
                            '• Executive attention: Planning and monitoring abilities for goal-directed behavior',
                            '• Cognitive fatigue: Effects of prolonged attention demands on cognitive function',
                            '• Attention fluctuation: Patterns of attention changes throughout different times of day',
                            '• Environmental factors: Analysis of different environments\' impact on attention performance',
                            '• Compensatory strategies: Assessment of personally developed attention management techniques'
                        ]
                    },
                    {
                        icon: <Users className="w-5 h-5" />,
                        title: locale === 'zh' ? '冲动性与多动性评估' : 'Impulsivity and Hyperactivity Assessment',
                        description: locale === 'zh' ? '评估冲动行为和多动症状的严重程度' : 'Assessment of impulsive behavior and hyperactivity symptom severity',
                        wordCount: locale === 'zh' ? '250-300字' : '250-300 words',
                        sampleContent: locale === 'zh' ? [
                            '• 冲动决策：快速决策倾向和后果考虑不足的表现',
                            '• 行为抑制：控制不当行为和冲动反应的能力评估',
                            '• 言语冲动：打断他人、过度分享个人信息的倾向',
                            '• 情绪冲动：情绪反应强度和情绪调节困难程度',
                            '• 静坐不安：在需要安静坐着的场合的不适感',
                            '• 内在躁动：内心的不安感和持续的活动需求',
                            '• 过度活动：比同龄人更高的活动水平和能量',
                            '• 风险行为：参与高风险活动的倾向和频率',
                            '• 社交冲动：社交场合中的不当行为和边界问题',
                            '• 购买冲动：冲动消费和财务管理困难的表现'
                        ] : [
                            '• Impulsive decision-making: Tendencies toward quick decisions and insufficient consequence consideration',
                            '• Behavioral inhibition: Assessment of ability to control inappropriate behaviors and impulsive reactions',
                            '• Verbal impulsivity: Tendencies to interrupt others and overshare personal information',
                            '• Emotional impulsivity: Intensity of emotional reactions and degree of emotional regulation difficulties',
                            '• Restlessness: Discomfort in situations requiring quiet sitting',
                            '• Internal restlessness: Inner sense of unease and continuous need for activity',
                            '• Hyperactivity: Higher activity levels and energy compared to same-age peers',
                            '• Risk behaviors: Tendencies and frequency of engaging in high-risk activities',
                            '• Social impulsivity: Inappropriate behaviors and boundary issues in social settings',
                            '• Purchasing impulsivity: Manifestations of impulsive spending and financial management difficulties'
                        ]
                    },
                    {
                        icon: <BookOpen className="w-5 h-5" />,
                        title: locale === 'zh' ? '工作与生活管理策略' : 'Work and Life Management Strategies',
                        description: locale === 'zh' ? '针对成人ADHD的实用管理技巧和策略' : 'Practical management techniques and strategies for adult ADHD',
                        wordCount: locale === 'zh' ? '250-300字' : '250-300 words',
                        sampleContent: locale === 'zh' ? [
                            '• 时间管理技巧：番茄工作法、时间块规划、优先级矩阵',
                            '• 任务分解策略：将大项目分解为可管理的小步骤',
                            '• 提醒系统建立：数字工具和物理提醒的有效组合',
                            '• 工作环境优化：减少干扰源、创建专注空间',
                            '• 拖延症管理：识别拖延触发因素和克服技巧',
                            '• 组织系统：文件管理、物品整理的实用方法',
                            '• 沟通技巧：与同事、上司的有效沟通策略',
                            '• 压力管理：识别压力信号和健康应对方式',
                            '• 职业选择：适合ADHD特质的工作环境和职业类型',
                            '• 生活规律：建立有助于症状管理的日常作息'
                        ] : [
                            '• Time management skills: Pomodoro Technique, time blocking, priority matrix',
                            '• Task breakdown strategies: Dividing large projects into manageable small steps',
                            '• Reminder system establishment: Effective combination of digital tools and physical reminders',
                            '• Work environment optimization: Reducing distractions, creating focused spaces',
                            '• Procrastination management: Identifying procrastination triggers and overcoming techniques',
                            '• Organization systems: Practical methods for file management and item organization',
                            '• Communication skills: Effective communication strategies with colleagues and supervisors',
                            '• Stress management: Identifying stress signals and healthy coping methods',
                            '• Career choices: Work environments and career types suited for ADHD traits',
                            '• Life routines: Establishing daily schedules that support symptom management'
                        ]
                    },
                    {
                        icon: <Lightbulb className="w-5 h-5" />,
                        title: locale === 'zh' ? '治疗建议与资源' : 'Treatment Recommendations and Resources',
                        description: locale === 'zh' ? '专业治疗建议和相关资源推荐' : 'Professional treatment recommendations and related resource suggestions',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 专业诊断：寻求有ADHD诊断经验的精神科医生或心理学家',
                            '• 药物治疗：刺激性和非刺激性药物的选择和考虑',
                            '• 心理治疗：认知行为疗法、正念训练的益处',
                            '• 生活方式干预：运动、睡眠、营养对症状的影响',
                            '• 支持群体：ADHD成人支持小组和在线社区',
                            '• 教育资源：相关书籍、播客、在线课程推荐',
                            '• 辅助技术：ADHD管理应用和工具推荐',
                            '• 家庭支持：为家庭成员提供的理解和支持指导'
                        ] : [
                            '• Professional diagnosis: Seeking psychiatrists or psychologists experienced in ADHD diagnosis',
                            '• Medication treatment: Choices and considerations for stimulant and non-stimulant medications',
                            '• Psychotherapy: Benefits of cognitive behavioral therapy and mindfulness training',
                            '• Lifestyle interventions: Impact of exercise, sleep, and nutrition on symptoms',
                            '• Support groups: Adult ADHD support groups and online communities',
                            '• Educational resources: Recommended books, podcasts, and online courses',
                            '• Assistive technology: ADHD management apps and tool recommendations',
                            '• Family support: Understanding and support guidance for family members'
                        ]
                    }
                ];

            case 'adhdChildTest':
                return [
                    {
                        icon: <Brain className="w-5 h-5" />,
                        title: locale === 'zh' ? '结果摘要' : 'Summary of Results',
                        description: locale === 'zh' ? '概括性总结孩子的ADHD评估结果和主要发现' : 'Comprehensive summary of child ADHD assessment results and key findings',
                        wordCount: locale === 'zh' ? '150-200字' : '150-200 words',
                        sampleContent: locale === 'zh' ? [
                            '• 您孩子的ADHD评估结果显示XX严重程度，需要XX级别的关注',
                            '• 主要症状表现：注意力不集中XX分、多动冲动XX分、对立违抗XX分',
                            '• 评估者一致性：家长评分XX分，教师评分XX分，一致性XX',
                            '• 年龄对照分析：与同龄儿童相比，症状严重程度位于XX百分位',
                            '• 发展阶段特征：症状表现符合/不符合该年龄段的典型模式',
                            '• 功能损害评估：对学习、社交、家庭生活的具体影响程度',
                            '• 重要提醒：此为筛查评估，确诊需要专业临床诊断'
                        ] : [
                            '• Your child\'s ADHD assessment shows XX severity level, requiring XX level of attention',
                            '• Main symptom manifestations: Inattention XX points, Hyperactivity-Impulsivity XX points, Oppositional Defiance XX points',
                            '• Rater consistency: Parent rating XX points, Teacher rating XX points, consistency XX',
                            '• Age comparison: Compared to same-age children, symptom severity at XX percentile',
                            '• Developmental characteristics: Symptom presentation fits/doesn\'t fit typical patterns for this age group',
                            '• Functional impairment assessment: Specific impact levels on learning, social, and family life',
                            '• Important note: This is a screening assessment, diagnosis requires professional clinical evaluation'
                        ]
                    },
                    {
                        icon: <Target className="w-5 h-5" />,
                        title: locale === 'zh' ? '儿童ADHD测试结果' : 'Child ADHD Test Results',
                        description: locale === 'zh' ? '详细分析儿童ADHD症状评估和各维度表现' : 'Detailed analysis of child ADHD symptom assessment and dimensional performance',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 注意力不集中维度：课堂专注困难、作业拖延、指令遗忘',
                            '• 多动冲动维度：坐立不安、过度活跃、冲动行为表现',
                            '• 对立违抗维度：规则挑战、权威反抗、情绪爆发频率',
                            '• 评估者间一致性：家长和教师观察的相似性和差异',
                            '• 环境特异性：不同环境下症状表现的变化模式',
                            '• 发展适宜性：症状是否超出该年龄段的正常范围',
                            '• 功能损害程度：对学业、社交、家庭功能的具体影响',
                            '• 共病风险：可能伴随的学习障碍、情绪问题等'
                        ] : [
                            '• Inattention dimension: Classroom focus difficulties, homework procrastination, instruction forgetting',
                            '• Hyperactivity-Impulsivity dimension: Restlessness, excessive activity, impulsive behavior manifestations',
                            '• Oppositional Defiance dimension: Rule challenges, authority defiance, emotional outburst frequency',
                            '• Inter-rater consistency: Similarities and differences in parent and teacher observations',
                            '• Environmental specificity: Variation patterns of symptom manifestation across different environments',
                            '• Developmental appropriateness: Whether symptoms exceed normal range for this age group',
                            '• Functional impairment level: Specific impacts on academic, social, and family functioning',
                            '• Comorbidity risks: Potential co-occurring learning disabilities, emotional problems, etc.'
                        ]
                    },
                    {
                        icon: <BookOpen className="w-5 h-5" />,
                        title: locale === 'zh' ? '学校表现分析' : 'School Performance Analysis',
                        description: locale === 'zh' ? '分析孩子在学校环境中的表现和挑战' : 'Analysis of child performance and challenges in school environment',
                        wordCount: locale === 'zh' ? '250-300字' : '250-300 words',
                        sampleContent: locale === 'zh' ? [
                            '• 课堂注意力困难：难以长时间专注听讲、易受干扰',
                            '• 学习任务挑战：作业完成困难、时间管理问题',
                            '• 组织能力不足：书包整理、学习用品管理困难',
                            '• 社交互动问题：与同龄人的关系建立和维护挑战',
                            '• 规则遵守困难：课堂纪律、指令执行的挑战',
                            '• 学业表现影响：各科目成绩和学习效率分析',
                            '• 教师反馈整合：课堂行为观察和专业建议',
                            '• 同伴关系：友谊建立、冲突解决能力评估',
                            '• 特殊需求识别：可能需要的学习支持和调整',
                            '• 优势领域发现：孩子在学校环境中的积极表现'
                        ] : [
                            '• Classroom attention difficulties: Trouble sustaining focus during lessons, easily distracted',
                            '• Learning task challenges: Homework completion difficulties, time management issues',
                            '• Organizational deficits: Backpack organization, school supply management difficulties',
                            '• Social interaction problems: Challenges in establishing and maintaining peer relationships',
                            '• Rule adherence difficulties: Classroom discipline and instruction-following challenges',
                            '• Academic performance impact: Grade analysis and learning efficiency across subjects',
                            '• Teacher feedback integration: Classroom behavior observations and professional suggestions',
                            '• Peer relationships: Friendship building and conflict resolution ability assessment',
                            '• Special needs identification: Potential learning supports and accommodations needed',
                            '• Strength area discovery: Child\'s positive performances in school environment'
                        ]
                    },
                    {
                        icon: <Users className="w-5 h-5" />,
                        title: locale === 'zh' ? '家庭支持策略' : 'Family Support Strategies',
                        description: locale === 'zh' ? '为家长提供的实用支持策略和管理技巧' : 'Practical support strategies and management techniques for parents',
                        wordCount: locale === 'zh' ? '250-300字' : '250-300 words',
                        sampleContent: locale === 'zh' ? [
                            '• 行为管理技巧：正面强化、一致性规则、后果设定',
                            '• 日常结构建立：规律作息、清晰期望、可预测环境',
                            '• 沟通策略：有效指令给予、积极倾听、情感验证',
                            '• 作业支持：学习环境优化、时间管理、任务分解',
                            '• 情绪调节帮助：识别情绪触发、冷静技巧教授',
                            '• 社交技能培养：角色扮演、社交故事、友谊指导',
                            '• 自尊建设：优势发现、成功体验创造、无条件接纳',
                            '• 压力管理：家庭压力识别、自我照顾重要性',
                            '• 兄弟姐妹支持：公平关注、特殊需求解释',
                            '• 专业协作：与学校、医生、治疗师的有效合作'
                        ] : [
                            '• Behavioral management techniques: Positive reinforcement, consistent rules, consequence setting',
                            '• Daily structure establishment: Regular routines, clear expectations, predictable environment',
                            '• Communication strategies: Effective instruction giving, active listening, emotional validation',
                            '• Homework support: Learning environment optimization, time management, task breakdown',
                            '• Emotional regulation assistance: Identifying emotional triggers, teaching calming techniques',
                            '• Social skills development: Role-playing, social stories, friendship guidance',
                            '• Self-esteem building: Strength discovery, success experience creation, unconditional acceptance',
                            '• Stress management: Family stress identification, importance of self-care',
                            '• Sibling support: Fair attention, explaining special needs',
                            '• Professional collaboration: Effective cooperation with schools, doctors, therapists'
                        ]
                    },
                    {
                        icon: <Lightbulb className="w-5 h-5" />,
                        title: locale === 'zh' ? '发展与成长规划' : 'Development and Growth Planning',
                        description: locale === 'zh' ? '基于评估结果的长期发展规划和成长支持' : 'Long-term development planning and growth support based on assessment results',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 发展里程碑：年龄适宜的期望和目标设定',
                            '• 技能培养重点：注意力、自控力、社交技能的渐进训练',
                            '• 学习支持计划：个性化教育计划(IEP)的考虑和申请',
                            '• 治疗干预选择：行为治疗、家庭治疗、药物治疗的时机',
                            '• 环境调整建议：家庭和学校环境的优化策略',
                            '• 长期预后：症状管理和功能改善的现实期望',
                            '• 转衔准备：不同教育阶段的过渡支持',
                            '• 优势发展：发现和培养孩子的特长和兴趣'
                        ] : [
                            '• Developmental milestones: Age-appropriate expectations and goal setting',
                            '• Skill development priorities: Progressive training in attention, self-control, social skills',
                            '• Learning support planning: Consideration and application for Individualized Education Program (IEP)',
                            '• Treatment intervention choices: Timing for behavioral therapy, family therapy, medication treatment',
                            '• Environmental adjustment recommendations: Optimization strategies for home and school environments',
                            '• Long-term prognosis: Realistic expectations for symptom management and functional improvement',
                            '• Transition preparation: Support for transitions between different educational stages',
                            '• Strength development: Discovering and nurturing child\'s talents and interests'
                        ]
                    },
                    {
                        icon: <Lightbulb className="w-5 h-5" />,
                        title: locale === 'zh' ? '专业建议与资源' : 'Professional Recommendations and Resources',
                        description: locale === 'zh' ? '专业的教育建议和相关资源推荐' : 'Professional educational recommendations and related resource suggestions',
                        wordCount: locale === 'zh' ? '200-250字' : '200-250 words',
                        sampleContent: locale === 'zh' ? [
                            '• 专业诊断：寻求儿童精神科医生或发展心理学家',
                            '• 学校配合：与教师、学校心理学家的协作策略',
                            '• 治疗选择：行为干预、认知训练、家庭治疗的考虑',
                            '• 教育支持：特殊教育服务、学习调整的申请流程',
                            '• 家长培训：ADHD家长培训课程和支持小组',
                            '• 社区资源：本地ADHD支持组织和服务机构',
                            '• 在线资源：可信的ADHD信息网站和教育材料',
                            '• 监测工具：症状追踪和进展评估的实用工具'
                        ] : [
                            '• Professional diagnosis: Seeking child psychiatrists or developmental psychologists',
                            '• School collaboration: Cooperation strategies with teachers and school psychologists',
                            '• Treatment options: Considerations for behavioral interventions, cognitive training, family therapy',
                            '• Educational support: Application process for special education services and learning accommodations',
                            '• Parent training: ADHD parent training courses and support groups',
                            '• Community resources: Local ADHD support organizations and service agencies',
                            '• Online resources: Trusted ADHD information websites and educational materials',
                            '• Monitoring tools: Practical tools for symptom tracking and progress assessment'
                        ]
                    }
                ];

            default:
                return [];
        }
    };

    const chapters = getTemplateChapters();

    // 计算总字数
    const totalWordCount = chapters.reduce((total, chapter) => {
        const wordCount = chapter.wordCount.match(/\d+/g);
        if (wordCount) {
            const maxWords = Math.max(...wordCount.map(Number));
            return total + maxWords;
        }
        return total;
    }, 0);

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden relative">

            {/* Template chapters with blur effect - only blur the middle content */}
            <div className="relative">
                {/* Blurred chapters section */}
                <div className="relative p-4 sm:p-6">
                    <div className="space-y-4 sm:space-y-6">
                        {chapters.map((chapter, index) => (
                            <div key={index} className="group">
                                {/* Enhanced chapter card */}
                                <div className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm hover:shadow-md transition-all duration-300">
                                    {/* Chapter content */}
                                    <div className="p-4 sm:p-6">
                                        <div className="flex items-start gap-3 sm:gap-4">
                                            <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                                                {chapter.icon}
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between mb-2">
                                                    <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                                                        {chapter.title}
                                                    </h3>
                                                    <span className="text-xs sm:text-sm text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full">
                                                        {chapter.wordCount}
                                                    </span>
                                                </div>
                                                <p className="text-sm sm:text-base text-gray-600 leading-relaxed mb-4">
                                                    {chapter.description}
                                                </p>

                                                {/* Enhanced sample content with better visual hierarchy */}
                                                <div className="space-y-2">
                                                    {chapter.sampleContent.slice(0, Math.min(chapter.sampleContent.length, 8)).map((content, contentIndex) => (
                                                        <div key={contentIndex} className="relative">
                                                            <div className="text-sm text-gray-700 bg-gray-50 px-3 py-2 rounded-lg border border-gray-100 leading-relaxed">
                                                                {content}
                                                            </div>
                                                            {/* Gradient fade effect for individual items */}
                                                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-white/60 rounded-lg pointer-events-none"></div>
                                                        </div>
                                                    ))}

                                                    {/* Show more indicator if there are more items */}
                                                    {chapter.sampleContent.length > 8 && (
                                                        <div className="text-center py-2">
                                                            <span className="text-xs text-gray-400 bg-gray-100 px-3 py-1 rounded-full">
                                                                +{chapter.sampleContent.length - 8} {locale === 'zh' ? '更多内容' : 'more items'}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Enhanced blur overlay with gradient - only covers the chapters section */}
                    <div className="absolute inset-0 bg-gradient-to-b from-white/10 via-white/30 to-white/60 backdrop-blur-[1px] pointer-events-none"></div>

                    {/* Subtle pattern overlay */}
                    <div className="absolute inset-0 opacity-5 pointer-events-none" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                    }}></div>
                </div>

                {/* Simple unlock message - not blurred */}
                <div className="p-4 sm:p-6 pt-0">
                    <div className="text-center">
                        <p className="text-base font-medium text-gray-900 mb-2">
                            {locale === 'zh'
                                ? '解锁完整报告，获得专业深度分析'
                                : 'Unlock Complete Report for Professional In-depth Analysis'
                            }
                        </p>
                        <p className="text-sm text-gray-600">
                            {locale === 'zh'
                                ? '基于您的测试结果，AI生成个性化专业建议'
                                : 'AI-generated personalized professional advice based on your test results'
                            }
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
} 