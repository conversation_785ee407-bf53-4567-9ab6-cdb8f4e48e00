'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Brain, Target, AlertTriangle, TrendingUp } from 'lucide-react';

interface AdhdAdultVisualizationProps {
    scores: {
        total: { score: number; max: number; percentage: number };
        inattention: { score: number; max: number; mean: number };
        hyperactivity: { score: number; max: number; mean: number };
        screener: { positiveItems: number; total: number; riskLevel: 'low' | 'moderate' | 'high' };
    };
    locale: string;
}

export default function AdhdAdultVisualization({ scores, locale }: AdhdAdultVisualizationProps) {
    const isZh = locale === 'zh';

    // Risk level colors and labels
    const getRiskLevelInfo = (level: string) => {
        switch (level) {
            case 'high':
                return {
                    color: 'text-red-600',
                    bgColor: 'bg-red-50',
                    borderColor: 'border-red-200',
                    label: isZh ? '高风险' : 'High Risk',
                    icon: AlertTriangle
                };
            case 'moderate':
                return {
                    color: 'text-yellow-600',
                    bgColor: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                    label: isZh ? '中等风险' : 'Moderate Risk',
                    icon: Target
                };
            default:
                return {
                    color: 'text-green-600',
                    bgColor: 'bg-green-50',
                    borderColor: 'border-green-200',
                    label: isZh ? '低风险' : 'Low Risk',
                    icon: Target
                };
        }
    };

    const riskInfo = getRiskLevelInfo(scores.screener.riskLevel);
    const RiskIcon = riskInfo.icon;

    // Calculate percentages for visual representation
    const inattentionPercentage = (scores.inattention.score / scores.inattention.max) * 100;
    const hyperactivityPercentage = (scores.hyperactivity.score / scores.hyperactivity.max) * 100;
    const screenerPercentage = (scores.screener.positiveItems / scores.screener.total) * 100;

    return (
        <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-xl p-4 sm:p-6 mb-6 border border-purple-100">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Brain className="w-6 h-6 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 break-words">
                        {isZh ? 'ADHD 成人评估结果' : 'Adult ADHD Assessment Results'}
                    </h3>
                    <p className="text-gray-600 text-xs sm:text-sm break-words">
                        {isZh ? '基于 ASRS-v1.1 量表的专业评估' : 'Professional assessment based on ASRS-v1.1 scale'}
                    </p>
                </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6">
                {/* Total Score */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200 shadow-sm"
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '总分' : 'Total Score'}
                        </span>
                        <TrendingUp className="w-4 h-4 text-purple-500" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                        {scores.total.score}<span className="text-lg text-gray-500">/{scores.total.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${scores.total.percentage}%` }}
                            transition={{ delay: 0.3, duration: 1 }}
                            className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full"
                        />
                    </div>
                </motion.div>

                {/* Screener Risk Level */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className={`${riskInfo.bgColor} ${riskInfo.borderColor} rounded-lg p-4 border shadow-sm`}
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '筛查风险' : 'Screening Risk'}
                        </span>
                        <RiskIcon className={`w-4 h-4 ${riskInfo.color}`} />
                    </div>
                    <div className={`text-lg font-bold ${riskInfo.color}`}>
                        {riskInfo.label}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                        {scores.screener.positiveItems}/{scores.screener.total} {isZh ? '项阳性' : 'positive items'}
                    </div>
                </motion.div>

                {/* Inattention */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '注意力缺陷' : 'Inattention'}
                        </span>
                        <div className="w-4 h-4 bg-blue-500 rounded-full" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                        {scores.inattention.score}<span className="text-sm text-gray-500">/{scores.inattention.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${inattentionPercentage}%` }}
                            transition={{ delay: 0.5, duration: 1 }}
                            className="bg-blue-500 h-2 rounded-full"
                        />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        {isZh ? '平均' : 'Mean'}: {scores.inattention.mean.toFixed(2)}
                    </div>
                </motion.div>

                {/* Hyperactivity */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '多动冲动' : 'Hyperactivity'}
                        </span>
                        <div className="w-4 h-4 bg-orange-500 rounded-full" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                        {scores.hyperactivity.score}<span className="text-sm text-gray-500">/{scores.hyperactivity.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${hyperactivityPercentage}%` }}
                            transition={{ delay: 0.6, duration: 1 }}
                            className="bg-orange-500 h-2 rounded-full"
                        />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        {isZh ? '平均' : 'Mean'}: {scores.hyperactivity.mean.toFixed(2)}
                    </div>
                </motion.div>
            </div>

            {/* Circular Progress for Screener */}
            <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 }}
                className="flex justify-center"
            >
                <div className="relative w-32 h-32">
                    <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
                        <circle
                            cx="50"
                            cy="50"
                            r="40"
                            stroke="currentColor"
                            strokeWidth="8"
                            fill="transparent"
                            className="text-gray-200"
                        />
                        <motion.circle
                            cx="50"
                            cy="50"
                            r="40"
                            stroke="currentColor"
                            strokeWidth="8"
                            fill="transparent"
                            strokeLinecap="round"
                            className={riskInfo.color}
                            initial={{ strokeDasharray: "0 251.2" }}
                            animate={{ strokeDasharray: `${(screenerPercentage / 100) * 251.2} 251.2` }}
                            transition={{ delay: 0.8, duration: 1.5 }}
                        />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                            <div className={`text-2xl font-bold ${riskInfo.color}`}>
                                {Math.round(screenerPercentage)}%
                            </div>
                            <div className="text-xs text-gray-600">
                                {isZh ? '筛查阳性' : 'Positive'}
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>

            <div className="text-center mt-4">
                <p className="text-sm text-gray-600">
                    {isZh 
                        ? '解锁完整报告查看详细分析和专业建议' 
                        : 'Unlock the full report for detailed analysis and professional recommendations'
                    }
                </p>
            </div>
        </div>
    );
}
