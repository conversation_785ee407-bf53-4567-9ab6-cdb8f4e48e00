'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Brain, MessageCircle, Users, Zap, Target, AlertTriangle, CheckCircle } from 'lucide-react';

interface RaadsrVisualizationProps {
    scores: {
        language: { score: number; max: number; significant: boolean };
        social: { score: number; max: number; significant: boolean };
        sensoryMotor: { score: number; max: number; significant: boolean };
        interests: { score: number; max: number; significant: boolean };
        total: { score: number; max: number; significant: boolean };
    };
    locale: string;
}

export default function RaadsrVisualization({ scores, locale }: RaadsrVisualizationProps) {
    const isZh = locale === 'zh';

    // Calculate percentages
    const languagePercentage = (scores.language.score / scores.language.max) * 100;
    const socialPercentage = (scores.social.score / scores.social.max) * 100;
    const sensoryMotorPercentage = (scores.sensoryMotor.score / scores.sensoryMotor.max) * 100;
    const interestsPercentage = (scores.interests.score / scores.interests.max) * 100;
    const totalPercentage = (scores.total.score / scores.total.max) * 100;

    // Count significant domains
    const significantDomains = [
        scores.language.significant,
        scores.social.significant,
        scores.sensoryMotor.significant,
        scores.interests.significant
    ].filter(Boolean).length;

    // Domain configurations
    const domains = [
        {
            key: 'language',
            icon: MessageCircle,
            label: isZh ? '语言沟通' : 'Language',
            color: 'blue',
            score: scores.language.score,
            max: scores.language.max,
            percentage: languagePercentage,
            significant: scores.language.significant
        },
        {
            key: 'social',
            icon: Users,
            label: isZh ? '社交关系' : 'Social',
            color: 'green',
            score: scores.social.score,
            max: scores.social.max,
            percentage: socialPercentage,
            significant: scores.social.significant
        },
        {
            key: 'sensoryMotor',
            icon: Zap,
            label: isZh ? '感知运动' : 'Sensory-Motor',
            color: 'purple',
            score: scores.sensoryMotor.score,
            max: scores.sensoryMotor.max,
            percentage: sensoryMotorPercentage,
            significant: scores.sensoryMotor.significant
        },
        {
            key: 'interests',
            icon: Target,
            label: isZh ? '兴趣规则' : 'Interests',
            color: 'orange',
            score: scores.interests.score,
            max: scores.interests.max,
            percentage: interestsPercentage,
            significant: scores.interests.significant
        }
    ];

    const getColorClasses = (color: string, significant: boolean) => {
        const colors = {
            blue: {
                bg: significant ? 'bg-blue-50' : 'bg-white',
                border: significant ? 'border-blue-200' : 'border-gray-200',
                text: 'text-blue-600',
                progress: 'bg-blue-500',
                dot: significant ? 'bg-blue-500' : 'bg-gray-300'
            },
            green: {
                bg: significant ? 'bg-green-50' : 'bg-white',
                border: significant ? 'border-green-200' : 'border-gray-200',
                text: 'text-green-600',
                progress: 'bg-green-500',
                dot: significant ? 'bg-green-500' : 'bg-gray-300'
            },
            purple: {
                bg: significant ? 'bg-purple-50' : 'bg-white',
                border: significant ? 'border-purple-200' : 'border-gray-200',
                text: 'text-purple-600',
                progress: 'bg-purple-500',
                dot: significant ? 'bg-purple-500' : 'bg-gray-300'
            },
            orange: {
                bg: significant ? 'bg-orange-50' : 'bg-white',
                border: significant ? 'border-orange-200' : 'border-gray-200',
                text: 'text-orange-600',
                progress: 'bg-orange-500',
                dot: significant ? 'bg-orange-500' : 'bg-gray-300'
            }
        };
        return colors[color as keyof typeof colors];
    };

    return (
        <div className="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl p-6 mb-6 border border-indigo-100">
            <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                    <h3 className="text-xl font-bold text-gray-900">
                        {isZh ? 'RAADS-R 自闭症评估结果' : 'RAADS-R Autism Assessment Results'}
                    </h3>
                    <p className="text-gray-600 text-sm">
                        {isZh ? '成人自闭症谱系障碍诊断量表' : 'Ritvo Autism Asperger Diagnostic Scale-Revised'}
                    </p>
                </div>
            </div>

            {/* Total Score and Clinical Significance */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '总分' : 'Total Score'}
                        </span>
                        <Brain className="w-4 h-4 text-indigo-500" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                        {scores.total.score}<span className="text-lg text-gray-500">/{scores.total.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${totalPercentage}%` }}
                            transition={{ delay: 0.3, duration: 1.5 }}
                            className="bg-gradient-to-r from-indigo-500 to-blue-500 h-3 rounded-full"
                        />
                    </div>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className={`rounded-lg p-4 border shadow-sm ${scores.total.significant ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'}`}
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '临床意义' : 'Clinical Significance'}
                        </span>
                        {scores.total.significant ? (
                            <AlertTriangle className="w-4 h-4 text-red-600" />
                        ) : (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                        )}
                    </div>
                    <div className={`text-lg font-bold ${scores.total.significant ? 'text-red-600' : 'text-green-600'}`}>
                        {scores.total.significant 
                            ? (isZh ? '达到临床阈值' : 'Clinically Significant')
                            : (isZh ? '未达到临床阈值' : 'Not Clinically Significant')
                        }
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                        {significantDomains}/4 {isZh ? '个维度显著' : 'domains significant'}
                    </div>
                </motion.div>
            </div>

            {/* Domain Scores */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {domains.map((domain, index) => {
                    const colorClasses = getColorClasses(domain.color, domain.significant);
                    const Icon = domain.icon;
                    
                    return (
                        <motion.div
                            key={domain.key}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 + index * 0.1 }}
                            className={`${colorClasses.bg} ${colorClasses.border} rounded-lg p-4 border shadow-sm`}
                        >
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-gray-600">
                                    {domain.label}
                                </span>
                                <div className="flex items-center gap-2">
                                    <Icon className={`w-4 h-4 ${colorClasses.text}`} />
                                    <div className={`w-3 h-3 rounded-full ${colorClasses.dot}`} />
                                </div>
                            </div>
                            <div className="text-lg font-bold text-gray-900">
                                {domain.score}<span className="text-sm text-gray-500">/{domain.max}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                                <motion.div
                                    initial={{ width: 0 }}
                                    animate={{ width: `${domain.percentage}%` }}
                                    transition={{ delay: 0.5 + index * 0.1, duration: 1 }}
                                    className={`${colorClasses.progress} h-2 rounded-full`}
                                />
                            </div>
                            {domain.significant && (
                                <div className={`text-xs ${colorClasses.text} font-medium mt-1`}>
                                    {isZh ? '临床显著' : 'Clinically Significant'}
                                </div>
                            )}
                        </motion.div>
                    );
                })}
            </div>

            {/* Radar Chart Representation */}
            <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8 }}
                className="flex justify-center"
            >
                <div className="relative w-40 h-40">
                    <svg className="w-40 h-40" viewBox="0 0 200 200">
                        {/* Background circles */}
                        <circle cx="100" cy="100" r="80" fill="none" stroke="#e5e7eb" strokeWidth="1" />
                        <circle cx="100" cy="100" r="60" fill="none" stroke="#e5e7eb" strokeWidth="1" />
                        <circle cx="100" cy="100" r="40" fill="none" stroke="#e5e7eb" strokeWidth="1" />
                        <circle cx="100" cy="100" r="20" fill="none" stroke="#e5e7eb" strokeWidth="1" />
                        
                        {/* Axis lines */}
                        <line x1="100" y1="20" x2="100" y2="180" stroke="#e5e7eb" strokeWidth="1" />
                        <line x1="20" y1="100" x2="180" y2="100" stroke="#e5e7eb" strokeWidth="1" />
                        
                        {/* Data points */}
                        {domains.map((domain, index) => {
                            const angle = (index * 90 - 90) * (Math.PI / 180);
                            const radius = (domain.percentage / 100) * 80;
                            const x = 100 + radius * Math.cos(angle);
                            const y = 100 + radius * Math.sin(angle);
                            const colorClasses = getColorClasses(domain.color, domain.significant);
                            
                            return (
                                <motion.circle
                                    key={domain.key}
                                    cx={x}
                                    cy={y}
                                    r="4"
                                    className={colorClasses.progress.replace('bg-', 'fill-')}
                                    initial={{ r: 0 }}
                                    animate={{ r: 4 }}
                                    transition={{ delay: 1 + index * 0.1 }}
                                />
                            );
                        })}
                    </svg>
                </div>
            </motion.div>

            <div className="text-center mt-4">
                <p className="text-sm text-gray-600">
                    {isZh 
                        ? '解锁完整报告查看详细分析和专业建议' 
                        : 'Unlock the full report for detailed analysis and professional recommendations'
                    }
                </p>
            </div>
        </div>
    );
}
