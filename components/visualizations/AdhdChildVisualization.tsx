'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Brain, AlertTriangle, Target, TrendingUp } from 'lucide-react';

interface AdhdChildVisualizationProps {
    scores: {
        inattention: { score: number; max: number; mean: number; significant: boolean };
        hyperactivity: { score: number; max: number; mean: number; significant: boolean };
        opposition: { score: number; max: number; mean: number; significant: boolean };
        total: { score: number; max: number };
        subtype: 'combined' | 'inattentive' | 'hyperactive' | 'none';
        respondent: string;
    };
    locale: string;
}

export default function AdhdChildVisualization({ scores, locale }: AdhdChildVisualizationProps) {
    const isZh = locale === 'zh';

    // Subtype information
    const getSubtypeInfo = (subtype: string) => {
        switch (subtype) {
            case 'combined':
                return {
                    label: isZh ? '混合型' : 'Combined Type',
                    color: 'text-red-600',
                    bgColor: 'bg-red-50',
                    borderColor: 'border-red-200',
                    icon: AlertTriangle
                };
            case 'inattentive':
                return {
                    label: isZh ? '注意力缺陷型' : 'Inattentive Type',
                    color: 'text-blue-600',
                    bgColor: 'bg-blue-50',
                    borderColor: 'border-blue-200',
                    icon: Brain
                };
            case 'hyperactive':
                return {
                    label: isZh ? '多动冲动型' : 'Hyperactive Type',
                    color: 'text-orange-600',
                    bgColor: 'bg-orange-50',
                    borderColor: 'border-orange-200',
                    icon: TrendingUp
                };
            default:
                return {
                    label: isZh ? '无显著症状' : 'No Significant Symptoms',
                    color: 'text-green-600',
                    bgColor: 'bg-green-50',
                    borderColor: 'border-green-200',
                    icon: Target
                };
        }
    };

    const subtypeInfo = getSubtypeInfo(scores.subtype);
    const SubtypeIcon = subtypeInfo.icon;

    // Calculate percentages
    const inattentionPercentage = (scores.inattention.score / scores.inattention.max) * 100;
    const hyperactivityPercentage = (scores.hyperactivity.score / scores.hyperactivity.max) * 100;
    const oppositionPercentage = (scores.opposition.score / scores.opposition.max) * 100;
    const totalPercentage = (scores.total.score / scores.total.max) * 100;

    return (
        <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 mb-6 border border-indigo-100">
            <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                </div>
                <div>
                    <h3 className="text-xl font-bold text-gray-900">
                        {isZh ? '儿童 ADHD 评估结果' : 'Child ADHD Assessment Results'}
                    </h3>
                    <p className="text-gray-600 text-sm">
                        {isZh ? `评估者：${scores.respondent === 'parent' ? '家长' : '教师'}` : `Respondent: ${scores.respondent === 'parent' ? 'Parent' : 'Teacher'}`}
                    </p>
                </div>
            </div>

            {/* Subtype and Total Score */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className={`${subtypeInfo.bgColor} ${subtypeInfo.borderColor} rounded-lg p-4 border shadow-sm`}
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? 'ADHD 亚型' : 'ADHD Subtype'}
                        </span>
                        <SubtypeIcon className={`w-5 h-5 ${subtypeInfo.color}`} />
                    </div>
                    <div className={`text-lg font-bold ${subtypeInfo.color}`}>
                        {subtypeInfo.label}
                    </div>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '总分' : 'Total Score'}
                        </span>
                        <TrendingUp className="w-4 h-4 text-indigo-500" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                        {scores.total.score}<span className="text-lg text-gray-500">/{scores.total.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${totalPercentage}%` }}
                            transition={{ delay: 0.4, duration: 1 }}
                            className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full"
                        />
                    </div>
                </motion.div>
            </div>

            {/* Symptom Dimensions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* Inattention */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className={`rounded-lg p-4 border shadow-sm ${scores.inattention.significant ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'}`}
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '注意力缺陷' : 'Inattention'}
                        </span>
                        <div className={`w-4 h-4 rounded-full ${scores.inattention.significant ? 'bg-blue-500' : 'bg-gray-300'}`} />
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                        {scores.inattention.score}<span className="text-sm text-gray-500">/{scores.inattention.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${inattentionPercentage}%` }}
                            transition={{ delay: 0.5, duration: 1 }}
                            className="bg-blue-500 h-2 rounded-full"
                        />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        {isZh ? '平均' : 'Mean'}: {scores.inattention.mean.toFixed(2)}
                        {scores.inattention.significant && (
                            <span className="ml-2 text-blue-600 font-medium">
                                {isZh ? '显著' : 'Significant'}
                            </span>
                        )}
                    </div>
                </motion.div>

                {/* Hyperactivity */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className={`rounded-lg p-4 border shadow-sm ${scores.hyperactivity.significant ? 'bg-orange-50 border-orange-200' : 'bg-white border-gray-200'}`}
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '多动冲动' : 'Hyperactivity'}
                        </span>
                        <div className={`w-4 h-4 rounded-full ${scores.hyperactivity.significant ? 'bg-orange-500' : 'bg-gray-300'}`} />
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                        {scores.hyperactivity.score}<span className="text-sm text-gray-500">/{scores.hyperactivity.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${hyperactivityPercentage}%` }}
                            transition={{ delay: 0.6, duration: 1 }}
                            className="bg-orange-500 h-2 rounded-full"
                        />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        {isZh ? '平均' : 'Mean'}: {scores.hyperactivity.mean.toFixed(2)}
                        {scores.hyperactivity.significant && (
                            <span className="ml-2 text-orange-600 font-medium">
                                {isZh ? '显著' : 'Significant'}
                            </span>
                        )}
                    </div>
                </motion.div>

                {/* Opposition */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className={`rounded-lg p-4 border shadow-sm ${scores.opposition.significant ? 'bg-red-50 border-red-200' : 'bg-white border-gray-200'}`}
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '对立违抗' : 'Opposition'}
                        </span>
                        <div className={`w-4 h-4 rounded-full ${scores.opposition.significant ? 'bg-red-500' : 'bg-gray-300'}`} />
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                        {scores.opposition.score}<span className="text-sm text-gray-500">/{scores.opposition.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${oppositionPercentage}%` }}
                            transition={{ delay: 0.7, duration: 1 }}
                            className="bg-red-500 h-2 rounded-full"
                        />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        {isZh ? '平均' : 'Mean'}: {scores.opposition.mean.toFixed(2)}
                        {scores.opposition.significant && (
                            <span className="ml-2 text-red-600 font-medium">
                                {isZh ? '显著' : 'Significant'}
                            </span>
                        )}
                    </div>
                </motion.div>
            </div>

            <div className="text-center">
                <p className="text-sm text-gray-600">
                    {isZh 
                        ? '解锁完整报告查看详细分析和专业建议' 
                        : 'Unlock the full report for detailed analysis and professional recommendations'
                    }
                </p>
            </div>
        </div>
    );
}
