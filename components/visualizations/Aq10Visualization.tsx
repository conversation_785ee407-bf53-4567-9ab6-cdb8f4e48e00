'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Brain, AlertTriangle, CheckCircle, Target, TrendingUp } from 'lucide-react';

interface Aq10VisualizationProps {
    scores: {
        score: number;
        max: number;
        significant: boolean;
    };
    locale: string;
}

export default function Aq10Visualization({ scores, locale }: Aq10VisualizationProps) {
    const isZh = locale === 'zh';

    // Calculate percentage
    const percentage = (scores.score / scores.max) * 100;
    
    // Risk level based on score
    const getRiskLevel = (score: number) => {
        if (score >= 8) return 'high';
        if (score >= 6) return 'moderate';
        if (score >= 4) return 'mild';
        return 'low';
    };

    const riskLevel = getRiskLevel(scores.score);

    const getRiskInfo = (level: string) => {
        switch (level) {
            case 'high':
                return {
                    label: isZh ? '高风险' : 'High Risk',
                    color: 'text-red-600',
                    bgColor: 'bg-red-50',
                    borderColor: 'border-red-200',
                    progressColor: 'bg-red-500',
                    icon: AlertTriangle,
                    description: isZh ? '强烈建议专业评估' : 'Professional evaluation strongly recommended'
                };
            case 'moderate':
                return {
                    label: isZh ? '中等风险' : 'Moderate Risk',
                    color: 'text-orange-600',
                    bgColor: 'bg-orange-50',
                    borderColor: 'border-orange-200',
                    progressColor: 'bg-orange-500',
                    icon: Target,
                    description: isZh ? '建议专业评估' : 'Professional evaluation recommended'
                };
            case 'mild':
                return {
                    label: isZh ? '轻度风险' : 'Mild Risk',
                    color: 'text-yellow-600',
                    bgColor: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                    progressColor: 'bg-yellow-500',
                    icon: Target,
                    description: isZh ? '可考虑专业咨询' : 'Consider professional consultation'
                };
            default:
                return {
                    label: isZh ? '低风险' : 'Low Risk',
                    color: 'text-green-600',
                    bgColor: 'bg-green-50',
                    borderColor: 'border-green-200',
                    progressColor: 'bg-green-500',
                    icon: CheckCircle,
                    description: isZh ? '无明显自闭症特征' : 'No significant autism traits'
                };
        }
    };

    const riskInfo = getRiskInfo(riskLevel);
    const RiskIcon = riskInfo.icon;

    // Score ranges for visualization
    const scoreRanges = [
        { min: 0, max: 3, label: isZh ? '低风险' : 'Low', color: 'bg-green-500' },
        { min: 4, max: 5, label: isZh ? '轻度' : 'Mild', color: 'bg-yellow-500' },
        { min: 6, max: 7, label: isZh ? '中等' : 'Moderate', color: 'bg-orange-500' },
        { min: 8, max: 10, label: isZh ? '高风险' : 'High', color: 'bg-red-500' }
    ];

    return (
        <div className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-xl p-6 mb-6 border border-teal-100">
            <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center">
                    <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                    <h3 className="text-xl font-bold text-gray-900">
                        {isZh ? 'AQ-10 自闭症筛查结果' : 'AQ-10 Autism Screening Results'}
                    </h3>
                    <p className="text-gray-600 text-sm">
                        {isZh ? '自闭症商数快速筛查量表' : 'Autism Spectrum Quotient 10-item screening'}
                    </p>
                </div>
            </div>

            {/* Score and Risk Level */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '筛查得分' : 'Screening Score'}
                        </span>
                        <TrendingUp className="w-4 h-4 text-teal-500" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900">
                        {scores.score}<span className="text-xl text-gray-500">/{scores.max}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3 mt-3">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${percentage}%` }}
                            transition={{ delay: 0.3, duration: 1.5 }}
                            className={`${riskInfo.progressColor} h-3 rounded-full`}
                        />
                    </div>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className={`${riskInfo.bgColor} ${riskInfo.borderColor} rounded-lg p-4 border shadow-sm`}
                >
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-600">
                            {isZh ? '风险等级' : 'Risk Level'}
                        </span>
                        <RiskIcon className={`w-5 h-5 ${riskInfo.color}`} />
                    </div>
                    <div className={`text-lg font-bold ${riskInfo.color}`}>
                        {riskInfo.label}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                        {riskInfo.description}
                    </div>
                </motion.div>
            </div>

            {/* Score Range Visualization */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm mb-6"
            >
                <h4 className="text-sm font-medium text-gray-600 mb-3">
                    {isZh ? '分数范围解释' : 'Score Range Interpretation'}
                </h4>
                <div className="space-y-2">
                    {scoreRanges.map((range, index) => (
                        <div key={index} className="flex items-center gap-3">
                            <div className="flex items-center gap-2 w-20">
                                <span className="text-sm font-medium text-gray-700">
                                    {range.min}-{range.max}
                                </span>
                            </div>
                            <div className="flex-1 bg-gray-200 rounded-full h-2 relative">
                                <div 
                                    className={`${range.color} h-2 rounded-full`}
                                    style={{ width: `${((range.max - range.min + 1) / 11) * 100}%` }}
                                />
                                {scores.score >= range.min && scores.score <= range.max && (
                                    <motion.div
                                        initial={{ scale: 0 }}
                                        animate={{ scale: 1 }}
                                        transition={{ delay: 0.6 + index * 0.1 }}
                                        className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white border-2 border-gray-800 rounded-full shadow-lg"
                                        style={{ 
                                            left: `${((scores.score - range.min) / (range.max - range.min)) * ((range.max - range.min + 1) / 11) * 100}%`,
                                            transform: 'translateX(-50%) translateY(-50%)'
                                        }}
                                    />
                                )}
                            </div>
                            <div className="w-16 text-xs text-gray-600">
                                {range.label}
                            </div>
                        </div>
                    ))}
                </div>
            </motion.div>

            {/* Circular Progress */}
            <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8 }}
                className="flex justify-center"
            >
                <div className="relative w-32 h-32">
                    <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
                        <circle
                            cx="50"
                            cy="50"
                            r="40"
                            stroke="currentColor"
                            strokeWidth="8"
                            fill="transparent"
                            className="text-gray-200"
                        />
                        <motion.circle
                            cx="50"
                            cy="50"
                            r="40"
                            stroke="currentColor"
                            strokeWidth="8"
                            fill="transparent"
                            strokeLinecap="round"
                            className={riskInfo.color}
                            initial={{ strokeDasharray: "0 251.2" }}
                            animate={{ strokeDasharray: `${(percentage / 100) * 251.2} 251.2` }}
                            transition={{ delay: 1, duration: 2 }}
                        />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                            <div className={`text-2xl font-bold ${riskInfo.color}`}>
                                {Math.round(percentage)}%
                            </div>
                            <div className="text-xs text-gray-600">
                                {isZh ? '筛查阳性' : 'Positive'}
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>

            {/* Clinical Threshold Indicator */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2 }}
                className="text-center mt-4"
            >
                <div className={`inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium ${
                    scores.significant 
                        ? 'bg-red-100 text-red-700 border border-red-200' 
                        : 'bg-green-100 text-green-700 border border-green-200'
                }`}>
                    {scores.significant ? (
                        <AlertTriangle className="w-4 h-4" />
                    ) : (
                        <CheckCircle className="w-4 h-4" />
                    )}
                    {scores.significant 
                        ? (isZh ? '达到筛查阈值 (≥6分)' : 'Meets screening threshold (≥6 points)')
                        : (isZh ? '未达到筛查阈值 (<6分)' : 'Below screening threshold (<6 points)')
                    }
                </div>
                <p className="text-sm text-gray-600 mt-3">
                    {isZh 
                        ? '解锁完整报告查看详细分析和专业建议' 
                        : 'Unlock the full report for detailed analysis and professional recommendations'
                    }
                </p>
            </motion.div>
        </div>
    );
}
