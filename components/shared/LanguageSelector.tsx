'use client'

import { useState, useRef, useEffect } from 'react';
import { useRouter, usePathname, locales, fullLanguageNames, Pathnames } from '@/i18n/routing';
import { useLocale } from 'next-intl';

type Locale = keyof typeof fullLanguageNames;

const flagEmojis: Record<string, string> = {
    'en': '🇺🇸',
    'zh': '🇨🇳',
    'tw': '🇨🇳',
    'de': '🇩🇪',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'fr': '🇫🇷',
    'pt': '🇵🇹',
    'es': '🇪🇸',
    'vi': '🇻🇳',
    'ar': '🇸🇦',
    'nl': '🇳🇱',
    'pl': '🇵🇱',
    'ru': '🇷🇺',
};

interface LanguageSelectorProps {
    onLanguageChange?: (lang: string) => void;
}

export default function LanguageSelector({ onLanguageChange }: LanguageSelectorProps) {
    const [isOpen, setIsOpen] = useState(false);
    const currentLocale = useLocale() as Locale;
    const pathname = usePathname();
    const router = useRouter();
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleLanguageChange = async (newLocale: string) => {
        onLanguageChange?.(newLocale);
        try {
            await router.replace(pathname as any, { locale: newLocale });
            router.refresh();
            setIsOpen(false);
        } catch (error) {
            console.error('Error changing language:', error);
        }
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center justify-center px-2 py-1.5 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                aria-label="Select language"
            >
                <span className="text-lg flex-shrink-0">{flagEmojis[currentLocale]}</span>
                <span className="hidden sm:inline ml-2 text-sm font-medium text-gray-700">
                    {fullLanguageNames[currentLocale]}
                </span>
                <svg
                    className="hidden sm:inline w-4 h-4 ml-1 text-gray-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </button>
            {isOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 transform origin-top-right transition-all duration-200">
                    {locales.map((locale) => (
                        <button
                            key={locale}
                            onClick={() => handleLanguageChange(locale)}
                            className={`flex items-center w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors duration-150
                                ${currentLocale === locale ? 'text-blue-600 font-medium bg-blue-50' : 'text-gray-700'}`}
                        >
                            <span className="text-lg mr-3">{flagEmojis[locale]}</span>
                            <span>{fullLanguageNames[locale]}</span>
                            {currentLocale === locale && (
                                <svg className="ml-auto h-4 w-4 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                            )}
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
}