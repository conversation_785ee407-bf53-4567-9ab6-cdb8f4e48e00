'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { Brain, CheckCircle, Zap, AlertCircle, Loader2 } from 'lucide-react';

interface StreamingReportGeneratorProps {
    endpoint: string;
    requestData: any;
    onComplete?: (content: string) => void;
    onError?: (error: string) => void;
    className?: string;
    locale?: string;
}

interface StreamEvent {
    type: 'status' | 'content' | 'complete' | 'error';
    message?: string;
    chunk?: string;
    content?: string;
    progress?: number;
}

export default function StreamingReportGenerator({
    endpoint,
    requestData,
    onComplete,
    onError,
    className = '',
    locale = 'en'
}: StreamingReportGeneratorProps) {
    const [status, setStatus] = useState<string>('');
    const [streamedContent, setStreamedContent] = useState<string>('');
    const [progress, setProgress] = useState<number>(0);
    const [isComplete, setIsComplete] = useState<boolean>(false);
    const [error, setError] = useState<string>('');
    const [isConnecting, setIsConnecting] = useState<boolean>(true);
    
    const eventSourceRef = useRef<EventSource | null>(null);
    const abortControllerRef = useRef<AbortController | null>(null);

    const isZh = locale === 'zh';

    useEffect(() => {
        startStreaming();
        
        return () => {
            cleanup();
        };
    }, [endpoint, requestData]);

    const cleanup = () => {
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = null;
        }
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
            abortControllerRef.current = null;
        }
    };

    const startStreaming = async () => {
        try {
            setIsConnecting(true);
            setError('');
            setStreamedContent('');
            setProgress(0);
            setIsComplete(false);

            // Create abort controller for cleanup
            abortControllerRef.current = new AbortController();

            // First, make the POST request to start the stream
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
                signal: abortControllerRef.current.signal
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is a stream
            if (!response.body) {
                throw new Error('No response body');
            }

            setIsConnecting(false);

            // Read the stream
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data: StreamEvent = JSON.parse(line.slice(6));
                                handleStreamEvent(data);
                            } catch (parseError) {
                                console.error('Error parsing stream data:', parseError);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }

        } catch (error) {
            console.error('Streaming error:', error);
            setIsConnecting(false);
            
            if (error instanceof Error && error.name !== 'AbortError') {
                const errorMessage = isZh ? '生成报告时出错，请重试' : 'Error generating report, please try again';
                setError(errorMessage);
                onError?.(errorMessage);
            }
        }
    };

    const handleStreamEvent = (event: StreamEvent) => {
        switch (event.type) {
            case 'status':
                setStatus(event.message || '');
                break;
                
            case 'content':
                if (event.chunk) {
                    setStreamedContent(prev => prev + event.chunk);
                }
                if (event.progress !== undefined) {
                    setProgress(event.progress);
                }
                break;
                
            case 'complete':
                setIsComplete(true);
                setProgress(100);
                setStatus(isZh ? '报告生成完成' : 'Report generation complete');
                if (event.content) {
                    setStreamedContent(event.content);
                    onComplete?.(event.content);
                }
                break;
                
            case 'error':
                setError(event.message || (isZh ? '生成报告时出错' : 'Error generating report'));
                onError?.(event.message || 'Error generating report');
                break;
        }
    };

    const renderProgressIndicator = () => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 mb-6 border border-blue-200 dark:border-blue-800"
        >
            <div className="flex items-center gap-3 mb-4">
                {isConnecting ? (
                    <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
                ) : isComplete ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                    <Brain className="w-5 h-5 text-blue-600 animate-pulse" />
                )}
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {isZh ? '正在生成报告' : 'Generating Report'}
                </h3>
            </div>
            
            {status && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {status}
                </p>
            )}
            
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
                <motion.div
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                />
            </div>
            
            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>{Math.round(progress)}% {isZh ? '完成' : 'complete'}</span>
                {isComplete && (
                    <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-green-600 flex items-center gap-1"
                    >
                        <CheckCircle className="w-3 h-3" />
                        {isZh ? '完成' : 'Done'}
                    </motion.span>
                )}
            </div>
        </motion.div>
    );

    const renderError = () => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6 border border-red-200 dark:border-red-800"
        >
            <div className="flex items-center gap-3 mb-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
                    {isZh ? '生成失败' : 'Generation Failed'}
                </h3>
            </div>
            <p className="text-red-700 dark:text-red-300">{error}</p>
        </motion.div>
    );

    const renderStreamedContent = () => {
        if (!streamedContent) return null;

        return (
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="prose dark:prose-invert max-w-none"
            >
                <ReactMarkdown
                    rehypePlugins={[rehypeRaw, rehypeHighlight]}
                    remarkPlugins={[remarkGfm]}
                >
                    {streamedContent}
                </ReactMarkdown>
                
                {/* Typing cursor effect */}
                {!isComplete && (
                    <motion.span
                        animate={{ opacity: [1, 0] }}
                        transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
                        className="inline-block w-2 h-5 bg-blue-500 ml-1"
                    />
                )}
            </motion.div>
        );
    };

    return (
        <div className={`space-y-6 ${className}`}>
            <AnimatePresence mode="wait">
                {error ? (
                    renderError()
                ) : (
                    <>
                        {(!isComplete || isConnecting) && renderProgressIndicator()}
                        {renderStreamedContent()}
                    </>
                )}
            </AnimatePresence>
        </div>
    );
}
