'use client';

import React, { useState } from 'react';
import { Download, FileText, Loader2 } from 'lucide-react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

interface PdfDownloadButtonProps {
    targetElementId: string;
    filename?: string;
    locale: string;
    testType: string;
    disabled?: boolean;
    className?: string;
}

export default function PdfDownloadButton({
    targetElementId,
    filename,
    locale,
    testType,
    disabled = false,
    className = ''
}: PdfDownloadButtonProps) {
    const [isGenerating, setIsGenerating] = useState(false);
    const isZh = locale === 'zh';

    const getDefaultFilename = () => {
        const timestamp = new Date().toISOString().split('T')[0];
        const testName = testType.includes('adhd') 
            ? (isZh ? 'ADHD评估报告' : 'ADHD-Assessment-Report')
            : testType.includes('raadsr')
            ? (isZh ? 'RAADS-R评估报告' : 'RAADS-R-Assessment-Report')
            : (isZh ? 'AQ-10评估报告' : 'AQ-10-Assessment-Report');
        
        return `${testName}_${timestamp}.pdf`;
    };

    const generatePDF = async () => {
        if (disabled || isGenerating) return;

        try {
            setIsGenerating(true);

            // Find the target element
            const element = document.getElementById(targetElementId);
            if (!element) {
                throw new Error('Target element not found');
            }

            // Create a clone of the element to modify for PDF
            const clone = element.cloneNode(true) as HTMLElement;
            
            // Apply PDF-specific styles
            clone.style.width = '210mm'; // A4 width
            clone.style.maxWidth = '210mm';
            clone.style.backgroundColor = 'white';
            clone.style.color = 'black';
            clone.style.fontFamily = 'Arial, sans-serif';
            clone.style.fontSize = '12px';
            clone.style.lineHeight = '1.4';
            clone.style.padding = '20px';
            clone.style.boxSizing = 'border-box';

            // Remove interactive elements and blur effects
            const interactiveElements = clone.querySelectorAll('button, .blur-xl, .blur-lg, .blur-md, .backdrop-blur-lg, .backdrop-blur-md, .backdrop-blur-sm');
            interactiveElements.forEach(el => {
                if (el.tagName === 'BUTTON') {
                    el.remove();
                } else {
                    (el as HTMLElement).style.filter = 'none';
                    (el as HTMLElement).style.backdropFilter = 'none';
                }
            });

            // Remove overlay elements
            const overlays = clone.querySelectorAll('.absolute.inset-0, [class*="pointer-events-none"]');
            overlays.forEach(el => el.remove());

            // Temporarily add clone to document for rendering
            clone.style.position = 'absolute';
            clone.style.left = '-9999px';
            clone.style.top = '0';
            document.body.appendChild(clone);

            // Generate canvas from the clone
            const canvas = await html2canvas(clone, {
                scale: 2, // Higher quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 794, // A4 width in pixels at 96 DPI
                height: Math.max(1123, clone.scrollHeight * 2), // A4 height minimum
                scrollX: 0,
                scrollY: 0
            });

            // Remove clone from document
            document.body.removeChild(clone);

            // Create PDF
            const imgData = canvas.toDataURL('image/png');
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            const imgWidth = 210; // A4 width in mm
            const pageHeight = 297; // A4 height in mm
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            // Add first page
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // Add additional pages if needed
            while (heightLeft > 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // Download the PDF
            const finalFilename = filename || getDefaultFilename();
            pdf.save(finalFilename);

        } catch (error) {
            console.error('Error generating PDF:', error);
            alert(isZh ? 'PDF生成失败，请重试' : 'Failed to generate PDF, please try again');
        } finally {
            setIsGenerating(false);
        }
    };

    const buttonText = isZh ? '下载PDF报告' : 'Download PDF Report';
    const generatingText = isZh ? '生成中...' : 'Generating...';

    return (
        <button
            onClick={generatePDF}
            disabled={disabled || isGenerating}
            className={`
                inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
                ${disabled 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                }
                ${isGenerating ? 'opacity-75 cursor-wait' : ''}
                ${className}
            `}
            title={disabled ? (isZh ? '请先解锁报告' : 'Please unlock the report first') : buttonText}
        >
            {isGenerating ? (
                <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
                <Download className="w-4 h-4" />
            )}
            <span className="hidden sm:inline">
                {isGenerating ? generatingText : buttonText}
            </span>
            <FileText className="w-4 h-4 sm:hidden" />
        </button>
    );
}
