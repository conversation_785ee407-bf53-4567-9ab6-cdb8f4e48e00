'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { TestResult } from '@prisma/client';
import { Send, MessageCircle, Minimize2, ChevronLeft, ChevronRight, Bo<PERSON>, User, <PERSON>rk<PERSON> } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';

interface Message {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    isStreaming?: boolean;
    type?: 'normal' | 'subscription_limit';
    upgradeRequired?: boolean;
}

interface StreamingMessageProps {
    content: string;
    isComplete: boolean;
    className?: string;
}

// 流式消息展示组件
function StreamingMessage({ content, isComplete, className = "" }: StreamingMessageProps) {
    const [displayedContent, setDisplayedContent] = useState('');
    const [showCursor, setShowCursor] = useState(true);

    useEffect(() => {
        setDisplayedContent(content);
    }, [content]);

    useEffect(() => {
        if (isComplete) {
            setShowCursor(false);
        } else {
            const cursorInterval = setInterval(() => {
                setShowCursor(prev => !prev);
            }, 500);
            return () => clearInterval(cursorInterval);
        }
    }, [isComplete]);

    return (
        <div className={className}>
            <div className="text-sm text-gray-800 prose prose-sm max-w-none">
                <ReactMarkdown>{displayedContent}</ReactMarkdown>
                {!isComplete && showCursor && (
                    <span className="inline-block w-2 h-4 bg-blue-500 ml-1 animate-pulse"></span>
                )}
            </div>
        </div>
    );
}

// 订阅限制消息组件
interface SubscriptionLimitMessageProps {
    content: string;
    onUpgradeClick: () => void;
    locale: string;
}

function SubscriptionLimitMessage({ content, onUpgradeClick, locale }: SubscriptionLimitMessageProps) {
    const t = useTranslations('chatConsultation');

    return (
        <div className="space-y-3">
            <div className="text-sm text-gray-800 leading-relaxed">
                {content}
            </div>
            <div className="flex flex-col gap-2">
                <button
                    onClick={() => {
                        if (process.env.NODE_ENV === 'development') {
                            console.log('SubscriptionLimitMessage: upgrade button clicked');
                        }
                        onUpgradeClick();
                    }}
                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium px-4 py-2.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center gap-2 active:scale-95"
                >
                    <Sparkles className="w-4 h-4" />
                    {t('upgradeSubscription')}
                </button>
            </div>
        </div>
    );
}

interface ReportSidebarProps {
    testResult: TestResult;
    locale: string;
}

export default function ReportSidebar({ testResult, locale }: ReportSidebarProps) {
    const t = useTranslations('chatConsultation');
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [messages, setMessages] = useState<Message[]>([]);
    const [inputValue, setInputValue] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [hasStarted, setHasStarted] = useState(false);
    const [isLoadingHistory, setIsLoadingHistory] = useState(true);
    const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([]);
    const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);
    const [scrolled, setScrolled] = useState(false);
    const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);
    const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
    const [streamingContent, setStreamingContent] = useState('');
    const [isStreamComplete, setIsStreamComplete] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const messageRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
    const abortControllerRef = useRef<AbortController | null>(null);

    // 推荐问题区域引用
    const initialSuggestedQuestionsRef = useRef<HTMLDivElement>(null);
    const ongoingSuggestedQuestionsRef = useRef<HTMLDivElement>(null);

    // 默认推荐问题列表（作为备选）
    const getDefaultRecommendedQuestions = useCallback(() => {
        return [
            t('questions.meaning'),
            t('questions.nextSteps'),
            t('questions.professionalHelp')
        ];
    }, [t]);

    // 获取动态推荐问题
    const fetchSuggestedQuestions = useCallback(async () => {
        if (isLoadingQuestions) return;

        setIsLoadingQuestions(true);
        try {
            const response = await fetch('/api/chat-consultation/suggested-questions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    testResultId: testResult.id,
                    locale,
                    conversationHistory: messages
                }),
            });

            if (response.ok) {
                const data = await response.json();
                if (data.questions && data.questions.length > 0) {
                    setSuggestedQuestions(data.questions);
                } else {
                    // 如果没有生成问题，使用默认问题
                    setSuggestedQuestions(getDefaultRecommendedQuestions());
                }
            } else {
                // 如果API调用失败，使用默认问题
                setSuggestedQuestions(getDefaultRecommendedQuestions());
            }
        } catch (error) {
            console.error('Error fetching suggested questions:', error);
            // 如果出错，使用默认问题
            setSuggestedQuestions(getDefaultRecommendedQuestions());
        } finally {
            setIsLoadingQuestions(false);
        }
    }, [testResult.id, locale, messages, isLoadingQuestions, getDefaultRecommendedQuestions]);

    // 使用ref来避免useEffect依赖问题
    const fetchSuggestedQuestionsRef = useRef(fetchSuggestedQuestions);
    fetchSuggestedQuestionsRef.current = fetchSuggestedQuestions;

    // 防抖标记，避免频繁调用
    const lastFetchTime = useRef(0);
    const FETCH_DEBOUNCE_MS = 2000; // 2秒防抖

    // 加载聊天历史
    useEffect(() => {
        const loadChatHistory = async () => {
            try {
                const response = await fetch(`/api/chat-consultation/history?testResultId=${testResult.id}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.messages && data.messages.length > 0) {
                        setMessages(data.messages);
                        setHasStarted(true);
                    }
                }
            } catch (error) {
                console.error('Error loading chat history:', error);
            } finally {
                setIsLoadingHistory(false);
            }
        };

        loadChatHistory();
    }, [testResult.id]);

    // 获取当前显示的推荐问题
    const getRecommendedQuestions = () => {
        // 如果正在加载历史，不显示推荐问题
        if (isLoadingHistory) {
            return [];
        }
        // 如果正在加载，不显示任何问题
        if (isLoadingQuestions) {
            return [];
        }
        // 如果正在等待回答，不显示推荐问题
        if (isWaitingForResponse) {
            return [];
        }
        return suggestedQuestions.length > 0 ? suggestedQuestions : getDefaultRecommendedQuestions();
    };

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    const scrollToMessage = (messageId: string) => {
        const messageElement = messageRefs.current[messageId];
        if (messageElement) {
            messageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    };

    // 滚动到推荐问题区域
    const scrollToSuggestedQuestions = useCallback(() => {
        // 优先滚动到对话中的推荐问题（如果存在）
        if (hasStarted && ongoingSuggestedQuestionsRef.current) {
            ongoingSuggestedQuestionsRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        } else if (!hasStarted && initialSuggestedQuestionsRef.current) {
            // 如果对话未开始，滚动到初始推荐问题
            initialSuggestedQuestionsRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }
    }, [hasStarted]);

    // 智能滚动逻辑：用户消息滚动到底部
    useEffect(() => {
        if (messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            if (lastMessage.role === 'user') {
                // 用户消息：滚动到底部
                scrollToBottom();
            }
            // 移除AI回答完成后滚动到回答开始的逻辑
        }
    }, [messages]);

    // 流式内容更新时自动滚动
    useEffect(() => {
        if (streamingMessageId && streamingContent) {
            scrollToBottom();
        }
    }, [streamingContent, streamingMessageId]);

    // 监听推荐问题加载状态，当加载完成时滚动到推荐问题
    useEffect(() => {
        // 当推荐问题加载完成且有新问题时，滚动到推荐问题区域
        if (!isLoadingQuestions && suggestedQuestions.length > 0 && hasStarted) {
            // 延迟执行，确保DOM已更新
            setTimeout(() => {
                scrollToSuggestedQuestions();
            }, 300); // 给一些时间让DOM更新和动画完成
        }
    }, [isLoadingQuestions, suggestedQuestions.length, hasStarted, scrollToSuggestedQuestions]);

    // 在组件加载时获取动态推荐问题
    useEffect(() => {
        if (!hasStarted) {
            const now = Date.now();
            if (now - lastFetchTime.current > FETCH_DEBOUNCE_MS) {
                lastFetchTime.current = now;
                fetchSuggestedQuestionsRef.current();
            }
        }
    }, [testResult.id, locale, hasStarted]);

    // 在对话进行一段时间后更新推荐问题 - 使用防抖
    useEffect(() => {
        if (messages.length > 0 && messages.length % 4 === 0) {
            const now = Date.now();
            if (now - lastFetchTime.current > FETCH_DEBOUNCE_MS) {
                lastFetchTime.current = now;
                // 每4轮对话后更新一次推荐问题
                fetchSuggestedQuestionsRef.current();
            }
        }
    }, [messages.length]);

    // 在AI回答完成后更新推荐问题
    useEffect(() => {
        if (messages.length > 0 && isStreamComplete) {
            const lastMessage = messages[messages.length - 1];
            // 如果最后一条消息是AI回复，且对话已经进行了几轮，则更新推荐问题
            if (lastMessage.role === 'assistant' && messages.length >= 2) {
                const now = Date.now();
                if (now - lastFetchTime.current > FETCH_DEBOUNCE_MS) {
                    lastFetchTime.current = now;
                    // 延迟1秒再更新，让用户有时间阅读回复
                    setTimeout(() => {
                        fetchSuggestedQuestionsRef.current();
                    }, 1000);
                }
            }
        }
    }, [messages, isStreamComplete]);

    // 监听滚动状态以动态调整header高度
    useEffect(() => {
        const handleScroll = () => {
            setScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        handleScroll(); // 初始检查

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    // 处理流式响应
    const handleStreamResponse = async (response: Response, messageId: string, userMessage: string) => {
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('No readable stream');
        }

        const decoder = new TextDecoder();
        let accumulatedContent = '';

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));

                            if (data.error) {
                                throw new Error(data.error);
                            }

                            if (data.content) {
                                accumulatedContent += data.content;
                                setStreamingContent(accumulatedContent);
                                setIsStreamComplete(data.isComplete || false);

                                if (data.isComplete) {
                                    // 流式完成，更新消息
                                    setMessages(prev => prev.map(msg =>
                                        msg.id === messageId
                                            ? { ...msg, content: accumulatedContent, isStreaming: false }
                                            : msg
                                    ));
                                    setStreamingMessageId(null);
                                    setStreamingContent('');

                                    // 保存完整的对话到数据库 - 直接使用传入的用户消息
                                    saveCompletedConversation(accumulatedContent, userMessage);
                                }
                            }
                        } catch (parseError) {
                            console.error('Error parsing SSE data:', parseError);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    };

    // 保存完成的对话到数据库
    const saveCompletedConversation = async (assistantResponse: string, userMessage: string) => {
        console.log(`\n=== FRONTEND: ReportSidebar.saveCompletedConversation START ===`);
        console.log(`Timestamp: ${new Date().toISOString()}`);
        console.log(`Test Result ID: ${testResult.id}`);
        console.log(`User ID: ${testResult.userId || 'NO_USER_ID'}`);
        console.log(`Assistant response length: ${assistantResponse.length}`);
        console.log(`Current messages count: ${messages.length}`);
        console.log(`User message content: "${userMessage.substring(0, 100)}${userMessage.length > 100 ? '...' : ''}"`);

        try {
            console.log(`📤 Calling /api/chat-consultation/save-message...`);
            const response = await fetch('/api/chat-consultation/save-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    testResultId: testResult.id,
                    userMessage: userMessage,
                    assistantMessage: assistantResponse,
                    userId: testResult.userId
                })
            });

            if (response.ok) {
                console.log(`✅ Conversation saved successfully`);
            } else {
                console.log(`❌ Failed to save conversation: ${response.status} ${response.statusText}`);
            }
            console.log(`=== FRONTEND: ReportSidebar.saveCompletedConversation END ===\n`);
        } catch (error) {
            console.error('❌ Error saving conversation:', error);
            console.log(`=== FRONTEND: ReportSidebar.saveCompletedConversation ERROR END ===\n`);
        }
    };

    // 发送消息
    const sendMessage = async (content: string, retryCount = 0) => {
        if (!content.trim() || isLoading) return;

        console.log(`\n=== FRONTEND: ReportSidebar.sendMessage START ===`);
        console.log(`Timestamp: ${new Date().toISOString()}`);
        console.log(`Test Result ID: ${testResult.id}`);
        console.log(`Message: "${content.substring(0, 100)}${content.length > 100 ? '...' : ''}"`);
        console.log(`Current messages count: ${messages.length}`);
        console.log(`Has started conversation: ${hasStarted}`);
        console.log(`Is loading: ${isLoading}`);
        console.log(`Is waiting for response: ${isWaitingForResponse}`);
        console.log(`Retry count: ${retryCount}`);

        // 保存当前用户消息内容供后续使用
        const currentUserMessageContent = content.trim();

        // 添加全局错误处理边界
        try {

            // 如果有正在进行的流式响应，先中断它
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }

            const userMessage: Message = {
                id: Date.now().toString(),
                role: 'user',
                content: currentUserMessageContent,
                timestamp: new Date()
            };

            setMessages(prev => [...prev, userMessage]);
            setInputValue('');
            setHasStarted(true);
            setIsWaitingForResponse(true);

            // 创建新的 AbortController
            abortControllerRef.current = new AbortController();

            try {
                const assistantMessageId = (Date.now() + 1).toString();

                // 先添加流式消息占位符
                const streamingMessage: Message = {
                    id: assistantMessageId,
                    role: 'assistant',
                    content: '',
                    timestamp: new Date(),
                    isStreaming: true
                };

                setMessages(prev => [...prev, streamingMessage]);
                setStreamingMessageId(assistantMessageId);
                setStreamingContent('');
                setIsStreamComplete(false);

                // 不设置 isLoading，避免同时显示两个消息框

                // 设置请求超时
                const timeoutId = setTimeout(() => {
                    if (abortControllerRef.current) {
                        abortControllerRef.current.abort();
                    }
                }, 30000); // 30秒超时

                const response = await fetch('/api/chat-consultation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        testResultId: testResult.id,
                        message: currentUserMessageContent,
                        locale,
                        conversationHistory: messages,
                        stream: true
                    }),
                    signal: abortControllerRef.current.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    let errorText = '';
                    try {
                        errorText = await response.text();
                    } catch (textError) {
                        console.warn('Failed to read error response text:', textError);
                        errorText = 'Failed to read error response';
                    }

                    // 只在开发环境输出详细API错误信息
                    if (process.env.NODE_ENV === 'development') {
                        console.error('API Error:', {
                            status: response.status,
                            statusText: response.statusText,
                            errorText
                        });
                    } else {
                        // 生产环境只输出简化信息
                        console.warn(`API request failed: ${response.status} ${response.statusText}`);
                    }

                    // 处理订阅限制错误
                    if (response.status === 403) {
                        try {
                            const errorData = JSON.parse(errorText);
                            if (errorData.upgradeRequired) {
                                // 移除流式消息占位符
                                setMessages(prev => prev.filter(msg => !(msg.isStreaming && msg.id === assistantMessageId)));
                                setStreamingMessageId(null);

                                // 添加订阅限制消息
                                const limitMessage: Message = {
                                    id: (Date.now() + 2).toString(),
                                    role: 'assistant',
                                    content: errorData.error || t('subscriptionLimitReached'),
                                    timestamp: new Date(),
                                    type: 'subscription_limit',
                                    upgradeRequired: true
                                };

                                setMessages(prev => [...prev, limitMessage]);
                                setIsWaitingForResponse(false);
                                return;
                            }
                        } catch (parseError) {
                            // 如果解析失败，继续使用通用错误处理
                        }
                    }

                    // 创建详细的错误消息，但不抛出异常
                    const errorMessage = `Failed to get response: ${response.status} ${response.statusText}`;
                    throw new Error(errorMessage);
                }

                if (response.headers.get('content-type')?.includes('text/event-stream')) {
                    await handleStreamResponse(response, assistantMessageId, currentUserMessageContent);
                } else {
                    // 降级到非流式响应
                    const data = await response.json();
                    setMessages(prev => prev.map(msg =>
                        msg.id === assistantMessageId
                            ? { ...msg, content: data.response, isStreaming: false }
                            : msg
                    ));
                    setStreamingMessageId(null);

                    // 为非流式响应也保存对话
                    saveCompletedConversation(data.response, currentUserMessageContent);
                }

            } catch (error) {
                if (error instanceof Error && error.name === 'AbortError') {
                    console.log('Request was aborted');
                    return;
                }

                // 只在开发环境输出详细错误信息
                if (process.env.NODE_ENV === 'development') {
                    console.error('Error sending message:', error);
                } else {
                    // 生产环境只输出简化的错误信息
                    console.warn('Failed to send message to chat consultation API');
                }

                // 移除流式消息占位符
                setMessages(prev => prev.filter(msg => !(msg.isStreaming && msg.id === streamingMessageId)));
                setStreamingMessageId(null);

                // 如果是网络错误且重试次数小于2，则自动重试
                if (retryCount < 2 && error instanceof Error &&
                    (error.message.includes('Failed to fetch') ||
                        error.message.includes('NetworkError') ||
                        error.message.includes('TypeError'))) {
                    console.log(`Retrying request (attempt ${retryCount + 1})`);
                    setTimeout(() => {
                        sendMessage(content, retryCount + 1);
                    }, 1000 * (retryCount + 1)); // 递增延迟: 1s, 2s
                    return;
                }

                // 如果流式响应失败，显示加载状态，然后显示错误消息
                setIsLoading(true);

                setTimeout(() => {
                    let errorContent = t('errorMessage');

                    // 根据错误类型提供更具体的错误消息
                    if (error instanceof Error) {
                        if (error.message.includes('403')) {
                            errorContent = locale === 'zh'
                                ? '咨询次数已用完，请升级订阅以继续使用咨询服务。'
                                : 'Consultation limit reached. Please upgrade your subscription to continue using consultation services.';
                        } else if (error.message.includes('404')) {
                            errorContent = locale === 'zh'
                                ? '测试结果未找到，请刷新页面重试。'
                                : 'Test result not found. Please refresh the page and try again.';
                        } else if (error.message.includes('500')) {
                            errorContent = locale === 'zh'
                                ? '服务器暂时不可用，请稍后再试。'
                                : 'Server is temporarily unavailable. Please try again later.';
                        } else if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                            errorContent = locale === 'zh'
                                ? '网络连接失败，请检查网络连接后重试。'
                                : 'Network connection failed. Please check your connection and try again.';
                        }
                    }

                    const errorMessage: Message = {
                        id: (Date.now() + 2).toString(),
                        role: 'assistant',
                        content: errorContent,
                        timestamp: new Date()
                    };

                    setMessages(prev => [...prev, errorMessage]);
                    setIsLoading(false);
                }, 500); // 短暂显示加载状态

            } finally {
                setIsWaitingForResponse(false);
                abortControllerRef.current = null;
            }

        } catch (globalError) {
            // 全局错误边界 - 捕获任何未处理的错误
            if (process.env.NODE_ENV === 'development') {
                console.error('Unexpected error in sendMessage:', globalError);
            } else {
                console.warn('An unexpected error occurred while sending message');
            }

            // 确保UI状态正确重置
            setIsLoading(false);
            setIsWaitingForResponse(false);
            setStreamingMessageId(null);

            // 显示通用错误消息
            const errorMessage: Message = {
                id: Date.now().toString(),
                role: 'assistant',
                content: locale === 'zh'
                    ? '发生了意外错误，请稍后重试。'
                    : 'An unexpected error occurred. Please try again later.',
                timestamp: new Date()
            };
            setMessages(prev => [...prev, errorMessage]);
        }
    };

    // 处理推荐问题点击
    const handleQuestionClick = (question: string) => {
        sendMessage(question);
    };

    // 处理升级按钮点击
    const handleUpgradeClick = async () => {
        if (process.env.NODE_ENV === 'development') {
            console.log('ReportSidebar: handleUpgradeClick called');
        }

        // 首先尝试触发全局事件来打开现有的解锁浮层
        let eventHandled = false;
        const handleEvent = () => { eventHandled = true; };

        window.addEventListener('openUnlockModalHandled', handleEvent, { once: true });
        window.dispatchEvent(new CustomEvent('openUnlockModal'));

        // 给事件处理一些时间
        await new Promise(resolve => setTimeout(resolve, 100));
        window.removeEventListener('openUnlockModalHandled', handleEvent);

        // 如果事件没有被处理，说明没有LockedReportUnlock组件在页面上
        if (!eventHandled) {
            if (process.env.NODE_ENV === 'development') {
                console.log('No unlock modal listener found, redirecting to subscription');
            }

            try {
                // 尝试直接创建Pro订阅（默认选择）
                const response = await fetch('/api/subscription/create-checkout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        planId: 'pro',
                        billingPeriod: 'monthly'
                    }),
                });

                const data = await response.json();

                if (response.ok && data.url) {
                    // 重定向到Stripe结账页面
                    window.location.href = data.url;
                } else if (response.status === 401) {
                    // 用户未登录，重定向到登录页面
                    const currentPath = encodeURIComponent(window.location.pathname + window.location.search);
                    window.location.href = `/${locale === 'en' ? '' : locale + '/'}auth/signin?callbackUrl=${currentPath}`;
                } else {
                    throw new Error(data.error || 'Failed to create checkout session');
                }
            } catch (error) {
                console.error('Error creating subscription checkout:', error);
                // 作为最后的备用方案，重定向到首页
                window.location.href = `/${locale === 'en' ? '' : locale + '/'}`;
            }
        }
    };

    // 处理键盘事件
    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage(inputValue);
        }
    };

    // 组件卸载时清理
    useEffect(() => {
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, []);

    if (isCollapsed) {
        return (
            <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-45 hidden xl:block">
                <button
                    onClick={() => setIsCollapsed(false)}
                    className="bg-blue-600 text-white p-3 rounded-l-xl shadow-lg hover:bg-blue-700 transition-colors"
                    title={t('buttons.expand')}
                >
                    <ChevronLeft className="w-5 h-5" />
                </button>
            </div>
        );
    }

    return (
        <div
            className="fixed right-0 top-0 h-screen w-96 bg-white shadow-2xl border-l border-gray-200 z-45 hidden xl:flex flex-col"
            style={{
                paddingTop: scrolled ? '64px' : '72px' // 动态适应header高度
            }}
        >
            {/* 头部 - 优化设计 */}
            <div className="flex items-center justify-between p-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-blue-100">
                <div className="flex items-center gap-4">
                    {/* 优化的图标设计 */}
                    <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
                            <MessageCircle className="w-5 h-5 text-white" />
                        </div>
                        {/* 装饰性星星图标 */}
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center shadow-sm">
                            <Sparkles className="w-2.5 h-2.5 text-white" />
                        </div>
                    </div>

                    {/* 标题和副标题 */}
                    <div className="flex-1 min-w-0">
                        <h3 className="font-bold text-gray-900 text-base leading-tight">
                            {t('title')}
                        </h3>
                        <p className="text-xs text-gray-600 font-medium mt-0.5 leading-relaxed">
                            {t('subtitle')}
                        </p>
                    </div>
                </div>

                {/* 最小化按钮 */}
                <button
                    onClick={() => setIsCollapsed(true)}
                    className="p-2 rounded-lg hover:bg-white/50 transition-colors flex-shrink-0"
                    title={t('buttons.minimize')}
                >
                    <ChevronRight className="w-4 h-4 text-gray-500" />
                </button>
            </div>

            {/* 消息区域 */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {!hasStarted && (
                    <div className="space-y-4">
                        {/* 欢迎消息 */}
                        <div className="flex items-start gap-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                                <Bot className="w-4 h-4 text-white" />
                            </div>
                            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl rounded-tl-sm p-3 flex-1 border border-blue-100">
                                <p className="text-sm text-gray-800 leading-relaxed">
                                    {t('welcome')}
                                </p>
                            </div>
                        </div>

                        {/* 推荐问题 */}
                        {!isWaitingForResponse && (
                            <div className="space-y-1.5" ref={initialSuggestedQuestionsRef}>
                                <div className="flex items-center justify-between px-2">
                                    <p className="text-xs text-gray-400 font-medium italic">
                                        {t('suggestedQuestions')}
                                    </p>
                                    {isLoadingQuestions && (
                                        <div className="flex items-center gap-1">
                                            <div className="w-1 h-1 bg-gray-300 rounded-full animate-bounce"></div>
                                            <div className="w-1 h-1 bg-gray-300 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                            <div className="w-1 h-1 bg-gray-300 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                        </div>
                                    )}
                                </div>
                                {isLoadingQuestions ? (
                                    <div className="flex items-center justify-center py-6">
                                        <div className="flex items-center gap-2 text-gray-400">
                                            <div className="w-3 h-3 border-2 border-gray-200 border-t-gray-400 rounded-full animate-spin"></div>
                                            <span className="text-xs italic">{t('loadingQuestions')}</span>
                                        </div>
                                    </div>
                                ) : (
                                    getRecommendedQuestions().map((question, index) => (
                                        <button
                                            key={`${question}-${index}`}
                                            onClick={() => handleQuestionClick(question)}
                                            className="w-full text-left px-3 py-2.5 bg-gradient-to-r from-blue-50 to-blue-100/50 hover:from-blue-100 hover:to-blue-200/70 rounded-xl text-sm text-blue-700 font-medium transition-all duration-200 border border-blue-200/50 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-0.5 leading-relaxed cursor-pointer"
                                        >
                                            <span className="flex items-center gap-2">
                                                <span className="w-1.5 h-1.5 bg-blue-400 rounded-full opacity-60"></span>
                                                {question}
                                            </span>
                                        </button>
                                    ))
                                )}
                            </div>
                        )}
                    </div>
                )}

                {/* 对话消息 */}
                {messages.map((message) => (
                    <div
                        key={message.id}
                        className="flex items-start gap-3"
                        ref={(el) => {
                            messageRefs.current[message.id] = el;
                        }}
                    >
                        {message.role === 'assistant' ? (
                            <>
                                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                                    <Bot className="w-4 h-4 text-white" />
                                </div>
                                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl rounded-tl-sm p-3 max-w-[280px] border border-blue-100">
                                    {message.isStreaming && message.id === streamingMessageId ? (
                                        <StreamingMessage
                                            content={streamingContent}
                                            isComplete={isStreamComplete}
                                        />
                                    ) : message.type === 'subscription_limit' ? (
                                        <SubscriptionLimitMessage
                                            content={message.content}
                                            onUpgradeClick={handleUpgradeClick}
                                            locale={locale}
                                        />
                                    ) : (
                                        <div className="text-sm text-gray-800 prose prose-sm max-w-none">
                                            <ReactMarkdown>{message.content}</ReactMarkdown>
                                        </div>
                                    )}
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                                    <User className="w-4 h-4 text-white" />
                                </div>
                                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl rounded-tl-sm p-3 max-w-[280px] border border-gray-200">
                                    <p className="text-sm text-gray-800">{message.content}</p>
                                </div>
                            </>
                        )}
                    </div>
                ))}

                {/* 在对话过程中显示更新的推荐问题 */}
                {hasStarted && messages.length > 0 && !isWaitingForResponse && (
                    <div className="space-y-1.5 pt-3 border-t border-gray-100" ref={ongoingSuggestedQuestionsRef}>
                        <div className="flex items-center justify-between px-2">
                            <p className="text-xs text-gray-400 font-medium italic">
                                {t('suggestedQuestions')}
                            </p>
                            {isLoadingQuestions && (
                                <div className="flex items-center gap-1">
                                    <div className="w-1 h-1 bg-gray-300 rounded-full animate-bounce"></div>
                                    <div className="w-1 h-1 bg-gray-300 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-1 h-1 bg-gray-300 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                            )}
                        </div>
                        {isLoadingQuestions ? (
                            <div className="flex items-center justify-center py-4">
                                <div className="flex items-center gap-2 text-gray-400">
                                    <div className="w-3 h-3 border-2 border-gray-200 border-t-gray-400 rounded-full animate-spin"></div>
                                    <span className="text-xs italic">{t('updatingQuestions')}</span>
                                </div>
                            </div>
                        ) : (
                            getRecommendedQuestions().map((question, index) => (
                                <button
                                    key={`${question}-${index}-ongoing`}
                                    onClick={() => handleQuestionClick(question)}
                                    className="w-full text-left px-3 py-2.5 bg-gradient-to-r from-blue-50 to-blue-100/50 hover:from-blue-100 hover:to-blue-200/70 rounded-xl text-sm text-blue-700 font-medium transition-all duration-200 border border-blue-200/50 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-0.5 leading-relaxed cursor-pointer"
                                >
                                    <span className="flex items-center gap-2">
                                        <span className="w-1.5 h-1.5 bg-blue-400 rounded-full opacity-60"></span>
                                        {question}
                                    </span>
                                </button>
                            ))
                        )}
                    </div>
                )}

                {/* 加载状态 - 只在没有流式消息时显示 */}
                {isLoading && !streamingMessageId && (
                    <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                            <Bot className="w-4 h-4 text-white" />
                        </div>
                        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl rounded-tl-sm p-3 border border-blue-100">
                            <div className="flex items-center gap-2">
                                <div className="flex space-x-1">
                                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                                <span className="text-xs text-gray-500">
                                    {t('thinking')}
                                </span>
                            </div>
                        </div>
                    </div>
                )}

                <div ref={messagesEndRef} />
            </div>

            {/* 输入区域 */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
                <div className="flex items-center gap-2">
                    <input
                        ref={inputRef}
                        type="text"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder={t('inputPlaceholder')}
                        className="flex-1 px-3 py-2.5 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white shadow-sm"
                        disabled={isLoading}
                    />
                    <button
                        onClick={() => sendMessage(inputValue)}
                        disabled={!inputValue.trim() || isLoading}
                        className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl flex items-center justify-center hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
                    >
                        <Send className="w-4 h-4" />
                    </button>
                </div>
            </div>


        </div>
    );
} 