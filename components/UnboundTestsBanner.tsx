'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Check, X, Calendar, BarChart2, Link2, RefreshCw, ChevronDown, ChevronUp } from 'lucide-react';
import { formatDate } from '@/utils/formatters';

interface UnboundTest {
  id: string;
  testType: string;
  email: string;
  createdAt: Date;
  scores?: any;
  metadata?: any;
  displayName: string;
  scoreSummary: string;
}

interface UnboundTestsBannerProps {
  locale: string;
}

export default function UnboundTestsBanner({ locale }: UnboundTestsBannerProps) {
  const t = useTranslations('unboundTestsBanner');
  const [unboundTests, setUnboundTests] = useState<UnboundTest[]>([]);
  const [selectedTests, setSelectedTests] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [isBinding, setIsBinding] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [showDetails, setShowDetails] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // 获取未绑定的测试
  const fetchUnboundTests = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch('/api/user/unbound-tests');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch unbound tests');
      }

      if (data.success && data.tests.length > 0) {
        setUnboundTests(data.tests);
        // 默认选中所有测试
        setSelectedTests(new Set(data.tests.map((test: UnboundTest) => test.id)));
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    } catch (err) {
      console.error('Error fetching unbound tests:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tests');
      setIsVisible(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUnboundTests();
  }, []);

  // 处理测试选择
  const handleTestToggle = (testId: string) => {
    const newSelected = new Set(selectedTests);
    if (newSelected.has(testId)) {
      newSelected.delete(testId);
    } else {
      newSelected.add(testId);
    }
    setSelectedTests(newSelected);
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedTests.size === unboundTests.length) {
      setSelectedTests(new Set());
    } else {
      setSelectedTests(new Set(unboundTests.map(test => test.id)));
    }
  };

  // 绑定选中的测试
  const handleBindSelected = async () => {
    if (selectedTests.size === 0) return;

    setIsBinding(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch('/api/user/bind-tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testIds: Array.from(selectedTests),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to bind tests');
      }

      if (data.success) {
        setSuccessMessage(data.message);
        // 移除已绑定的测试
        const remainingTests = unboundTests.filter(test => !selectedTests.has(test.id));
        setUnboundTests(remainingTests);
        setSelectedTests(new Set());
        
        // 如果没有剩余测试，隐藏横幅
        if (remainingTests.length === 0) {
          setTimeout(() => setIsVisible(false), 2000);
        }
      }
    } catch (err) {
      console.error('Error binding tests:', err);
      setError(err instanceof Error ? err.message : 'Failed to bind tests');
    } finally {
      setIsBinding(false);
    }
  };

  // 关闭横幅
  const handleDismiss = () => {
    setIsVisible(false);
  };

  // 如果正在加载或没有测试或已隐藏，不显示横幅
  if (isLoading || !isVisible || unboundTests.length === 0) {
    return null;
  }

  return (
    <div className="bg-blue-50 border-l-4 border-blue-400 p-3 sm:p-4 md:p-6 mb-4 md:mb-6 rounded-lg shadow-sm relative">
      {/* 关闭按钮 - 绝对定位到右上角 */}
      <button
        onClick={handleDismiss}
        className="absolute top-2 right-2 sm:top-3 sm:right-3 md:top-4 md:right-4 text-blue-400 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-md p-1.5 transition-colors z-10 bg-white/80 hover:bg-white shadow-sm"
        aria-label="Close banner"
      >
        <X className="h-4 w-4 sm:h-5 sm:w-5" />
      </button>

      {/* 主要内容区域 - 为关闭按钮预留空间 */}
      <div className="pr-8 sm:pr-10 md:pr-12">
        {/* 头部信息 - 移动端优化布局 */}
        <div className="flex items-start gap-2 sm:gap-3">
          <Link2 className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1 min-w-0">
            <h3 className="text-base sm:text-lg font-medium text-blue-900 leading-tight">
              {t('title', { count: unboundTests.length })}
            </h3>
            <p className="mt-1 text-xs sm:text-sm text-blue-700 leading-relaxed">
              {t('description')}
            </p>
          </div>
        </div>

        {/* 错误和成功消息 - 移动端优化 */}
        {error && (
          <div className="mt-3 p-2 sm:p-3 bg-red-100 border border-red-200 rounded-md text-xs sm:text-sm text-red-700">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="mt-3 p-2 sm:p-3 bg-green-100 border border-green-200 rounded-md text-xs sm:text-sm text-green-700 flex items-center gap-2">
            <Check className="h-4 w-4 flex-shrink-0" />
            <span className="flex-1">{successMessage}</span>
          </div>
        )}

        {/* 测试详情列表 - 移动端优化布局 */}
        {showDetails && (
          <div className="mt-4 space-y-3">
            {/* 选择控件 - 移动端堆叠布局 */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
              <button
                onClick={handleSelectAll}
                className="text-blue-600 hover:text-blue-800 font-medium text-sm text-left sm:text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded px-1 py-0.5"
              >
                {selectedTests.size === unboundTests.length ? t('deselectAll') : t('selectAll')}
              </button>
              <span className="text-gray-600 text-xs sm:text-sm">
                {t('selectedCount', { selected: selectedTests.size, total: unboundTests.length })}
              </span>
            </div>

            {/* 测试列表 - 移动端优化卡片布局 */}
            <div className="space-y-2 max-h-64 sm:max-h-80 overflow-y-auto">
              {unboundTests.map((test) => (
                <div
                  key={test.id}
                  className={`p-3 sm:p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                    selectedTests.has(test.id)
                      ? 'bg-blue-100 border-blue-300 shadow-sm'
                      : 'bg-white border-gray-200 hover:border-blue-200 hover:shadow-sm'
                  }`}
                  onClick={() => handleTestToggle(test.id)}
                >
                  <div className="flex items-start gap-3">
                    {/* 复选框 */}
                    <input
                      type="checkbox"
                      checked={selectedTests.has(test.id)}
                      onChange={() => handleTestToggle(test.id)}
                      className="mt-0.5 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
                      onClick={(e) => e.stopPropagation()}
                    />
                    
                    {/* 测试信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm sm:text-base font-medium text-gray-900 leading-tight">
                        {test.displayName}
                      </div>
                      
                      {/* 元数据信息 - 移动端优化布局 */}
                      <div className="flex flex-col sm:flex-row sm:items-center mt-1 sm:mt-2 gap-1 sm:gap-4">
                        <span className="flex items-center text-xs text-gray-600">
                          <Calendar className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span className="truncate">{formatDate(new Date(test.createdAt), locale)}</span>
                        </span>
                        <span className="flex items-center text-xs text-gray-600">
                          <BarChart2 className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span className="truncate">{test.scoreSummary}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 - 移动端优化布局 */}
        <div className="mt-4 flex flex-col sm:flex-row gap-2 sm:gap-3">
          {/* 显示详情按钮 */}
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="inline-flex items-center justify-center sm:justify-start px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors"
          >
            {showDetails ? (
              <>
                <ChevronUp className="h-4 w-4 mr-2" />
                {t('hideDetails')}
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-2" />
                {t('showDetails')}
              </>
            )}
          </button>

          {/* 操作按钮组 - 移动端堆叠 */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 sm:flex-1">
            {showDetails && (
              <button
                onClick={handleBindSelected}
                disabled={selectedTests.size === 0 || isBinding}
                className={`inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${
                  selectedTests.size === 0 || isBinding
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed focus:ring-gray-400'
                    : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md'
                }`}
              >
                {isBinding ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    {t('binding')}
                  </>
                ) : (
                  <>
                    <Link2 className="h-4 w-4 mr-2" />
                    {t('bindSelected', { count: selectedTests.size })}
                  </>
                )}
              </button>
            )}

            <button
              onClick={fetchUnboundTests}
              className="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-1 transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('refresh')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 