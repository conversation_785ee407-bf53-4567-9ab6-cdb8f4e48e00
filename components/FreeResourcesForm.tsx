'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { notifyFeishu } from '@/utils/notifyFeishu';
import { logEvent } from '@/utils/gaLog';
import { createPortal } from 'react-dom';

interface FreeResourcesFormProps {
  locale: string;
}

export default function FreeResourcesForm({ locale }: FreeResourcesFormProps) {
  const t = useTranslations('pages.home.content.seo.headings');
  const rt = useTranslations('pages.home.content.resources');
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 组件挂载后设置mounted为true，用于Portal渲染
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // 控制页面滚动
  useEffect(() => {
    if (showSuccess) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [showSuccess]);

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email) {
      setError(rt('emailRequired'));
      return;
    }

    if (!validateEmail(email)) {
      setError(rt('emailInvalid'));
      return;
    }

    setLoading(true);

    try {
      // Prepare user data
      const userData = {
        email,
        name: name.trim() || 'Not provided',
        locale,
        timestamp: new Date().toISOString(),
        path: window.location.pathname,
        referrer: document.referrer || 'Direct',
      };

      // Send notification to Feishu
      const message = `
        📬 Free Resources Request
        Email: ${userData.email}
        Name: ${userData.name}
        Locale: ${userData.locale}
        Time: ${userData.timestamp}
        Path: ${userData.path}
        Referrer: ${userData.referrer}
      `.trim();

      await notifyFeishu.keyNotify(message);

      // Track the action directly using logEvent
      logEvent(
        'subscribe', 
        'newsletter', 
        'Free Resources Form', 
        1
      );

      // Show success message
      setShowSuccess(true);
      setEmail('');
      setName('');
    } catch (err) {
      console.error('Error submitting form:', err);
      setError(rt('submitError'));
    } finally {
      setLoading(false);
    }
  };

  const closeSuccessModal = () => {
    setShowSuccess(false);
  };

  // 成功弹窗内容
  const SuccessModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4" 
         style={{position: 'fixed', top: 0, left: 0, right: 0, bottom: 0}}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative">
        <button 
          onClick={closeSuccessModal} 
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 focus:outline-none"
          aria-label={rt('close')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <h3 className="text-xl font-medium text-gray-900 mb-4">
            {rt('successTitle')}
          </h3>
          
          <p className="text-gray-600 mb-6">
            {rt('successMessage')}
          </p>
          
          <button
            onClick={closeSuccessModal}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50"
          >
            {rt('close')}
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      <form onSubmit={handleSubmit} className="w-full max-w-md mx-auto space-y-4">
        <div>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full px-4 py-3 rounded-md border-2 border-blue-100 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-gray-800"
            placeholder={rt('namePlaceholder')}
          />
        </div>
        
        <div>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 rounded-md border-2 border-blue-100 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-gray-800"
            placeholder={t('emailPlaceholder')}
            required
          />
        </div>
        
        {error && (
          <div className="text-red-500 text-sm font-medium bg-red-50 p-2 rounded-md">
            {error}
          </div>
        )}
        
        <button
          type="submit"
          disabled={loading}
          className="w-full bg-white hover:bg-blue-50 text-blue-600 font-semibold py-3 px-6 rounded-md transition-colors border-2 border-white hover:border-blue-200 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50 disabled:opacity-70 disabled:cursor-not-allowed"
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {rt('processing')}
            </span>
          ) : (
            t('getFreeResourcesBtn')
          )}
        </button>
      </form>

      {/* 使用Portal将成功弹窗渲染到body中 */}
      {mounted && showSuccess && createPortal(<SuccessModal />, document.body)}
    </div>
  );
} 