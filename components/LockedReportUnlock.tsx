"use client";

import { useState, useEffect, useRef } from 'react';
import { Lock, Share2, Facebook, Twitter, Linkedin, Eye, CheckCircle, CreditCard, Star, X, Sparkles, Gift, Crown, Zap, AlertCircle } from 'lucide-react';
import { useReferralStats } from '@/hooks/useReferralTracking';
import { generateShareUrl } from '@/utils/referralUtils';
import { getPricingConfig, getDefaultSingleReportPrice, PricingPlan, getLocalizedPlan } from '@/utils/pricingConfig';
import PaymentModal from '@/components/PaymentModal';
import { motion, AnimatePresence } from 'framer-motion';

// 订阅信息接口
interface SubscriptionInfo {
    type: string | null;
    status: string | null;
    currentPeriodEnd: Date | null;
    reportsRemaining: number;
    consultationsRemaining: number;
    hasHistoryAccess: boolean;
    hasVipFeatures: boolean;
    isUnlimited: boolean;
}

interface LockedReportUnlockProps {
    id: string;
    locale: string;
    type: 'adhd' | 'autism';
    email?: string;
    userId?: string;
    onUnlock: () => void;
    translations: {
        reportLocked: string;
        reportLockedDescription: string;
        unlockReport: string;
        shareReport: string;
        shareToUnlock: string;
        shareReportDescription: string;
        payToUnlock: string;
        comingSoon: string;
        linkCopied?: string;
        shareUnlockDivider?: string;
        shareHelpText?: string;
        payToUnlockTab?: string;
        shareToUnlockTab?: string;
        shareToUnlockFull?: string;
        free?: string;
        share?: string;
        payment?: {
            title: string;
            description: string;
            cardDetails: string;
            payButton: string;
            processing: string;
            cancel: string;
            success: string;
            error: string;
        };
        subscription?: {
            title: string;
            billingPeriod: {
                monthly: string;
                yearly: string;
                save: string;
                saveAmount: string;
            };
            period: {
                month: string;
                year: string;
            };
            singleReport: {
                oneTime: string;
                unlockButton: string;
            };
            plans: {
                selectPro: string;
                selectPremium: string;
                mostPopular: string;
                savePerYear: string;
            };
            proUnlock?: {
                title: string;
                unlockButton: string;
                remainingReports: string;
                description: string;
                unlockingReport: string;
                noQuotaTitle: string;
                noQuotaDescription: string;
                upgradePrompt: string;
            };
            premiumUnlock?: {
                title: string;
                unlockButton: string;
                description: string;
                unlockingReport: string;
            };
        };
        referralStats?: {
            title: string;
            clicks: string;
            completions: string;
            needsCompletions: string;
            pendingClicks: string;
            unlocked: string;
        };
        unlockReportButton?: string;
        viewCompleteAnalysis?: string;
    };
    children: React.ReactNode;
}

type BillingPeriod = 'monthly' | 'yearly';
type UnlockTab = 'payment' | 'share';

export default function LockedReportUnlock({
    id,
    locale,
    type,
    email,
    userId,
    onUnlock,
    translations,
    children
}: LockedReportUnlockProps) {
    const [showUnlockArea, setShowUnlockArea] = useState(false);
    const [showMobilePreview, setShowMobilePreview] = useState(false);
    const [showFullModal, setShowFullModal] = useState(false);
    const [showDesktopCenterButton, setShowDesktopCenterButton] = useState(false);
    const [referralCode, setReferralCode] = useState<string>('');
    const [isCopied, setIsCopied] = useState(false);
    const [isUnlocking, setIsUnlocking] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
    const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>('monthly');
    const [activeTab, setActiveTab] = useState<UnlockTab>('payment');
    const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
    const [subscriptionLoading, setSubscriptionLoading] = useState(false);
    const [isProUnlocking, setIsProUnlocking] = useState(false);
    // 新增：未登录用户订阅购买拦截提示
    const [showAuthRequired, setShowAuthRequired] = useState(false);

    const blurContainerRef = useRef<HTMLDivElement>(null);
    const observerRef = useRef<IntersectionObserver | null>(null);

    const { stats, loading: statsLoading, refetch } = useReferralStats(id);
    const pricingConfig = getPricingConfig();
    const defaultPrice = getDefaultSingleReportPrice();

    // 获取本地化的单次报告信息
    const localizedSingleReport = getLocalizedPlan(pricingConfig.singleReport, locale);

    // 判断是否应该显示Pro用户专用解锁界面
    const shouldShowProUnlock = () => {
        return subscriptionInfo &&
            subscriptionInfo.type === 'pro' &&
            subscriptionInfo.status === 'active' &&
            subscriptionInfo.reportsRemaining > 0;
    };

    // 判断是否应该显示Premium用户专用解锁界面
    const shouldShowPremiumUnlock = () => {
        return subscriptionInfo &&
            subscriptionInfo.type === 'premium' &&
            subscriptionInfo.status === 'active';
    };

    // 判断Pro用户是否已经没有额度
    const isProWithoutQuota = () => {
        return subscriptionInfo &&
            subscriptionInfo.type === 'pro' &&
            subscriptionInfo.status === 'active' &&
            subscriptionInfo.reportsRemaining <= 0;
    };

    // 获取Pro计划的报告总数
    const getProTotalReports = () => {
        const proConfig = pricingConfig.proMonthly;
        return proConfig.limits?.reportsPerMonth || 3;
    };

    // 获取推荐码
    useEffect(() => {
        if (stats?.referralCode) {
            setReferralCode(stats.referralCode);
        }
    }, [stats]);

    // 获取用户订阅信息
    useEffect(() => {
        const fetchSubscriptionInfo = async () => {
            if (!userId) return;

            setSubscriptionLoading(true);
            try {
                const response = await fetch('/api/subscription/info');
                if (response.ok) {
                    const data = await response.json();
                    setSubscriptionInfo(data);
                } else {
                    console.error('Failed to fetch subscription info');
                }
            } catch (error) {
                console.error('Error fetching subscription info:', error);
            } finally {
                setSubscriptionLoading(false);
            }
        };

        fetchSubscriptionInfo();
    }, [userId]);

    // 当用户有Pro/Premium订阅时，强制显示payment tab
    useEffect(() => {
        if (shouldShowProUnlock() || shouldShowPremiumUnlock()) {
            setActiveTab('payment');
        }
    }, [subscriptionInfo, shouldShowProUnlock, shouldShowPremiumUnlock]);

    // 监听全局unlock事件 - 当AI咨询升级按钮被点击时打开unlock模态框
    useEffect(() => {
        const handleOpenUnlockModal = () => {
            if (process.env.NODE_ENV === 'development') {
                console.log('LockedReportUnlock: Received openUnlockModal event, opening full modal');
            }
            setShowFullModal(true);
        };

        window.addEventListener('openUnlockModal', handleOpenUnlockModal);

        return () => {
            window.removeEventListener('openUnlockModal', handleOpenUnlockModal);
        };
    }, []);

    // 改进的 IntersectionObserver - 修复移动端滚动触发问题
    useEffect(() => {
        if (!blurContainerRef.current) return;

        if (observerRef.current) {
            observerRef.current.disconnect();
        }

        const isMobile = window.innerWidth <= 768;

        observerRef.current = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    const rect = entry.boundingClientRect;
                    const viewportHeight = window.innerHeight;

                    let isInCenter = false;

                    if (isMobile) {
                        // 移动端：更宽松的触发条件
                        const topThreshold = viewportHeight * 0.2;  // 20% from top
                        const bottomThreshold = viewportHeight * 0.8; // 80% from top
                        isInCenter = rect.top <= bottomThreshold && rect.bottom >= topThreshold;
                    } else {
                        // 桌面端：中心触发
                        const viewportCenter = viewportHeight / 2;
                        isInCenter = rect.top <= viewportCenter && rect.bottom >= viewportCenter;
                    }

                    if (isMobile) {
                        setShowMobilePreview(isInCenter);
                    } else {
                        // 桌面端逻辑
                        if (isInCenter) {
                            // 如果blur内容在中心且没有显示任何浮层/按钮，则首次自动显示浮层
                            if (!showUnlockArea && !showFullModal && !showDesktopCenterButton) {
                                setShowUnlockArea(true);
                            }
                            // 如果已经有居中按钮状态，保持它
                        } else {
                            // 如果不在中心，隐藏居中按钮
                            setShowDesktopCenterButton(false);
                        }
                    }

                    // 调试信息
                    if (process.env.NODE_ENV === 'development') {
                        console.log('Unlock trigger debug:', {
                            isMobile,
                            viewportHeight,
                            rectTop: rect.top,
                            rectBottom: rect.bottom,
                            isInCenter,
                            showDesktopCenterButton,
                            showUnlockArea,
                            showFullModal,
                            intersectionRatio: entry.intersectionRatio
                        });
                    }
                });
            },
            {
                root: null,
                rootMargin: isMobile ? '-10% 0px -10% 0px' : '0px',
                threshold: isMobile
                    ? [0, 0.05, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
                    : [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
            }
        );

        observerRef.current.observe(blurContainerRef.current);

        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, [showUnlockArea, showFullModal, showDesktopCenterButton]);

    // 滚动监听 - 支持向上滚动隐藏和向下滚动显示
    useEffect(() => {
        let scrollTimeout: NodeJS.Timeout;
        let lastScrollY = window.scrollY;
        let scrollDirection: 'up' | 'down' = 'down';

        const handleScroll = () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (!blurContainerRef.current) return;

                const currentScrollY = window.scrollY;
                const rect = blurContainerRef.current.getBoundingClientRect();
                const viewportHeight = window.innerHeight;
                const isMobile = window.innerWidth <= 768;

                // 检测滚动方向
                if (currentScrollY > lastScrollY) {
                    scrollDirection = 'down';
                } else if (currentScrollY < lastScrollY) {
                    scrollDirection = 'up';
                }
                lastScrollY = currentScrollY;

                // 检查元素是否在视窗中
                const topThreshold = viewportHeight * (isMobile ? 0.3 : 0.2);
                const bottomThreshold = viewportHeight * (isMobile ? 0.7 : 0.8);
                const isInView = rect.top <= bottomThreshold && rect.bottom >= topThreshold;

                // 向上滚动时，如果内容滚出视窗上方或滚动到页面顶部则隐藏
                if (scrollDirection === 'up' && (rect.bottom < topThreshold || currentScrollY <= 100)) {
                    if (isMobile) {
                        setShowMobilePreview(false);
                    } else {
                        setShowDesktopCenterButton(false);
                        setShowUnlockArea(false);
                    }
                    return;
                }

                // 向下滚动时，如果内容滚出视窗下方则隐藏
                if (scrollDirection === 'down' && rect.top > bottomThreshold) {
                    if (isMobile) {
                        setShowMobilePreview(false);
                    } else {
                        setShowDesktopCenterButton(false);
                        setShowUnlockArea(false);
                    }
                    return;
                }

                // 在视窗中时显示
                if (isInView && currentScrollY > 100) {
                    if (isMobile) {
                        setShowMobilePreview(true);
                    } else {
                        // PC端：如果没有打开模态框，显示居中按钮
                        if (!showUnlockArea && !showFullModal) {
                            setShowDesktopCenterButton(true);
                        }
                    }
                } else {
                    // 不在视窗中时或在页面顶部时隐藏
                    if (isMobile) {
                        setShowMobilePreview(false);
                    } else {
                        setShowDesktopCenterButton(false);
                    }
                }
            }, 50);
        };

        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('touchmove', handleScroll, { passive: true });
        window.addEventListener('resize', handleScroll, { passive: true });

        return () => {
            clearTimeout(scrollTimeout);
            window.removeEventListener('scroll', handleScroll);
            window.removeEventListener('touchmove', handleScroll);
            window.removeEventListener('resize', handleScroll);
        };
    }, [showUnlockArea, showFullModal]);

    const getShareUrl = () => {
        if (!referralCode) return '';
        return generateShareUrl(id, referralCode, locale, type);
    };

    const handleSystemShare = async () => {
        const shareUrl = getShareUrl();
        const shareData = {
            title: translations.shareReport,
            text: translations.shareReportDescription,
            url: shareUrl,
        };

        if (navigator.share) {
            try {
                await navigator.share(shareData);
            } catch (error) {
                console.error('系统分享失败:', error);
                await navigator.clipboard.writeText(shareUrl);
                setIsCopied(true);
                setTimeout(() => setIsCopied(false), 2000);
            }
        } else {
            await navigator.clipboard.writeText(shareUrl);
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        }
    };

    const handleSocialShare = (platform: string) => {
        const shareUrl = getShareUrl();
        const text = encodeURIComponent(translations.shareReportDescription);
        const url = encodeURIComponent(shareUrl);

        let shareLink = '';
        switch (platform) {
            case 'facebook':
                shareLink = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                break;
            case 'twitter':
                shareLink = `https://twitter.com/intent/tweet?text=${text}&url=${url}`;
                break;
            case 'linkedin':
                shareLink = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
                break;
        }

        if (shareLink) {
            window.open(shareLink, '_blank');
        }
    };

    const handleSelectPlan = (plan: PricingPlan) => {
        setSelectedPlan(plan);

        // 订阅计划需要登录，未登录时弹出提示
        if (plan.type === 'subscription' && !userId) {
            setShowAuthRequired(true);
            return;
        }
        // 单次解锁逻辑不变
        if (plan.type === 'subscription') {
            handleSubscriptionCheckout(plan);
        } else {
            setShowPaymentModal(true);
            setIsUnlocking(true);
        }
    };

    const handleSubscriptionCheckout = async (plan: PricingPlan) => {
        try {
            setIsUnlocking(true);

            const billingPeriod = plan.billingPeriod || 'monthly';
            const planId = plan.id.includes('pro') ? 'pro' : 'premium';

            const response = await fetch('/api/subscription/create-checkout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    planId,
                    billingPeriod
                }),
            });

            const data = await response.json();

            if (response.ok && data.url) {
                // Redirect to Stripe checkout
                window.location.href = data.url;
            } else {
                throw new Error(data.error || 'Failed to create checkout session');
            }
        } catch (error) {
            console.error('Error creating subscription checkout:', error);
            setError(error instanceof Error ? error.message : 'Failed to start subscription checkout');
        } finally {
            setIsUnlocking(false);
        }
    };

    const handlePaymentSuccess = async () => {
        setShowPaymentModal(false);
        setIsUnlocking(false);
        onUnlock();
    };

    const handlePaymentModalClose = () => {
        setShowPaymentModal(false);
        setIsUnlocking(false);
    };

    // Pro用户使用订阅额度解锁报告
    const handleProUnlock = async () => {
        if (!userId || isProUnlocking) return;

        setIsProUnlocking(true);
        setError(null);

        try {
            const response = await fetch('/api/unlock-single-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    testId: id
                }),
            });

            const data = await response.json();

            if (response.ok) {
                console.log(`✅ Pro解锁成功，开始关闭模态框并触发报告生成`);

                // 更新订阅信息
                if (data.reportsRemaining !== undefined) {
                    setSubscriptionInfo(prev => prev ? {
                        ...prev,
                        reportsRemaining: data.reportsRemaining
                    } : null);
                }

                // 清除前端缓存，确保获取最新状态
                if (typeof window !== 'undefined') {
                    const clearReportCache = (window as any).clearReportCache;
                    if (clearReportCache && typeof clearReportCache === 'function') {
                        clearReportCache(id, locale);
                        console.log(`🗑️ 清除前端缓存: ${id}-${locale}`);
                    }
                }

                // 立即关闭模态框
                setShowFullModal(false);
                setShowUnlockArea(false);
                setShowMobilePreview(false);
                setShowDesktopCenterButton(false);

                // 稍微延迟一下，确保模态框关闭动画完成，然后触发报告生成
                setTimeout(() => {
                    console.log(`🚀 调用onUnlock回调，触发报告生成`);
                    onUnlock();
                }, 100);
            } else {
                if (data.needsUpgrade) {
                    setError(data.error || 'Subscription upgrade required');
                } else {
                    setError(data.error || 'Failed to unlock report');
                }
            }
        } catch (error) {
            console.error('Error unlocking report with Pro subscription:', error);
            setError('Failed to unlock report. Please try again.');
        } finally {
            setIsProUnlocking(false);
        }
    };

    const handleCloseUnlockArea = () => {
        setShowUnlockArea(false);
        setShowFullModal(false);

        // PC端：关闭浮层后，如果blur内容在viewport中心，则显示居中按钮
        const isMobile = window.innerWidth <= 768;
        if (!isMobile && blurContainerRef.current) {
            const rect = blurContainerRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportCenter = viewportHeight / 2;
            const isInCenter = rect.top <= viewportCenter && rect.bottom >= viewportCenter;

            if (isInCenter) {
                setShowDesktopCenterButton(true);
            }
        }
    };

    const handleOpenFullModal = () => {
        setShowFullModal(true);
        setShowUnlockArea(true);
        setShowDesktopCenterButton(false); // 打开浮层时隐藏居中按钮
    };

    // 新增：PC端居中按钮点击处理
    const handleDesktopCenterButtonClick = () => {
        setShowDesktopCenterButton(false);
        setShowUnlockArea(true);
    };

    // 获取当前计费周期的计划
    const getCurrentPlans = () => {
        const plans = billingPeriod === 'monthly'
            ? { pro: pricingConfig.proMonthly, premium: pricingConfig.premiumMonthly }
            : { pro: pricingConfig.proYearly, premium: pricingConfig.premiumYearly };

        // 根据当前语言本地化计划信息
        return {
            pro: getLocalizedPlan(plans.pro, locale),
            premium: getLocalizedPlan(plans.premium, locale)
        };
    };

    const currentPlans = getCurrentPlans();

    // 计算年付费折扣
    const getYearlyDiscount = () => {
        const monthlyTotal = pricingConfig.proMonthly.price * 12;
        const yearlyPrice = pricingConfig.proYearly.price;
        const discount = Math.round(((monthlyTotal - yearlyPrice) / monthlyTotal) * 100);
        return discount;
    };

    const yearlyDiscount = getYearlyDiscount();

    return (
        <div className="relative">
            <div
                ref={blurContainerRef}
                className="relative filter blur-[2px] pointer-events-none select-none opacity-60"
                style={{ minHeight: '400px' }}
            >
                {children}

                {/* 显示内容提示覆盖层 */}
                {(showUnlockArea || showMobilePreview) && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div className="text-center text-white/90 bg-black/50 rounded-lg p-4 mx-4 backdrop-blur-sm">
                            <Lock className="w-8 h-8 mx-auto mb-2 opacity-80" />
                            <p className="text-sm font-medium">Your detailed report is ready</p>
                            <p className="text-xs opacity-80">Unlock to view complete insights</p>
                        </div>
                    </div>
                )}
            </div>

            {/* 移动端居中unlock按钮 - 根据用户订阅状态显示不同按钮 */}
            {showMobilePreview && !showFullModal && (
                <div className="fixed inset-0 z-50 pointer-events-none sm:hidden flex items-center justify-center">
                    {shouldShowProUnlock() ? (
                        // Pro用户专用解锁按钮
                        <motion.button
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            onClick={handleProUnlock}
                            disabled={isProUnlocking}
                            className="pointer-events-auto bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-6 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.05] active:scale-[0.95] backdrop-blur-sm border border-white/20 mx-4"
                        >
                            <div className="flex items-center justify-center space-x-3">
                                <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                                    {isProUnlocking ? (
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                    ) : (
                                        <Zap className="w-4 h-4" />
                                    )}
                                </div>
                                <div className="flex flex-col items-start">
                                    <span className="text-base font-bold leading-tight">
                                        {isProUnlocking
                                            ? (translations.subscription?.proUnlock?.unlockingReport || 'Unlocking...')
                                            : (translations.subscription?.proUnlock?.unlockButton || 'Unlock with Pro')
                                        }
                                    </span>
                                    <span className="text-xs text-blue-100 leading-tight">
                                        {translations.subscription?.proUnlock?.remainingReports
                                            ?.replace('${remaining}', subscriptionInfo?.reportsRemaining.toString() || '0')
                                            ?.replace('${total}', getProTotalReports().toString()) ||
                                            `Remaining: ${subscriptionInfo?.reportsRemaining || 0}/${getProTotalReports()} reports`}
                                    </span>
                                </div>
                            </div>
                        </motion.button>
                    ) : shouldShowPremiumUnlock() ? (
                        // Premium用户专用解锁按钮
                        <motion.button
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            onClick={onUnlock}
                            className="pointer-events-auto bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-bold py-4 px-6 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.05] active:scale-[0.95] backdrop-blur-sm border border-white/20 mx-4"
                        >
                            <div className="flex items-center justify-center space-x-3">
                                <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                                    <Crown className="w-4 h-4" />
                                </div>
                                <div className="flex flex-col items-start">
                                    <span className="text-base font-bold leading-tight">
                                        {translations.subscription?.premiumUnlock?.unlockButton || 'Access Report'}
                                    </span>
                                    <span className="text-xs text-purple-100 leading-tight">
                                        {translations.subscription?.premiumUnlock?.description || 'Unlimited reports included'}
                                    </span>
                                </div>
                            </div>
                        </motion.button>
                    ) : (
                        // 普通解锁按钮（Free用户或Pro无额度用户）
                        <motion.button
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            onClick={handleOpenFullModal}
                            className="pointer-events-auto bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.05] active:scale-[0.95] backdrop-blur-sm border border-white/20 mx-4"
                        >
                            <div className="flex items-center justify-center space-x-3">
                                <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                                    <Lock className="w-4 h-4" />
                                </div>
                                <div className="flex flex-col items-start">
                                    <span className="text-base font-bold leading-tight">{translations.unlockReportButton || 'Unlock Report'}</span>
                                    <span className="text-xs text-emerald-100 leading-tight">{translations.viewCompleteAnalysis || 'View your complete analysis'}</span>
                                </div>
                            </div>
                        </motion.button>
                    )}
                </div>
            )}

            {/* PC端居中unlock按钮 - 根据用户订阅状态显示不同按钮 */}
            {showDesktopCenterButton && !showUnlockArea && !showFullModal && (
                <div className="fixed inset-0 z-50 pointer-events-none hidden sm:flex items-center justify-center xl:justify-start xl:pl-[calc((100vw-24rem)/2)]">
                    {shouldShowProUnlock() ? (
                        // Pro用户专用解锁按钮
                        <motion.button
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            onClick={handleProUnlock}
                            disabled={isProUnlocking}
                            className="pointer-events-auto bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-6 xl:px-8 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.05] active:scale-[0.95] backdrop-blur-sm border border-white/20"
                        >
                            <div className="flex items-center justify-center space-x-3 xl:space-x-4">
                                <div className="w-7 h-7 xl:w-8 xl:h-8 bg-white/20 rounded-full flex items-center justify-center">
                                    {isProUnlocking ? (
                                        <div className="w-4 h-4 xl:w-5 xl:h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                    ) : (
                                        <Zap className="w-4 h-4 xl:w-5 xl:h-5" />
                                    )}
                                </div>
                                <div className="flex flex-col items-start">
                                    <span className="text-base xl:text-lg font-bold leading-tight">
                                        {isProUnlocking
                                            ? (translations.subscription?.proUnlock?.unlockingReport || 'Unlocking...')
                                            : (translations.subscription?.proUnlock?.unlockButton || 'Unlock with Pro')
                                        }
                                    </span>
                                    <span className="text-xs xl:text-sm text-blue-100 leading-tight">
                                        {translations.subscription?.proUnlock?.remainingReports
                                            ?.replace('${remaining}', subscriptionInfo?.reportsRemaining.toString() || '0')
                                            ?.replace('${total}', getProTotalReports().toString()) ||
                                            `Remaining: ${subscriptionInfo?.reportsRemaining || 0}/${getProTotalReports()} reports`}
                                    </span>
                                </div>
                            </div>
                        </motion.button>
                    ) : shouldShowPremiumUnlock() ? (
                        // Premium用户专用解锁按钮
                        <motion.button
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            onClick={onUnlock}
                            className="pointer-events-auto bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-bold py-4 px-6 xl:px-8 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.05] active:scale-[0.95] backdrop-blur-sm border border-white/20"
                        >
                            <div className="flex items-center justify-center space-x-3 xl:space-x-4">
                                <div className="w-7 h-7 xl:w-8 xl:h-8 bg-white/20 rounded-full flex items-center justify-center">
                                    <Crown className="w-4 h-4 xl:w-5 xl:h-5" />
                                </div>
                                <div className="flex flex-col items-start">
                                    <span className="text-base xl:text-lg font-bold leading-tight">
                                        {translations.subscription?.premiumUnlock?.unlockButton || 'Access Report'}
                                    </span>
                                    <span className="text-xs xl:text-sm text-purple-100 leading-tight">
                                        {translations.subscription?.premiumUnlock?.description || 'Unlimited reports included'}
                                    </span>
                                </div>
                            </div>
                        </motion.button>
                    ) : (
                        // 普通解锁按钮（Free用户或Pro无额度用户）
                        <motion.button
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            onClick={handleDesktopCenterButtonClick}
                            className="pointer-events-auto bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white font-bold py-4 px-6 xl:px-8 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.05] active:scale-[0.95] backdrop-blur-sm border border-white/20"
                        >
                            <div className="flex items-center justify-center space-x-3 xl:space-x-4">
                                <div className="w-7 h-7 xl:w-8 xl:h-8 bg-white/20 rounded-full flex items-center justify-center">
                                    <Lock className="w-4 h-4 xl:w-5 xl:h-5" />
                                </div>
                                <div className="flex flex-col items-start">
                                    <span className="text-base xl:text-lg font-bold leading-tight">{translations.unlockReportButton || 'Unlock Report'}</span>
                                    <span className="text-xs xl:text-sm text-emerald-100 leading-tight">{translations.viewCompleteAnalysis || 'View your complete analysis'}</span>
                                </div>
                            </div>
                        </motion.button>
                    )}
                </div>
            )}

            {/* 桌面端和移动端全屏模态框 - 优化宽度和响应式 */}
            {(showUnlockArea || showFullModal) && (
                <div className="fixed inset-0 z-[60] bg-black/30 backdrop-blur-[2px] flex items-center justify-center p-0 sm:p-8 lg:p-12 xl:p-16">
                    <motion.div
                        initial={{ opacity: 0, scale: 0.95, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.95, y: 20 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className="bg-white dark:bg-gray-800 rounded-none sm:rounded-2xl shadow-2xl w-full h-screen sm:h-auto sm:w-[90%] sm:max-w-2xl lg:max-w-3xl xl:max-w-4xl 2xl:max-w-5xl sm:max-h-[90vh] lg:max-h-[85vh] overflow-hidden flex flex-col relative z-[65]"
                    >
                        {/* 头部 */}
                        <div className="bg-gradient-to-r from-emerald-500 to-blue-600 p-2 sm:p-6 text-white relative">
                            <button
                                onClick={handleCloseUnlockArea}
                                className="absolute top-1 right-1 sm:top-4 sm:right-4 w-7 h-7 sm:w-8 sm:h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors z-10"
                            >
                                <X className="w-3.5 h-3.5 sm:w-5 sm:h-5" />
                            </button>

                            {/* 移动端头部 - 修复垂直对齐问题 */}
                            <div className="block sm:hidden">
                                <div className="flex items-center justify-between pr-8 min-h-[44px]">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                                            <Lock className="w-4 h-4" />
                                        </div>
                                        <h3 className="text-base font-bold leading-tight">
                                            {translations.reportLocked}
                                        </h3>
                                    </div>
                                </div>
                            </div>

                            {/* 桌面端完整头部 */}
                            <div className="hidden sm:flex items-center space-x-3 pr-10">
                                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                    <Lock className="w-6 h-6" />
                                </div>
                                <div className="min-w-0 flex-1">
                                    <h3 className="text-xl font-bold">
                                        {translations.reportLocked}
                                    </h3>
                                    <p className="text-emerald-100 text-sm">
                                        {translations.reportLockedDescription}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Tab 导航 - 根据用户订阅状态调整 */}
                        <div className="border-b border-gray-200 dark:border-gray-600">
                            <div className="flex">
                                <button
                                    onClick={() => setActiveTab('payment')}
                                    className={`${shouldShowProUnlock() || shouldShowPremiumUnlock() ? 'w-full' : 'flex-1'} px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium transition-all duration-200 relative ${activeTab === 'payment'
                                        ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20'
                                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                                        }`}
                                >
                                    <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                                        {shouldShowProUnlock() || shouldShowPremiumUnlock() ? (
                                            <Zap className="w-3 h-3 sm:w-4 sm:h-4" />
                                        ) : (
                                            <CreditCard className="w-3 h-3 sm:w-4 sm:h-4" />
                                        )}
                                        <span>
                                            {shouldShowProUnlock() || shouldShowPremiumUnlock()
                                                ? 'Unlock Options'
                                                : (translations.payToUnlockTab || translations.payToUnlock || 'Pay to Unlock')
                                            }
                                        </span>
                                    </div>
                                </button>
                                {/* 只有非订阅用户才显示分享选项 */}
                                {!shouldShowProUnlock() && !shouldShowPremiumUnlock() && (
                                    <button
                                        onClick={() => setActiveTab('share')}
                                        className={`flex-1 px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium transition-all duration-200 relative ${activeTab === 'share'
                                            ? 'text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-600 dark:border-emerald-400 bg-emerald-50/50 dark:bg-emerald-900/20'
                                            : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/30'
                                            }`}
                                    >
                                        <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                                            <Share2 className="w-3 h-3 sm:w-4 sm:h-4" />
                                            <span className="hidden sm:inline">{translations.shareToUnlockTab || translations.shareToUnlock || 'Share to Unlock'}</span>
                                            <span className="sm:hidden">{translations.share || 'Share'}</span>
                                            <span className="text-xs bg-emerald-100 dark:bg-emerald-900 text-emerald-700 dark:text-emerald-300 px-1 sm:px-1.5 py-0.5 rounded-full">
                                                {translations.free || 'FREE'}
                                            </span>
                                        </div>
                                    </button>
                                )}
                            </div>
                        </div>

                        {/* 内容区域 */}
                        <div className="flex-1 overflow-y-auto sm:max-h-[calc(95vh-160px)] xl:max-h-[calc(90vh-160px)]">
                            <AnimatePresence mode="wait">
                                {activeTab === 'payment' || shouldShowProUnlock() || shouldShowPremiumUnlock() ? (
                                    <motion.div
                                        key="payment"
                                        initial={{ opacity: 0, x: -20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        exit={{ opacity: 0, x: 20 }}
                                        transition={{ duration: 0.2 }}
                                        className="p-3 sm:p-6"
                                    >
                                        {/* 显示错误信息 */}
                                        {error && (
                                            <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                                <div className="flex items-start space-x-2">
                                                    <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                                                    <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                                                </div>
                                            </div>
                                        )}

                                        {/* Pro用户专用解锁选项 */}
                                        {shouldShowProUnlock() && (
                                            <div className="mb-4 sm:mb-6">
                                                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg sm:rounded-xl p-3 sm:p-4 border-2 border-blue-200 dark:border-blue-700">
                                                    {/* 移动端紧凑布局 */}
                                                    <div className="block sm:hidden">
                                                        <div className="flex items-center justify-between mb-2">
                                                            <div className="flex items-center space-x-2">
                                                                <Zap className="w-4 h-4 text-blue-600" />
                                                                <span className="text-sm font-bold text-blue-900 dark:text-blue-100">
                                                                    {translations.subscription?.proUnlock?.title || 'Use Your Subscription'}
                                                                </span>
                                                            </div>
                                                            <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full font-medium">
                                                                PRO
                                                            </span>
                                                        </div>
                                                        <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                                                            {translations.subscription?.proUnlock?.description || 'Use one of your monthly Pro report allowances'}
                                                        </p>
                                                        <p className="text-xs text-blue-700 dark:text-blue-300 mb-3 font-medium">
                                                            {translations.subscription?.proUnlock?.remainingReports
                                                                ?.replace('${remaining}', subscriptionInfo?.reportsRemaining.toString() || '0')
                                                                ?.replace('${total}', getProTotalReports().toString()) ||
                                                                `Remaining: ${subscriptionInfo?.reportsRemaining || 0}/${getProTotalReports()} reports`}
                                                        </p>
                                                        <button
                                                            onClick={handleProUnlock}
                                                            disabled={isProUnlocking}
                                                            className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                        >
                                                            {isProUnlocking ? (
                                                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                            ) : (
                                                                <>
                                                                    <Zap className="w-4 h-4 mr-2" />
                                                                    <span className="text-sm whitespace-nowrap">
                                                                        {translations.subscription?.proUnlock?.unlockButton || 'Unlock with Pro'}
                                                                    </span>
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>

                                                    {/* 桌面端完整布局 */}
                                                    <div className="hidden sm:flex sm:items-center sm:justify-between gap-4">
                                                        <div className="flex-1">
                                                            <div className="flex items-center space-x-2 mb-1">
                                                                <Zap className="w-5 h-5 text-blue-600" />
                                                                <span className="text-base sm:text-lg font-bold text-blue-900 dark:text-blue-100">
                                                                    {translations.subscription?.proUnlock?.title || 'Use Your Subscription'}
                                                                </span>
                                                                <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full font-medium">
                                                                    PRO
                                                                </span>
                                                            </div>
                                                            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-1">
                                                                {translations.subscription?.proUnlock?.description || 'Use one of your monthly Pro report allowances'}
                                                            </p>
                                                            <p className="text-xs sm:text-sm text-blue-700 dark:text-blue-300 font-medium">
                                                                {translations.subscription?.proUnlock?.remainingReports
                                                                    ?.replace('${remaining}', subscriptionInfo?.reportsRemaining.toString() || '0')
                                                                    ?.replace('${total}', getProTotalReports().toString()) ||
                                                                    `Remaining: ${subscriptionInfo?.reportsRemaining || 0}/${getProTotalReports()} reports`}
                                                            </p>
                                                        </div>
                                                        <button
                                                            onClick={handleProUnlock}
                                                            disabled={isProUnlocking}
                                                            className="w-auto bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                        >
                                                            {isProUnlocking ? (
                                                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                            ) : (
                                                                <>
                                                                    <Zap className="w-4 h-4 mr-2" />
                                                                    <span className="text-sm whitespace-nowrap">
                                                                        {translations.subscription?.proUnlock?.unlockButton || 'Unlock with Pro'}
                                                                    </span>
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* Premium用户专用解锁选项 */}
                                        {shouldShowPremiumUnlock() && (
                                            <div className="mb-4 sm:mb-6">
                                                <div className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg sm:rounded-xl p-3 sm:p-4 border-2 border-purple-200 dark:border-purple-700">
                                                    {/* 移动端紧凑布局 */}
                                                    <div className="block sm:hidden">
                                                        <div className="flex items-center justify-between mb-2">
                                                            <div className="flex items-center space-x-2">
                                                                <Crown className="w-4 h-4 text-purple-600" />
                                                                <span className="text-sm font-bold text-purple-900 dark:text-purple-100">
                                                                    {translations.subscription?.premiumUnlock?.title || 'Premium Access'}
                                                                </span>
                                                            </div>
                                                            <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full font-medium">
                                                                PREMIUM
                                                            </span>
                                                        </div>
                                                        <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                                                            {translations.subscription?.premiumUnlock?.description || 'Unlimited reports included with Premium'}
                                                        </p>
                                                        <button
                                                            onClick={onUnlock}
                                                            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                        >
                                                            <Crown className="w-4 h-4 mr-2" />
                                                            <span className="text-sm whitespace-nowrap">
                                                                {translations.subscription?.premiumUnlock?.unlockButton || 'Access Report'}
                                                            </span>
                                                        </button>
                                                    </div>

                                                    {/* 桌面端完整布局 */}
                                                    <div className="hidden sm:flex sm:items-center sm:justify-between gap-4">
                                                        <div className="flex-1">
                                                            <div className="flex items-center space-x-2 mb-1">
                                                                <Crown className="w-5 h-5 text-purple-600" />
                                                                <span className="text-base sm:text-lg font-bold text-purple-900 dark:text-purple-100">
                                                                    {translations.subscription?.premiumUnlock?.title || 'Premium Access'}
                                                                </span>
                                                                <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full font-medium">
                                                                    PREMIUM
                                                                </span>
                                                            </div>
                                                            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                                                                {translations.subscription?.premiumUnlock?.description || 'Unlimited reports included with Premium'}
                                                            </p>
                                                        </div>
                                                        <button
                                                            onClick={onUnlock}
                                                            className="w-auto bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                        >
                                                            <Crown className="w-4 h-4 mr-2" />
                                                            <span className="text-sm whitespace-nowrap">
                                                                {translations.subscription?.premiumUnlock?.unlockButton || 'Access Report'}
                                                            </span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* Pro用户没有额度时的提示 */}
                                        {isProWithoutQuota() && (
                                            <div className="mb-4 sm:mb-6">
                                                <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-amber-200 dark:border-amber-800">
                                                    <div className="flex items-start space-x-3">
                                                        <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
                                                        <div className="flex-1">
                                                            <h4 className="text-sm font-semibold text-amber-900 dark:text-amber-100 mb-1">
                                                                {translations.subscription?.proUnlock?.noQuotaTitle || 'Monthly Limit Reached'}
                                                            </h4>
                                                            <p className="text-xs text-amber-700 dark:text-amber-300 mb-2">
                                                                {translations.subscription?.proUnlock?.noQuotaDescription
                                                                    ?.replace('${total}', getProTotalReports().toString()) ||
                                                                    `You've used all ${getProTotalReports()} reports this month`}
                                                            </p>
                                                            <p className="text-xs text-amber-700 dark:text-amber-300">
                                                                {translations.subscription?.proUnlock?.upgradePrompt || 'Upgrade to Premium for unlimited reports'}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* 单次解锁选项 - 只对Free用户或Pro无额度用户显示 */}
                                        {(!shouldShowProUnlock() && !shouldShowPremiumUnlock()) && (
                                            <div className="mb-4 sm:mb-6">
                                                <div className="bg-gradient-to-r from-emerald-50 to-blue-50 dark:from-emerald-900/20 dark:to-blue-900/20 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-emerald-200 dark:border-emerald-700">
                                                    {/* 移动端紧凑布局 */}
                                                    <div className="block sm:hidden">
                                                        <div className="flex items-center justify-between mb-2">
                                                            <div className="flex items-center space-x-2">
                                                                <Star className="w-4 h-4 text-emerald-600" />
                                                                <span className="text-lg font-bold text-emerald-900 dark:text-emerald-100">
                                                                    ${defaultPrice.price}
                                                                </span>
                                                                <span className="text-xs text-gray-600 dark:text-gray-400">
                                                                    {translations.subscription?.singleReport.oneTime || 'One-time'}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                                            {localizedSingleReport.name}
                                                        </h4>
                                                        <button
                                                            onClick={() => handleSelectPlan(pricingConfig.singleReport)}
                                                            disabled={isUnlocking}
                                                            className="w-full bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                        >
                                                            {isUnlocking && selectedPlan?.id === 'single-report' ? (
                                                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                            ) : (
                                                                <span className="text-sm whitespace-nowrap">
                                                                    {translations.subscription?.singleReport.unlockButton?.replace('${price}', defaultPrice.price.toString()) || `Unlock $${defaultPrice.price}`}
                                                                </span>
                                                            )}
                                                        </button>
                                                    </div>

                                                    {/* 桌面端完整布局 */}
                                                    <div className="hidden sm:flex sm:items-center sm:justify-between gap-3">
                                                        <div className="flex-1">
                                                            <div className="flex items-center space-x-2 mb-1">
                                                                <Star className="w-4 h-4 text-emerald-600" />
                                                                <span className="text-base sm:text-lg font-bold text-emerald-900 dark:text-emerald-100">
                                                                    ${defaultPrice.price}
                                                                </span>
                                                                <span className="text-xs text-gray-600 dark:text-gray-400">
                                                                    {translations.subscription?.singleReport.oneTime || 'One-time'}
                                                                </span>
                                                            </div>
                                                            <h4 className="text-sm sm:text-base font-semibold text-gray-900 dark:text-gray-100 mb-1">
                                                                {localizedSingleReport.name}
                                                            </h4>
                                                            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                                                {localizedSingleReport.description}
                                                            </p>
                                                        </div>
                                                        <button
                                                            onClick={() => handleSelectPlan(pricingConfig.singleReport)}
                                                            disabled={isUnlocking}
                                                            className="w-auto bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                        >
                                                            {isUnlocking && selectedPlan?.id === 'single-report' ? (
                                                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                            ) : (
                                                                <span className="text-sm whitespace-nowrap">
                                                                    {translations.subscription?.singleReport.unlockButton?.replace('${price}', defaultPrice.price.toString()) || `Unlock $${defaultPrice.price}`}
                                                                </span>
                                                            )}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* 订阅选项 - 只对非订阅用户显示 */}
                                        {!shouldShowProUnlock() && !shouldShowPremiumUnlock() && !isProWithoutQuota() && (
                                            <div>
                                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4">
                                                    <h3 className="text-sm sm:text-lg font-semibold text-gray-900 dark:text-gray-100">
                                                        {translations.subscription?.title || 'Subscription Plans'}
                                                    </h3>

                                                    {/* 计费周期切换 - 改进的省钱提示效果 */}
                                                    <div className="relative w-fit mx-auto sm:mx-0">
                                                        {/* 年度省钱提示 - 位于tab外部，更显眼 */}
                                                        <AnimatePresence>
                                                            {billingPeriod === 'yearly' && (
                                                                <motion.div
                                                                    initial={{ opacity: 0, scale: 0.8, y: -10 }}
                                                                    animate={{ opacity: 1, scale: 1, y: 0 }}
                                                                    exit={{ opacity: 0, scale: 0.8, y: -10 }}
                                                                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                                                    className="absolute -top-8 right-0 z-20"
                                                                >
                                                                    <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                                                        <div className="flex items-center space-x-1">
                                                                            <span>🎉</span>
                                                                            <span>
                                                                                {translations.subscription?.billingPeriod.saveAmount?.replace('{amount}', Math.round(yearlyDiscount).toString()) ||
                                                                                    `SAVE ${Math.round(yearlyDiscount)}%`}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    {/* 小箭头指向yearly tab */}
                                                                    <div className="absolute top-full right-6 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-green-500"></div>
                                                                </motion.div>
                                                            )}
                                                        </AnimatePresence>

                                                        {/* 计费周期切换器 */}
                                                        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-1 relative overflow-visible">
                                                            <motion.div
                                                                className="absolute inset-y-1 bg-white dark:bg-gray-600 rounded-md shadow-sm z-0"
                                                                initial={false}
                                                                animate={{
                                                                    x: billingPeriod === 'monthly' ? '4px' : '124px',
                                                                    width: '116px'
                                                                }}
                                                                transition={{ type: "spring", stiffness: 300, damping: 30 }}
                                                            />
                                                            <div className="flex relative z-10">
                                                                <button
                                                                    onClick={() => setBillingPeriod('monthly')}
                                                                    className={`w-[116px] py-2.5 px-4 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center ${billingPeriod === 'monthly'
                                                                        ? 'text-gray-900 dark:text-gray-100'
                                                                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                                                                        }`}
                                                                >
                                                                    {translations.subscription?.billingPeriod.monthly || 'Monthly'}
                                                                </button>
                                                                <button
                                                                    onClick={() => setBillingPeriod('yearly')}
                                                                    className={`w-[116px] py-2.5 px-4 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center ${billingPeriod === 'yearly'
                                                                        ? 'text-gray-900 dark:text-gray-100'
                                                                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                                                                        }`}
                                                                >
                                                                    <div className="flex items-center space-x-1">
                                                                        <span>{translations.subscription?.billingPeriod.yearly || 'Yearly'}</span>
                                                                        {/* 小的内部省钱标识 */}
                                                                        <motion.div
                                                                            initial={{ scale: 0 }}
                                                                            animate={{ scale: billingPeriod === 'yearly' ? 1 : 0.8 }}
                                                                            className="flex items-center"
                                                                        >
                                                                            <span className={`text-[10px] px-1.5 py-0.5 rounded-full font-bold transition-all ${billingPeriod === 'yearly'
                                                                                ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300'
                                                                                : 'bg-green-200 dark:bg-green-800 text-green-600 dark:text-green-400'
                                                                                }`}>
                                                                                {translations.subscription?.billingPeriod.save || 'SAVE'}
                                                                            </span>
                                                                        </motion.div>
                                                                    </div>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* 订阅计划容器 - 为Most Popular标签预留空间 */}
                                                <div className="pt-4 sm:pt-6">
                                                    <motion.div
                                                        key={billingPeriod}
                                                        initial={{ opacity: 0, y: 10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        transition={{ duration: 0.3 }}
                                                        className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-1 lg:grid-cols-2 sm:gap-4 sm:items-stretch"
                                                    >
                                                        {/* Pro Plan - 修复Most Popular标签溢出问题 */}
                                                        <div className="border-2 border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 rounded-lg sm:rounded-xl p-3 sm:p-4 relative shadow-lg ring-2 ring-blue-200 dark:ring-blue-700 transform scale-105">
                                                            {/* Most Popular标签 - 确保不被overflow hidden */}
                                                            <div className="absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2 z-20">
                                                                <motion.span
                                                                    initial={{ scale: 0 }}
                                                                    animate={{ scale: 1 }}
                                                                    className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-bold shadow-lg whitespace-nowrap"
                                                                >
                                                                    {translations.subscription?.plans.mostPopular || 'MOST POPULAR'}
                                                                </motion.span>
                                                            </div>

                                                            <div className="flex flex-col h-full pt-4 sm:pt-6">
                                                                <div className="text-center flex-grow">
                                                                    <h4 className="text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                                                                        {currentPlans.pro.name}
                                                                    </h4>
                                                                    <div className="mb-3">
                                                                        <span className="text-xl sm:text-3xl font-bold text-blue-600">
                                                                            ${currentPlans.pro.price}
                                                                        </span>
                                                                        <span className="text-xs sm:text-sm text-gray-500">
                                                                            /{billingPeriod === 'monthly' ? (translations.subscription?.period.month || 'month') : (translations.subscription?.period.year || 'year')}
                                                                        </span>
                                                                        {billingPeriod === 'yearly' && (
                                                                            <motion.div
                                                                                initial={{ opacity: 0, scale: 0.8 }}
                                                                                animate={{ opacity: 1, scale: 1 }}
                                                                                className="text-xs text-green-600 dark:text-green-400 font-medium mt-1"
                                                                            >
                                                                                {translations.subscription?.plans.savePerYear?.replace('${amount}', Math.round((pricingConfig.proMonthly.price * 12) - pricingConfig.proYearly.price).toString()) || `Save $${Math.round((pricingConfig.proMonthly.price * 12) - pricingConfig.proYearly.price)}/year`}
                                                                            </motion.div>
                                                                        )}
                                                                    </div>

                                                                    {/* 统一描述 */}
                                                                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-3 sm:mb-4 line-clamp-2">
                                                                        {currentPlans.pro.description}
                                                                    </p>

                                                                    {/* 移动端简化特性列表 */}
                                                                    <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1 mb-3 sm:hidden">
                                                                        {currentPlans.pro.features.slice(0, 2).map((feature: string, index: number) => (
                                                                            <li key={index} className="flex items-center text-left">
                                                                                <CheckCircle className="w-3 h-3 text-blue-500 mr-2 flex-shrink-0" />
                                                                                <span>{feature}</span>
                                                                            </li>
                                                                        ))}
                                                                    </ul>

                                                                    {/* 桌面端完整特性列表 */}
                                                                    <ul className="hidden sm:block text-xs text-gray-600 dark:text-gray-400 space-y-1 sm:space-y-2 mb-4 flex-grow">
                                                                        {currentPlans.pro.features.slice(0, 3).map((feature: string, index: number) => (
                                                                            <li key={index} className="flex items-center text-left">
                                                                                <CheckCircle className="w-3 h-3 text-blue-500 mr-2 flex-shrink-0" />
                                                                                <span>{feature}</span>
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>

                                                                <button
                                                                    onClick={() => handleSelectPlan(currentPlans.pro)}
                                                                    disabled={isUnlocking}
                                                                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-3 sm:py-4 px-4 rounded-lg transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] mt-auto"
                                                                >
                                                                    {isUnlocking && selectedPlan?.id === currentPlans.pro.id ? (
                                                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                                    ) : (
                                                                        <span className="text-sm">{translations.subscription?.plans.selectPro || 'Select Pro'}</span>
                                                                    )}
                                                                </button>
                                                            </div>
                                                        </div>

                                                        {/* Premium Plan */}
                                                        <div className="border border-gray-200 dark:border-gray-600 rounded-lg sm:rounded-xl p-3 sm:p-4 hover:border-purple-300 dark:hover:border-purple-500 transition-colors">
                                                            <div className="flex flex-col h-full">
                                                                <div className="text-center flex-grow">
                                                                    <h4 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                                                        {currentPlans.premium.name}
                                                                    </h4>
                                                                    <div className="mb-3">
                                                                        <span className="text-xl sm:text-3xl font-bold text-purple-600">
                                                                            ${currentPlans.premium.price}
                                                                        </span>
                                                                        <span className="text-xs sm:text-sm text-gray-500">
                                                                            /{billingPeriod === 'monthly' ? (translations.subscription?.period.month || 'month') : (translations.subscription?.period.year || 'year')}
                                                                        </span>
                                                                        {billingPeriod === 'yearly' && (
                                                                            <motion.div
                                                                                initial={{ opacity: 0, scale: 0.8 }}
                                                                                animate={{ opacity: 1, scale: 1 }}
                                                                                className="text-xs text-green-600 dark:text-green-400 font-medium mt-1"
                                                                            >
                                                                                {translations.subscription?.plans.savePerYear?.replace('${amount}', Math.round((pricingConfig.premiumMonthly.price * 12) - pricingConfig.premiumYearly.price).toString()) || `Save $${Math.round((pricingConfig.premiumMonthly.price * 12) - pricingConfig.premiumYearly.price)}/year`}
                                                                            </motion.div>
                                                                        )}
                                                                    </div>

                                                                    {/* 统一描述 */}
                                                                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-3 sm:mb-4 line-clamp-2">
                                                                        {currentPlans.premium.description}
                                                                    </p>

                                                                    {/* 移动端简化特性列表 */}
                                                                    <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1 mb-3 sm:hidden">
                                                                        {currentPlans.premium.features.slice(0, 2).map((feature: string, index: number) => (
                                                                            <li key={index} className="flex items-center text-left">
                                                                                <CheckCircle className="w-3 h-3 text-purple-500 mr-2 flex-shrink-0" />
                                                                                <span>{feature}</span>
                                                                            </li>
                                                                        ))}
                                                                    </ul>

                                                                    {/* 桌面端完整特性列表 */}
                                                                    <ul className="hidden sm:block text-xs text-gray-600 dark:text-gray-400 space-y-1 sm:space-y-2 mb-4 flex-grow">
                                                                        {currentPlans.premium.features.slice(0, 3).map((feature: string, index: number) => (
                                                                            <li key={index} className="flex items-center text-left">
                                                                                <CheckCircle className="w-3 h-3 text-purple-500 mr-2 flex-shrink-0" />
                                                                                <span>{feature}</span>
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>

                                                                <button
                                                                    onClick={() => handleSelectPlan(currentPlans.premium)}
                                                                    disabled={isUnlocking}
                                                                    className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium py-3 sm:py-4 px-4 rounded-lg transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] mt-auto"
                                                                >
                                                                    {isUnlocking && selectedPlan?.id === currentPlans.premium.id ? (
                                                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                                    ) : (
                                                                        <span className="text-sm">{translations.subscription?.plans.selectPremium || 'Select Premium'}</span>
                                                                    )}
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </motion.div>
                                                </div>
                                            </div>
                                        )}
                                    </motion.div>
                                ) : (
                                    <motion.div
                                        key="share"
                                        initial={{ opacity: 0, x: 20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        exit={{ opacity: 0, x: -20 }}
                                        transition={{ duration: 0.2 }}
                                        className="p-3 sm:p-6"
                                    >
                                        {/* 分享解锁内容 */}
                                        <div className="text-center mb-4 sm:mb-6">
                                            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                                                <Share2 className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                                            </div>
                                            <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                                {translations.shareToUnlockFull || 'Share to Unlock for Free'}
                                            </h3>
                                            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                                                {translations.shareHelpText || "Share with friends and get 1 completion to unlock for free"}
                                            </p>
                                        </div>

                                        {/* 推荐统计 */}
                                        {stats && (
                                            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
                                                <div className="flex items-center justify-between text-xs sm:text-sm mb-2">
                                                    <div className="flex items-center space-x-2">
                                                        <Eye className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600" />
                                                        <span className="text-blue-800 dark:text-blue-200">
                                                            {translations.referralStats?.clicks || 'Clicks'}: {stats.totalClicks}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
                                                        <span className="text-green-800 dark:text-green-200">
                                                            {translations.referralStats?.completions || 'Completed'}: {stats.totalCompletions}
                                                        </span>
                                                    </div>
                                                </div>
                                                {stats.totalCompletions < 1 && (
                                                    <p className="text-xs text-blue-700 dark:text-blue-300">
                                                        {translations.referralStats?.needsCompletions?.replace('COUNT_PLACEHOLDER', '1') ||
                                                            'Need 1 more person to complete the test to unlock the report'}
                                                    </p>
                                                )}
                                            </div>
                                        )}

                                        {/* 分享按钮 */}
                                        <div className="space-y-3 sm:space-y-4">
                                            <button
                                                onClick={handleSystemShare}
                                                className="w-full bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white font-medium py-3 sm:py-4 px-4 sm:px-6 rounded-lg sm:rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
                                            >
                                                <Share2 className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3" />
                                                <span className="text-sm sm:text-base">
                                                    {isCopied ? (translations.linkCopied || 'Link Copied!') : translations.shareToUnlock}
                                                </span>
                                            </button>

                                            {/* 社交媒体分享 */}
                                            <div className="grid grid-cols-3 gap-2 sm:gap-3">
                                                <button
                                                    onClick={() => handleSocialShare('facebook')}
                                                    className="bg-blue-600 hover:bg-blue-700 text-white p-3 sm:p-4 rounded-lg sm:rounded-xl transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                >
                                                    <Facebook className="w-4 h-4 sm:w-5 sm:h-5" />
                                                </button>
                                                <button
                                                    onClick={() => handleSocialShare('twitter')}
                                                    className="bg-sky-500 hover:bg-sky-600 text-white p-3 sm:p-4 rounded-lg sm:rounded-xl transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                >
                                                    <Twitter className="w-4 h-4 sm:w-5 sm:h-5" />
                                                </button>
                                                <button
                                                    onClick={() => handleSocialShare('linkedin')}
                                                    className="bg-blue-700 hover:bg-blue-800 text-white p-3 sm:p-4 rounded-lg sm:rounded-xl transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
                                                >
                                                    <Linkedin className="w-4 h-4 sm:w-5 sm:h-5" />
                                                </button>
                                            </div>
                                        </div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    </motion.div>
                </div>
            )}

            {/* Stripe 支付模态框 */}
            {selectedPlan && (
                <PaymentModal
                    isOpen={showPaymentModal}
                    onClose={handlePaymentModalClose}
                    onSuccess={handlePaymentSuccess}
                    testId={id}
                    email={email}
                    amount={selectedPlan.price}
                    currency="usd"
                    userId={userId}
                    locale={locale}
                    stripePriceId={selectedPlan.stripePriceId}
                    translations={{
                        paymentTitle: translations.payment?.title || "Unlock Report",
                        paymentDescription: translations.payment?.description || "Secure payment to unlock your detailed report",
                        cardDetails: translations.payment?.cardDetails || "Card Information",
                        payButton: translations.payment?.payButton || "Pay",
                        processing: translations.payment?.processing || "Processing...",
                        cancel: translations.payment?.cancel || "Cancel",
                        paymentSuccess: translations.payment?.success || "Payment Successful!",
                        paymentError: translations.payment?.error || "Payment Failed"
                    }}
                />
            )}

            {/* 错误信息 */}
            {error && (
                <div className="fixed bottom-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50">
                    <p className="text-sm">{error}</p>
                </div>
            )}

            {/* 登录认证提示 */}
            {showAuthRequired && (
                <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/40 px-2 sm:px-0 animate-fade-in">
                    <div className="relative bg-white dark:bg-gray-900 border border-blue-100 dark:border-blue-800 rounded-2xl shadow-xl max-w-xs w-full flex flex-col items-center p-6 sm:p-8">
                        <button
                            className="absolute top-3 right-3 text-gray-400 hover:text-primary-500 text-2xl font-bold focus:outline-none"
                            onClick={() => setShowAuthRequired(false)}
                            aria-label="Close"
                        >
                            ×
                        </button>
                        <Lock className="w-10 h-10 text-primary-500 mb-3" />
                        <span className="font-heading text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 text-center">Sign in Required</span>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-5 text-center">Please sign in to unlock this feature and enjoy full access to your report and subscription benefits.</p>
                        <a
                            href={`/${locale === 'en' ? '' : locale + '/'}auth/signin?callbackUrl=${encodeURIComponent(`/${locale}/${type}/result/${id}${window?.location?.search || ''}`)}`}
                            className="w-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white font-semibold py-2.5 rounded-xl shadow-md transition-colors text-center text-base focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2"
                        >
                            Sign in to continue
                        </a>
                    </div>
                </div>
            )}
        </div>
    );
} 