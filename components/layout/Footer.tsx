import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';
import Image from 'next/image';

export default function Footer() {
    const t = useTranslations('components.footer');
    const navT = useTranslations('navigation');

    return (
        <footer className="w-full bg-gray-50 border-t border-gray-100">
            <div className="container mx-auto px-4 py-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="flex flex-col items-center md:items-start">
                        <Link href={"/" as Pathnames} className="flex items-center group">
                            <div className="relative w-10 h-10">
                                <Image 
                                    src="/logo.webp" 
                                    alt="RaadsTest Logo" 
                                    fill
                                    className="object-contain"
                                />
                            </div>
                            <span className="ml-2.5 text-lg font-bold text-blue-600">
                                Raadstest.com
                            </span>
                        </Link>
                        <p className="mt-3 text-sm text-gray-500 text-center md:text-left max-w-xs">
                            {t('copyright')}
                        </p>
                    </div>
                    
                    <div className="flex flex-col items-center">
                        <h3 className="font-medium text-gray-800 mb-3">{navT('quickLinks')}</h3>
                        <div className="flex flex-col space-y-2">
                            <Link href={"/adhd" as Pathnames} className="text-sm text-gray-500 hover:text-blue-600 transition-colors">
                                {navT('adhdTest')}
                            </Link>
                            <Link href={"/autism" as Pathnames} className="text-sm text-gray-500 hover:text-blue-600 transition-colors">
                                {navT('autismTest')}
                            </Link>
                        </div>
                    </div>
                    
                    <div className="flex flex-col items-center md:items-end">
                        <h3 className="font-medium text-gray-800 mb-3">{navT('legal')}</h3>
                        <div className="flex flex-col space-y-2">
                            <a href="https://raadstest.com/blog/disclaimer/"
                                className="text-sm text-gray-500 hover:text-blue-600 transition-colors">
                                {t('links.privacyPolicy')}
                            </a>
                            <a href="https://raadstest.com/blog/private-policy/"
                                className="text-sm text-gray-500 hover:text-blue-600 transition-colors">
                                {t('links.termsOfService')}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
}