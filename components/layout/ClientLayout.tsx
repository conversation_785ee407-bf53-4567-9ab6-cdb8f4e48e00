"use client";

import { ReactNode } from 'react';
import { usePathname } from 'next/navigation';

interface ClientLayoutProps {
  children: ReactNode;
  header: ReactNode;
  footer: ReactNode;
}

export default function ClientLayout({ children, header, footer }: ClientLayoutProps) {
  const pathname = usePathname();
  const isIframePath = pathname.includes('/i/');
  console.log('Current pathname:', pathname, 'isIframePath:', isIframePath);
  
  return (
    <>
      {!isIframePath && <header>{header}</header>}
      <main className="flex-grow flex flex-col">
        {children}
      </main>
      {!isIframePath && <footer>{footer}</footer>}
    </>
  );
} 