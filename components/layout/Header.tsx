'use client'

import { Link, Pathnames } from '@/i18n/routing';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import LanguageSelector from '@/components/shared/LanguageSelector';
import UserMenu from '@/components/UserMenu';
import Image from 'next/image';
import { useParams } from 'next/navigation';

export default function Header() {
    const [isVisible, setIsVisible] = useState(false);
    const [scrolled, setScrolled] = useState(false);
    const params = useParams();
    const locale = params?.locale as string || 'en';

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 100);

        const handleScroll = () => {
            setScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        handleScroll(); // 初始检查

        return () => {
            clearTimeout(timer);
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.header
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                        duration: 0.3,
                        ease: "easeOut"
                    }}
                    className={`fixed top-0 left-0 right-0 z-50 backdrop-blur-lg bg-white/95 shadow-sm border-b border-gray-100 transition-all duration-300 header-responsive ${
                        scrolled ? 'scrolled py-2 sm:py-2' : 'py-3 sm:py-3'
                    }`}
                >
                    <div className="container mx-auto px-4">
                        <nav className="flex justify-between items-center">
                            <Link href={"/" as Pathnames} className="flex items-center group">
                                <div className="flex items-center">
                                    <div className={`relative ${scrolled ? 'w-8 h-8 sm:w-9 sm:h-9' : 'w-9 h-9 sm:w-10 sm:h-10'} transition-all duration-300`}>
                                        <Image 
                                            src="/logo.webp" 
                                            alt="RaadsTest Logo" 
                                            fill
                                            className="object-contain"
                                            priority
                                        />
                                    </div>
                                    <span className={`ml-2 sm:ml-2.5 ${scrolled ? 'text-base sm:text-lg' : 'text-lg sm:text-xl'} font-bold text-blue-600 group-hover:text-blue-700 transition-all duration-300`}>
                                        Raadstest.com
                                    </span>
                                </div>
                            </Link>
                            <div className="flex items-center space-x-3 sm:space-x-4">
                                <UserMenu locale={locale} />
                                <LanguageSelector />
                            </div>
                        </nav>
                    </div>
                </motion.header>
            )}
        </AnimatePresence>
    );
}