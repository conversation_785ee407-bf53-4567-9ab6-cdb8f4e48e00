'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';

interface ReportBindBannerProps {
  locale: string;
  testId: string;
  token: string;
}

export default function ReportBindBanner({ locale, testId, token }: ReportBindBannerProps) {
  const t = useTranslations('reportBindBanner');
  const [isBinding, setIsBinding] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 绑定报告到当前用户
  const handleBind = async () => {
    setIsBinding(true);
    setError(null);
    
    try {
      const response = await fetch('/api/bind-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testId, token }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || t('errors.bindFailed'));
      }
      
      setSuccess(true);
      
      // 绑定成功后刷新页面，更新URL（移除token参数）
      setTimeout(() => {
        window.location.href = `/${locale === 'en' ? '' : locale + '/'}report/${testId}`;
      }, 2000);
    } catch (err) {
      console.error('绑定报告出错:', err);
      setError(typeof err === 'string' ? err : (err instanceof Error ? err.message : t('errors.bindFailed')));
    } finally {
      setIsBinding(false);
    }
  };
  
  // 绑定成功后显示成功消息
  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center"
      >
        <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <div className="flex-grow">
          <h3 className="text-lg font-semibold text-green-800">{t('success.title')}</h3>
          <p className="text-green-600">{t('success.description')}</p>
        </div>
      </motion.div>
    );
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"
    >
      <div className="flex flex-col sm:flex-row items-start sm:items-center">
        <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4 mb-3 sm:mb-0">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div className="flex-grow mb-3 sm:mb-0">
          <h3 className="text-lg font-semibold text-blue-800">{t('title')}</h3>
          <p className="text-blue-600">{t('description')}</p>
          
          {/* 显示错误消息 */}
          {error && (
            <p className="mt-2 text-red-600 text-sm">{error}</p>
          )}
        </div>
        <div className="flex-shrink-0 w-full sm:w-auto sm:ml-4">
          <button
            onClick={handleBind}
            disabled={isBinding}
            className={`w-full sm:w-auto px-4 py-2 rounded-lg ${
              isBinding 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          >
            {isBinding ? t('binding') : t('bindButton')}
          </button>
        </div>
      </div>
    </motion.div>
  );
} 