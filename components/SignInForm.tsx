'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';

interface SignInFormProps {
  locale: string;
  callbackUrl?: string;
  error?: string;
  testId?: string;
  token?: string;
}

export default function SignInForm({ locale, callbackUrl, error, testId, token }: SignInFormProps) {
  const t = useTranslations('pages.auth.signin');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(error || null);
  const [emailSent, setEmailSent] = useState(false);

  // 确保回调 URL 是有效的，默认为首页
  const finalCallbackUrl = callbackUrl || `/${locale === 'en' ? '' : locale}`;

  // 处理邮箱登录 - 使用自定义 Resend API
  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !email.includes('@')) {
      setFormError(t('errors.invalidEmail'));
      return;
    }

    setIsSubmitting(true);
    setFormError(null);
    setEmailSent(false);

    try {
      // 添加测试ID和令牌到回调URL
      let targetCallbackUrl = finalCallbackUrl;
      if (testId && token) {
        const separator = targetCallbackUrl.includes('?') ? '&' : '?';
        targetCallbackUrl = `${targetCallbackUrl}${separator}testId=${testId}&token=${token}`;
      }

      // 使用自定义 API 发送邮件
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          callbackUrl: targetCallbackUrl,
          locale
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || '发送邮件失败');
      }

      setEmailSent(true);
      setIsSubmitting(false);
    } catch (err) {
      console.error('登录错误:', err);
      setFormError(err instanceof Error ? err.message : t('errors.general'));
      setIsSubmitting(false);
    }
  };

  // 处理 NextAuth 邮箱登录（备用方案）
  const handleNextAuthEmailSignIn = async () => {
    if (!email || !email.includes('@')) {
      setFormError(t('errors.invalidEmail'));
      return;
    }

    setIsSubmitting(true);
    setFormError(null);

    try {
      // 添加测试ID和令牌到回调URL
      let targetCallbackUrl = finalCallbackUrl;
      if (testId && token) {
        const separator = targetCallbackUrl.includes('?') ? '&' : '?';
        targetCallbackUrl = `${targetCallbackUrl}${separator}testId=${testId}&token=${token}`;
      }

      await signIn('email', {
        email,
        callbackUrl: targetCallbackUrl,
        redirect: true
      });
    } catch (err) {
      console.error('登录错误:', err);
      setFormError(t('errors.general'));
      setIsSubmitting(false);
    }
  };

  // 处理 Google 登录
  const handleGoogleSignIn = async () => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      // 添加测试ID和令牌到回调URL
      let targetCallbackUrl = finalCallbackUrl;
      if (testId && token) {
        const separator = targetCallbackUrl.includes('?') ? '&' : '?';
        targetCallbackUrl = `${targetCallbackUrl}${separator}testId=${testId}&token=${token}`;
      }

      await signIn('google', {
        callbackUrl: targetCallbackUrl,
        redirect: true
      });
    } catch (err) {
      console.error('Google 登录错误:', err);
      setFormError(t('errors.general'));
      setIsSubmitting(false);
    }
  };

  return (
    <div className="mt-8 bg-white py-8 px-4 sm:px-6 lg:px-8 shadow rounded-lg">
      {/* 成功提示 */}
      {emailSent && (
        <div className="mb-4 bg-green-50 text-green-700 p-3 rounded border border-green-200 text-sm">
          <p className="font-medium">{t('success.emailSentTitle')}</p>
          <p>{t('success.emailSentMessage', { email })}</p>
        </div>
      )}

      {/* 错误提示 */}
      {formError && (
        <div className="mb-4 bg-red-50 text-red-700 p-3 rounded border border-red-200 text-sm">
          {formError}
        </div>
      )}

      {/* 社交登录 */}
      <div className="space-y-4">
        <button
          type="button"
          onClick={handleGoogleSignIn}
          disabled={isSubmitting}
          className="w-full flex justify-center items-center gap-3 px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed"
        >
          <Image src="/images/auth/google-logo.svg" alt="Google" width={18} height={18} />
          <span>{t('buttons.google')}</span>
        </button>
      </div>

      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">
              {t('orLabel')}
            </span>
          </div>
        </div>
      </div>

      {/* 邮箱登录表单 */}
      <form className="mt-6 space-y-6" onSubmit={handleEmailSignIn}>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            {t('emailLabel')}
          </label>
          <div className="mt-1">
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              placeholder={t('emailPlaceholder')}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isSubmitting || emailSent}
              className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:opacity-70 disabled:bg-gray-100"
            />
          </div>
        </div>

        <div>
          <button
            type="submit"
            disabled={isSubmitting || emailSent}
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isSubmitting ? t('buttons.processing') : emailSent ? t('buttons.emailSent') : t('buttons.emailLink')}
          </button>
        </div>
      </form>

      {/* 重新发送邮件选项 */}
      {emailSent && (
        <div className="mt-4 text-center">
          <button
            type="button"
            onClick={() => {
              setEmailSent(false);
              setFormError(null);
            }}
            className="text-sm text-blue-600 hover:text-blue-500 underline"
          >
            {t('resend.noEmailReceived')}
          </button>
        </div>
      )}

      {/* 底部链接 */}
      <div className="mt-6 text-center text-sm">
        <p className="text-gray-600">
          {t('termsNotice')}{' '}
          <Link
            href={`/${locale === 'en' ? '' : locale}/terms`}
            className="font-medium text-blue-600 hover:text-blue-500"
          >
            {t('termsLink')}
          </Link>
        </p>
      </div>
    </div>
  );
} 