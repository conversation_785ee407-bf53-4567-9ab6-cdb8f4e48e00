'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';

interface StreamingReportContentProps {
    content: string;
    isStreaming?: boolean;
    streamingSpeed?: number; // Characters per interval
    streamingInterval?: number; // Milliseconds between updates
    onStreamingComplete?: () => void;
    className?: string;
}

export default function StreamingReportContent({
    content,
    isStreaming = false,
    streamingSpeed = 50,
    streamingInterval = 100,
    onStreamingComplete,
    className = ''
}: StreamingReportContentProps) {
    const [displayedContent, setDisplayedContent] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isComplete, setIsComplete] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    // Reset when content changes
    useEffect(() => {
        if (isStreaming) {
            setDisplayedContent('');
            setCurrentIndex(0);
            setIsComplete(false);
        } else {
            setDisplayedContent(content);
            setIsComplete(true);
        }
    }, [content, isStreaming]);

    // Streaming effect
    useEffect(() => {
        if (!isStreaming || isComplete || currentIndex >= content.length) {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            if (!isComplete && currentIndex >= content.length) {
                setIsComplete(true);
                onStreamingComplete?.();
            }
            return;
        }

        intervalRef.current = setInterval(() => {
            setCurrentIndex(prevIndex => {
                const nextIndex = Math.min(prevIndex + streamingSpeed, content.length);
                const newContent = content.substring(0, nextIndex);
                setDisplayedContent(newContent);

                if (nextIndex >= content.length) {
                    setIsComplete(true);
                    onStreamingComplete?.();
                }

                return nextIndex;
            });
        }, streamingInterval);

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, [isStreaming, currentIndex, content, content.length, streamingSpeed, streamingInterval, onStreamingComplete, isComplete]);

    // Parse content into sections for better animation
    const parseContentSections = (text: string) => {
        const lines = text.split('\n');
        const sections: { type: 'header' | 'content', text: string, level?: number }[] = [];
        
        for (const line of lines) {
            if (line.startsWith('#')) {
                const level = line.match(/^#+/)?.[0].length || 1;
                sections.push({ type: 'header', text: line, level });
            } else if (line.trim()) {
                sections.push({ type: 'content', text: line });
            }
        }
        
        return sections;
    };

    const sections = parseContentSections(displayedContent);

    return (
        <div className={`relative ${className}`}>
            <AnimatePresence>
                {isStreaming && !isComplete && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="absolute top-0 right-0 z-10"
                    >
                        <div className="flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 px-3 py-1 rounded-full border border-blue-200 dark:border-blue-800">
                            <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                            </div>
                            <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                                Generating...
                            </span>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            <div className="prose dark:prose-invert max-w-none">
                {isStreaming ? (
                    <div className="space-y-4">
                        {sections.map((section, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ 
                                    duration: 0.3,
                                    delay: index * 0.1,
                                    ease: "easeOut"
                                }}
                            >
                                <ReactMarkdown
                                    rehypePlugins={[rehypeRaw, rehypeHighlight]}
                                    remarkPlugins={[remarkGfm]}
                                >
                                    {section.text}
                                </ReactMarkdown>
                            </motion.div>
                        ))}
                        
                        {/* Typing cursor effect */}
                        {!isComplete && (
                            <motion.span
                                animate={{ opacity: [1, 0] }}
                                transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
                                className="inline-block w-0.5 h-5 bg-blue-500 ml-1"
                            />
                        )}
                    </div>
                ) : (
                    <ReactMarkdown
                        rehypePlugins={[rehypeRaw, rehypeHighlight]}
                        remarkPlugins={[remarkGfm]}
                    >
                        {displayedContent}
                    </ReactMarkdown>
                )}
            </div>
        </div>
    );
}
