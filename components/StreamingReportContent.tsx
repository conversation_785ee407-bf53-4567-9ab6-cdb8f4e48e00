'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { Brain, CheckCircle, Zap } from 'lucide-react';

interface StreamingReportContentProps {
    content: string;
    isStreaming?: boolean;
    streamingSpeed?: number; // Characters per interval
    streamingInterval?: number; // Milliseconds between updates
    onStreamingComplete?: () => void;
    className?: string;
    showProgressIndicator?: boolean;
    progressTitle?: string;
}

export default function StreamingReportContent({
    content,
    isStreaming = false,
    streamingSpeed = 80, // Faster default speed
    streamingInterval = 50, // Faster interval
    onStreamingComplete,
    className = '',
    showProgressIndicator = true,
    progressTitle = 'Generating Report'
}: StreamingReportContentProps) {
    const [displayedContent, setDisplayedContent] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isComplete, setIsComplete] = useState(false);
    const [showCompletionMessage, setShowCompletionMessage] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const completionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Reset when content changes
    useEffect(() => {
        if (isStreaming) {
            setDisplayedContent('');
            setCurrentIndex(0);
            setIsComplete(false);
            setShowCompletionMessage(false);
        } else {
            setDisplayedContent(content);
            setIsComplete(true);
            setShowCompletionMessage(false);
        }
    }, [content, isStreaming]);

    // Handle completion message timing
    useEffect(() => {
        if (isComplete && isStreaming) {
            setShowCompletionMessage(true);
            completionTimeoutRef.current = setTimeout(() => {
                setShowCompletionMessage(false);
                onStreamingComplete?.();
            }, 3000); // Show completion message for 3 seconds
        }

        return () => {
            if (completionTimeoutRef.current) {
                clearTimeout(completionTimeoutRef.current);
                completionTimeoutRef.current = null;
            }
        };
    }, [isComplete, isStreaming, onStreamingComplete]);

    // Streaming effect
    useEffect(() => {
        if (!isStreaming || isComplete || currentIndex >= content.length) {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            if (!isComplete && currentIndex >= content.length) {
                setIsComplete(true);
                onStreamingComplete?.();
            }
            return;
        }

        intervalRef.current = setInterval(() => {
            setCurrentIndex(prevIndex => {
                const nextIndex = Math.min(prevIndex + streamingSpeed, content.length);
                const newContent = content.substring(0, nextIndex);
                setDisplayedContent(newContent);

                if (nextIndex >= content.length) {
                    setIsComplete(true);
                    onStreamingComplete?.();
                }

                return nextIndex;
            });
        }, streamingInterval);

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, [isStreaming, currentIndex, content, content.length, streamingSpeed, streamingInterval, onStreamingComplete, isComplete]);

    // Parse content into sections for better animation
    const parseContentSections = (text: string) => {
        const lines = text.split('\n');
        const sections: { type: 'header' | 'content', text: string, level?: number }[] = [];
        
        for (const line of lines) {
            if (line.startsWith('#')) {
                const level = line.match(/^#+/)?.[0].length || 1;
                sections.push({ type: 'header', text: line, level });
            } else if (line.trim()) {
                sections.push({ type: 'content', text: line });
            }
        }
        
        return sections;
    };

    const sections = parseContentSections(displayedContent);

    return (
        <div className={`relative ${className}`}>
            {/* Enhanced Progress Indicator */}
            <AnimatePresence>
                {isStreaming && showProgressIndicator && (
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-xs sm:max-w-sm mx-4"
                    >
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 px-3 sm:px-6 py-2 sm:py-4 flex items-center space-x-2 sm:space-x-4">
                            <div className="flex items-center space-x-2 sm:space-x-3">
                                <motion.div
                                    animate={{ rotate: 360 }}
                                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                                >
                                    <Brain className="w-4 h-4 sm:w-6 sm:h-6 text-blue-500" />
                                </motion.div>
                                <div className="min-w-0">
                                    <div className="text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                                        {progressTitle}
                                    </div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                        AI is analyzing your results...
                                    </div>
                                </div>
                            </div>
                            <div className="flex space-x-1">
                                <motion.div
                                    animate={{ scale: [1, 1.2, 1] }}
                                    transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                                    className="w-2 h-2 bg-blue-500 rounded-full"
                                />
                                <motion.div
                                    animate={{ scale: [1, 1.2, 1] }}
                                    transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                                    className="w-2 h-2 bg-blue-500 rounded-full"
                                />
                                <motion.div
                                    animate={{ scale: [1, 1.2, 1] }}
                                    transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                                    className="w-2 h-2 bg-blue-500 rounded-full"
                                />
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Completion Message */}
            <AnimatePresence>
                {showCompletionMessage && (
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
                    >
                        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-800 px-6 py-4 flex items-center space-x-3">
                            <CheckCircle className="w-6 h-6 text-green-500" />
                            <div>
                                <div className="text-sm font-medium text-green-900 dark:text-green-100">
                                    Report Generated Successfully!
                                </div>
                                <div className="text-xs text-green-600 dark:text-green-400">
                                    Your personalized analysis is ready
                                </div>
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            <div className="prose dark:prose-invert max-w-none">
                {isStreaming ? (
                    <div className="space-y-4">
                        {sections.map((section, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ 
                                    duration: 0.3,
                                    delay: index * 0.1,
                                    ease: "easeOut"
                                }}
                            >
                                <ReactMarkdown
                                    rehypePlugins={[rehypeRaw, rehypeHighlight]}
                                    remarkPlugins={[remarkGfm]}
                                >
                                    {section.text}
                                </ReactMarkdown>
                            </motion.div>
                        ))}
                        
                        {/* Typing cursor effect */}
                        {!isComplete && (
                            <motion.span
                                animate={{ opacity: [1, 0] }}
                                transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
                                className="inline-block w-0.5 h-5 bg-blue-500 ml-1"
                            />
                        )}
                    </div>
                ) : (
                    <ReactMarkdown
                        rehypePlugins={[rehypeRaw, rehypeHighlight]}
                        remarkPlugins={[remarkGfm]}
                    >
                        {displayedContent}
                    </ReactMarkdown>
                )}
            </div>
        </div>
    );
}
