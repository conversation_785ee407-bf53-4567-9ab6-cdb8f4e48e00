'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { MessageCircle, X, Sparkles, EyeOff } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    shouldShowIntroduction,
    recordIntroductionView,
    disableIntroductionFor7Days,
    shouldShowDisableOption,
    type IntroductionState
} from '@/utils/introductionManager';

interface ChatIntroductionProps {
    locale: string;
}

const STORAGE_KEY = 'chat-intro-mobile';

export default function ChatIntroduction({ locale }: ChatIntroductionProps) {
    const t = useTranslations('chatConsultation');
    const [isVisible, setIsVisible] = useState(false);
    const [introState, setIntroState] = useState<IntroductionState>({ viewCount: 0, lastViewTime: 0 });
    const [showDisableOption, setShowDisableOption] = useState(false);

    useEffect(() => {
        // 检查是否应该显示引导提示
        if (shouldShowIntroduction(STORAGE_KEY)) {
            // 延迟3秒显示介绍
            const timer = setTimeout(() => {
                // 记录一次查看并更新状态
                const newState = recordIntroductionView(STORAGE_KEY);
                setIntroState(newState);
                setShowDisableOption(shouldShowDisableOption(newState.viewCount));
                setIsVisible(true);
            }, 3000);

            return () => clearTimeout(timer);
        }
    }, []);

    const handleClose = () => {
        setIsVisible(false);
    };

    const handleTryNow = () => {
        setIsVisible(false);
        // 触发聊天窗口打开
        const chatButton = document.querySelector('[data-chat-button]') as HTMLButtonElement;
        if (chatButton) {
            chatButton.click();
        }
    };

    const handleDisableFor7Days = () => {
        disableIntroductionFor7Days(STORAGE_KEY);
        setIsVisible(false);
    };

    return (
        <AnimatePresence>
            {isVisible && (
                <>
                    {/* 背景遮罩 */}
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black bg-opacity-50 z-50 xl:hidden"
                        onClick={handleClose}
                    />

                    {/* 介绍卡片 */}
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.8, y: 20 }}
                        className="fixed bottom-24 right-6 max-w-80 bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 z-50 xl:hidden"
                    >
                        {/* 关闭按钮 */}
                        <button
                            onClick={handleClose}
                            className="absolute top-4 right-4 p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
                            aria-label={locale === 'zh' ? '关闭' : 'Close'}
                        >
                            <X className="w-4 h-4 text-gray-500" />
                        </button>

                        {/* 图标和标题区域 - 优化布局 */}
                        <div className="text-center mb-5">
                            {/* 主图标 */}
                            <div className="relative inline-flex mb-4">
                                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                                    <MessageCircle className="w-8 h-8 text-white" />
                                </div>
                                {/* 装饰性星星图标 */}
                                <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center shadow-md">
                                    <Sparkles className="w-3.5 h-3.5 text-white" />
                                </div>
                            </div>

                            {/* 标题和副标题 */}
                            <div>
                                <h3 className="text-lg font-bold text-gray-900 mb-1">
                                    {locale === 'zh' ? 'AI 咨询助手' : 'AI Consultation'}
                                </h3>
                                <p className="text-sm text-gray-500 font-medium">
                                    {locale === 'zh' ? '基于您的测试结果提供个性化咨询' : 'Personalized consultation based on your test results'}
                                </p>
                            </div>
                        </div>

                        {/* 描述文本 */}
                        <p className="text-sm text-gray-600 mb-5 leading-relaxed text-center">
                            {locale === 'zh'
                                ? '点击右下角的聊天按钮，获得基于您测试结果的个性化AI咨询。我们的AI助手可以帮助您更好地理解测试结果并提供专业建议。'
                                : 'Click the chat button in the bottom right to get personalized AI consultation based on your test results. Our AI assistant can help you better understand your results and provide professional advice.'
                            }
                        </p>

                        {/* 按钮区域 */}
                        <div className="space-y-3">
                            {/* 主要操作按钮 */}
                            <div className="flex gap-3">
                                <button
                                    onClick={handleTryNow}
                                    className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-xl text-sm font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                                >
                                    {locale === 'zh' ? '立即体验' : 'Try Now'}
                                </button>
                                <button
                                    onClick={handleClose}
                                    className="px-4 py-3 text-gray-500 text-sm font-medium hover:text-gray-700 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 rounded-xl"
                                >
                                    {locale === 'zh' ? '稍后' : 'Later'}
                                </button>
                            </div>

                            {/* 不再提示按钮 - 仅在查看3次后显示 */}
                            {showDisableOption && (
                                <motion.button
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    onClick={handleDisableFor7Days}
                                    className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-gray-50 text-gray-600 rounded-xl text-xs font-medium hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 border border-gray-200"
                                >
                                    <EyeOff className="w-3.5 h-3.5" />
                                    {locale === 'zh' ? '7天内不再提示' : "Don't show for 7 days"}
                                </motion.button>
                            )}
                        </div>

                        {/* 指示箭头 */}
                        <div className="absolute -bottom-2 right-8 w-4 h-4 bg-white border-r border-b border-gray-200 transform rotate-45"></div>
                    </motion.div>
                </>
            )}
        </AnimatePresence>
    );
} 