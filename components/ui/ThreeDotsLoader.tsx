import React from 'react';

export default function ThreeDotsLoader({ text }: { text?: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <div className="flex space-x-2 mb-4">
        <span className="dot bg-emerald-500 dark:bg-emerald-400"></span>
        <span className="dot bg-emerald-500 dark:bg-emerald-400"></span>
        <span className="dot bg-emerald-500 dark:bg-emerald-400"></span>
      </div>
      {text && (
        <p className="text-gray-600 dark:text-gray-300 text-center text-base md:text-lg max-w-xs md:max-w-md">
          {text}
        </p>
      )}
      <style jsx>{`
        .dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          display: inline-block;
          animation: bounce 1.4s infinite both;
        }
        .dot:nth-child(2) { animation-delay: 0.2s; }
        .dot:nth-child(3) { animation-delay: 0.4s; }
        @keyframes bounce {
          0%, 80%, 100% { transform: scale(0.7); opacity: 0.5; }
          40% { transform: scale(1.2); opacity: 1; }
        }
      `}</style>
    </div>
  );
} 