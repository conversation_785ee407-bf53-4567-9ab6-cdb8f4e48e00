'use client';

import { TestResult } from '@prisma/client';
import ChatConsultation from './ChatConsultation';
import ReportSidebar from './ReportSidebar';
import ChatIntroduction from './ChatIntroduction';
import SidebarIntroduction from './SidebarIntroduction';

interface ChatConsultationContainerProps {
    testResult: TestResult;
    locale: string;
}

export default function ChatConsultationContainer({ testResult, locale }: ChatConsultationContainerProps) {
    return (
        <>
            {/* 移动端和中等屏幕的浮动聊天窗口 */}
            <ChatConsultation testResult={testResult} locale={locale} />

            {/* 大屏幕的侧边栏聊天 */}
            <ReportSidebar testResult={testResult} locale={locale} />

            {/* 功能介绍 */}
            <ChatIntroduction locale={locale} />

            {/* 侧边栏功能介绍 */}
            <SidebarIntroduction locale={locale} />
        </>
    );
} 