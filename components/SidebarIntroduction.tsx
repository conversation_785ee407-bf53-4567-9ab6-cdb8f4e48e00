'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON>, <PERSON><PERSON><PERSON>, ArrowR<PERSON>, EyeOff, MessageCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    shouldShowIntroduction,
    recordIntroductionView,
    disableIntroductionFor7Days,
    shouldShowDisableOption,
    type IntroductionState
} from '@/utils/introductionManager';

interface SidebarIntroductionProps {
    locale: string;
}

const STORAGE_KEY = 'chat-intro-sidebar';

export default function SidebarIntroduction({ locale }: SidebarIntroductionProps) {
    const t = useTranslations('chatConsultation');
    const [isVisible, setIsVisible] = useState(false);
    const [introState, setIntroState] = useState<IntroductionState>({ viewCount: 0, lastViewTime: 0 });
    const [showDisableOption, setShowDisableOption] = useState(false);
    const [scrolled, setScrolled] = useState(false);

    useEffect(() => {
        // 监听滚动状态以动态调整位置
        const handleScroll = () => {
            setScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        handleScroll(); // 初始检查

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    useEffect(() => {
        // 检查是否应该显示引导提示
        if (shouldShowIntroduction(STORAGE_KEY)) {
            // 延迟2秒显示介绍
            const timer = setTimeout(() => {
                // 记录一次查看并更新状态
                const newState = recordIntroductionView(STORAGE_KEY);
                setIntroState(newState);
                setShowDisableOption(shouldShowDisableOption(newState.viewCount));
                setIsVisible(true);
            }, 2000);

            return () => clearTimeout(timer);
        }
    }, []);

    const handleClose = () => {
        setIsVisible(false);
    };

    const handleDisableFor7Days = () => {
        disableIntroductionFor7Days(STORAGE_KEY);
        setIsVisible(false);
    };

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="fixed right-[25rem] max-w-80 bg-white rounded-xl shadow-lg border border-blue-200 p-5 z-45 hidden xl:block"
                    style={{
                        top: scrolled ? '84px' : '92px' // 动态适应header高度 + 一些间距
                    }}
                >
                    {/* 关闭按钮 */}
                    <button
                        onClick={handleClose}
                        className="absolute top-3 right-3 p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
                        aria-label={locale === 'zh' ? '关闭' : 'Close'}
                    >
                        <X className="w-4 h-4 text-gray-500" />
                    </button>

                    {/* 内容区域 */}
                    <div className="pr-8">
                        {/* 图标和标题区域 - 优化布局 */}
                        <div className="flex items-start gap-3 mb-4">
                            {/* 主图标 */}
                            <div className="relative flex-shrink-0">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
                                    <MessageCircle className="w-6 h-6 text-white" />
                                </div>
                                {/* 装饰性星星图标 */}
                                <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center shadow-sm">
                                    <Sparkles className="w-3 h-3 text-white" />
                                </div>
                            </div>

                            {/* 标题和副标题 */}
                            <div className="flex-1 min-w-0">
                                <h3 className="font-bold text-gray-900 text-sm mb-1">
                                    {locale === 'zh' ? 'AI 咨询功能' : 'AI Consultation'}
                                </h3>
                                <p className="text-xs text-gray-500 font-medium leading-relaxed">
                                    {locale === 'zh' ? '基于测试结果的个性化咨询' : 'Personalized consultation based on results'}
                                </p>
                            </div>
                        </div>

                        {/* 描述文本 */}
                        <p className="text-xs text-gray-600 mb-4 leading-relaxed">
                            {locale === 'zh'
                                ? '右侧面板中的AI助手可以根据您的测试结果提供个性化咨询和建议。'
                                : 'The AI assistant in the right panel can provide personalized consultation based on your test results.'
                            }
                        </p>

                        {/* 操作提示 */}
                        <div className="flex items-center gap-2 text-blue-600 mb-4">
                            <span className="text-xs font-semibold">
                                {locale === 'zh' ? '点击推荐问题开始' : 'Click suggested questions to start'}
                            </span>
                            <ArrowRight className="w-3 h-3 flex-shrink-0" />
                        </div>

                        {/* 不再提示按钮 - 仅在查看3次后显示 */}
                        {showDisableOption && (
                            <motion.button
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                onClick={handleDisableFor7Days}
                                className="w-full flex items-center justify-center gap-2 px-3 py-2 bg-gray-50 text-gray-600 rounded-lg text-xs font-medium hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 border border-gray-200"
                            >
                                <EyeOff className="w-3 h-3" />
                                {locale === 'zh' ? '7天内不再提示' : "Don't show for 7 days"}
                            </motion.button>
                        )}
                    </div>

                    {/* 指示箭头 - 指向右侧AI面板 */}
                    <div className="absolute top-8 right-0 w-3 h-3 bg-white border-t border-r border-blue-200 transform rotate-45 translate-x-1/2"></div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} 