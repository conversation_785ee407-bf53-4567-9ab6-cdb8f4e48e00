'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { TestResult } from '@prisma/client';
import ReactMarkdown from 'react-markdown';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface ReportViewerProps {
  testResult: TestResult;
  locale: string;
}

export default function ReportViewer({ testResult, locale }: ReportViewerProps) {
  const t = useTranslations('reportViewer');
  const [report, setReport] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 从API获取报告
  useEffect(() => {
    const fetchReport = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/autism/get-report?id=${testResult.id}&locale=${locale}`);
        
        if (!response.ok) {
          throw new Error(`获取报告出错: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
          setError(data.error);
          setLoading(false);
          return;
        }
        
        setReport(data.report);
      } catch (err) {
        console.error('获取报告失败:', err);
        setError(t('errors.fetchFailed'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchReport();
  }, [testResult.id, locale, t]);
  
  // 报告加载中状态
  if (loading) {
    return (
      <div className="p-8 flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-600">{t('loading')}</p>
      </div>
    );
  }
  
  // 报告错误状态
  if (error) {
    return (
      <div className="p-8 flex flex-col items-center justify-center min-h-[300px] bg-red-50 text-red-700 rounded-lg">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <p className="text-center font-medium">{error}</p>
        <p className="mt-4 text-sm">{t('errors.retry')}</p>
      </div>
    );
  }
  
  // 报告已锁定状态
  if (testResult.isReportLocked) {
    return (
      <div className="p-8">
        <div className="flex flex-col items-center justify-center min-h-[300px] text-center">
          <div className="w-24 h-24 mb-6 relative">
            <Image 
              src="/images/report/locked-report.svg" 
              alt="Locked Report"
              fill
              className="object-contain"
            />
          </div>
          <h2 className="text-xl font-bold mb-3">{t('locked.title')}</h2>
          <p className="text-gray-600 max-w-md mb-6">{t('locked.description')}</p>
          <button
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            onClick={() => window.location.href = `/checkout?testId=${testResult.id}`}
          >
            {t('locked.unlockButton')}
          </button>
        </div>
      </div>
    );
  }
  
  // 报告为空状态
  if (!report) {
    return (
      <div className="p-8 flex flex-col items-center justify-center min-h-[300px]">
        <div className="w-24 h-24 mb-6 relative">
          <Image 
            src="/images/report/empty-report.svg" 
            alt="Empty Report"
            fill
            className="object-contain"
          />
        </div>
        <h2 className="text-xl font-bold mb-3">{t('empty.title')}</h2>
        <p className="text-gray-600 max-w-md text-center">{t('empty.description')}</p>
      </div>
    );
  }
  
  // 显示报告内容
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-8"
    >
      <div className="prose prose-blue max-w-none">
        <ReactMarkdown>{report}</ReactMarkdown>
      </div>
    </motion.div>
  );
} 