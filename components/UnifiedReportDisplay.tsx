'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import StreamingReportContent from './StreamingReportContent';
import LockedReportUnlock from './LockedReportUnlock';
import AdhdAdultVisualization from './visualizations/AdhdAdultVisualization';
import AdhdChildVisualization from './visualizations/AdhdChildVisualization';
import RaadsrVisualization from './visualizations/RaadsrVisualization';
import Aq10Visualization from './visualizations/Aq10Visualization';
import ReportTemplate from './ReportTemplate';
import ErrorReportModal from './ErrorReportModal';

interface UnifiedReportDisplayProps {
    // Test data
    testId: string;
    testType: string;
    answers?: Record<number, number>;
    scores?: any;
    score?: number; // For AQ-10
    
    // Report content
    report: string;
    isReportLocked: boolean;
    
    // Streaming state
    isStreaming?: boolean;
    justUnlocked?: boolean;
    onStreamingComplete?: () => void;
    
    // Unlock functionality
    onUnlock: () => void;
    translations: any;
    
    // User info
    email?: string;
    userId?: string;
    locale: string;
    
    // Type classification
    reportType: 'adhd' | 'autism';
}

export default function UnifiedReportDisplay({
    testId,
    testType,
    answers,
    scores,
    score,
    report,
    isReportLocked,
    isStreaming = false,
    justUnlocked = false,
    onStreamingComplete,
    onUnlock,
    translations,
    email,
    userId,
    locale,
    reportType
}: UnifiedReportDisplayProps) {
    const [realContent, setRealContent] = useState<string>('');
    const [isLoadingContent, setIsLoadingContent] = useState(true);
    const [contentError, setContentError] = useState<string | null>(null);
    const [showErrorModal, setShowErrorModal] = useState(false);

    // Fetch real content for preview (both locked and unlocked states)
    useEffect(() => {
        const fetchRealContent = async () => {
            // If we already have report content, use it
            if (report) {
                setRealContent(report);
                setIsLoadingContent(false);
                return;
            }

            // If no report content, try to fetch or generate it
            if (!answers || !scores) {
                setIsLoadingContent(false);
                return;
            }

            try {
                setIsLoadingContent(true);
                setContentError(null);

                const endpoint = reportType === 'adhd' ? '/api/adhd/report' : '/api/autism/report';
                
                const requestData = reportType === 'adhd' ? {
                    testType: testType.replace('Test', '').replace('adhd', '').replace('Adult', 'adult').replace('Child', 'child'),
                    answers,
                    scores,
                    locale
                } : {
                    type: testType,
                    answers,
                    score: scores || score,
                    locale
                };

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept-Language': locale,
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch content');
                }

                const data = await response.json();
                if (data.report) {
                    setRealContent(data.report);
                    
                    // Save the report to database
                    const saveEndpoint = reportType === 'adhd' ? '/api/adhd/save-report' : '/api/autism/save-report';
                    try {
                        await fetch(saveEndpoint, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                id: testId,
                                locale,
                                report: data.report
                            })
                        });
                        console.log(`[UnifiedReportDisplay] Report saved to database for ${testId}`);
                    } catch (saveError) {
                        console.error('[UnifiedReportDisplay] Failed to save report:', saveError);
                    }
                }
            } catch (error) {
                console.error('Error fetching real content:', error);
                setContentError('Failed to load content');
            } finally {
                setIsLoadingContent(false);
            }
        };

        fetchRealContent();
    }, [testId, reportType, testType, answers, scores, score, locale, report]);

    // Render data visualization based on test type
    const renderVisualization = () => {
        if (!scores && !score) return null;

        if (reportType === 'adhd') {
            const isAdult = testType.includes('adult') || testType.includes('Adult');
            return isAdult ? (
                <AdhdAdultVisualization scores={scores} locale={locale} />
            ) : (
                <AdhdChildVisualization scores={scores} locale={locale} />
            );
        } else {
            const isRaadsr = testType === 'raadsrTest' || testType === 'raadsr';
            return isRaadsr ? (
                <RaadsrVisualization scores={scores} locale={locale} />
            ) : (
                <Aq10Visualization 
                    scores={{ 
                        score: score || scores?.score || 0, 
                        max: 10, 
                        significant: (score || scores?.score || 0) >= 6 
                    }} 
                    locale={locale} 
                />
            );
        }
    };

    // Render content with appropriate blur/unlock overlay
    const renderContent = () => {
        if (isLoadingContent) {
            return (
                <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                    <div className="animate-pulse space-y-3 sm:space-y-4">
                        <div className="h-6 sm:h-8 bg-gray-200 rounded w-3/4"></div>
                        <div className="space-y-2">
                            <div className="h-3 sm:h-4 bg-gray-200 rounded"></div>
                            <div className="h-3 sm:h-4 bg-gray-200 rounded w-5/6"></div>
                            <div className="h-3 sm:h-4 bg-gray-200 rounded w-4/6"></div>
                        </div>
                        <div className="h-5 sm:h-6 bg-gray-200 rounded w-1/2 mt-4 sm:mt-6"></div>
                        <div className="space-y-2">
                            <div className="h-3 sm:h-4 bg-gray-200 rounded"></div>
                            <div className="h-3 sm:h-4 bg-gray-200 rounded w-5/6"></div>
                            <div className="h-3 sm:h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-3 sm:h-4 bg-gray-200 rounded w-4/6"></div>
                        </div>
                    </div>
                </div>
            );
        }

        if (contentError || !realContent) {
            // Convert testType to the expected format for ReportTemplate
            const templateTestType = testType as 'raadsrTest' | 'aq10Test' | 'adhdAdultTest' | 'adhdChildTest';
            return (
                <ReportTemplate
                    testType={templateTestType}
                    locale={locale}
                />
            );
        }

        const contentToDisplay = realContent;
        const shouldStream = isStreaming && justUnlocked;

        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                {/* Data Visualization - always shown */}
                <div className="px-4 sm:px-6 pt-4 sm:pt-6">
                    {renderVisualization()}
                </div>

                {/* Report Content */}
                <div className={isReportLocked ? 'relative' : ''}>
                    <div className={isReportLocked ? 'filter blur-lg select-none pointer-events-none' : ''}>
                        <StreamingReportContent
                            content={contentToDisplay}
                            isStreaming={shouldStream}
                            streamingSpeed={60}
                            streamingInterval={40}
                            onStreamingComplete={onStreamingComplete}
                            className="px-4 sm:px-6 pb-4 sm:pb-6"
                            showProgressIndicator={shouldStream}
                            progressTitle={locale === 'zh' ? '正在生成报告' : 'Generating Report'}
                        />
                    </div>

                    {/* Enhanced multi-layer blur overlay for locked content */}
                    {isReportLocked && (
                        <>
                            {/* Primary blur overlay */}
                            <div className="absolute inset-0 bg-gradient-to-b from-white/40 via-white/60 to-white/40 dark:from-gray-900/40 dark:via-gray-900/60 dark:to-gray-900/40 backdrop-blur-md pointer-events-none" />
                            {/* Secondary privacy protection */}
                            <div className="absolute inset-0 bg-white/30 dark:bg-gray-900/30 pointer-events-none" />
                            {/* Additional blur layer for extra security */}
                            <div className="absolute inset-0 backdrop-blur-sm bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-gray-900/20 pointer-events-none" />
                        </>
                    )}
                </div>

                {/* Error Report Button - only show when unlocked */}
                {!isReportLocked && (
                    <div className="sticky bottom-0 flex justify-end p-3 sm:p-4 bg-gradient-to-t from-white via-white to-transparent dark:from-gray-800 dark:via-gray-800 dark:to-transparent">
                        <button
                            onClick={() => setShowErrorModal(true)}
                            className="inline-flex items-center gap-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 px-3 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 focus:ring-offset-2 text-sm font-medium touch-manipulation"
                            title={locale === 'zh' ? '报告问题' : 'Report Issue'}
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 19c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <span className="hidden sm:inline">
                                {locale === 'zh' ? '报告问题' : 'Report Issue'}
                            </span>
                        </button>
                    </div>
                )}

                {/* Error Report Modal */}
                <ErrorReportModal
                    isOpen={showErrorModal}
                    onClose={() => setShowErrorModal(false)}
                    locale={locale}
                    testId={testId}
                    testType={testType}
                    reportType={reportType}
                />
            </div>
        );
    };

    // If locked, wrap with unlock overlay
    if (isReportLocked) {
        return (
            <LockedReportUnlock
                id={testId}
                locale={locale}
                type={reportType}
                email={email}
                userId={userId}
                onUnlock={onUnlock}
                translations={translations}
            >
                {renderContent()}
            </LockedReportUnlock>
        );
    }

    // If unlocked, show content directly
    return renderContent();
}
