'use client';

import { useState } from 'react';
import { Download, AlertTriangle, Trash2, X } from 'lucide-react';
import { signOut } from 'next-auth/react';
import { getLocalizedPath } from '@/utils/utils';

interface ProfileActionsProps {
  locale: string;
  translations: {
    title: string;
    exportData: string;
    deleteAccount: string;
  };
}

export default function ProfileActions({ locale, translations }: ProfileActionsProps) {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 导出用户数据
  const handleExportData = async () => {
    setIsExporting(true);

    try {
      // 这里应该实现导出用户数据的逻辑
      // 示例：调用API端点获取用户数据并下载
      const response = await fetch('/api/user/export-data');
      const data = await response.json();

      // 创建并下载文件
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `raadstest-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Failed to export data:', error);
      // 这里应该添加错误处理
    } finally {
      setIsExporting(false);
    }
  };

  // 删除账户处理
  const handleDeleteAccount = async () => {
    if (confirmText !== 'DELETE') return;

    setIsSubmitting(true);
    try {
      // 这里应该实现删除账户的逻辑
      await fetch('/api/user/delete-account', {
        method: 'DELETE',
      });

      // 登出并重定向到首页
      await signOut({ callbackUrl: getLocalizedPath('/', locale) });
    } catch (error) {
      console.error('Failed to delete account:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6 transition-shadow hover:shadow-md">
      <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
        {translations.title}
      </h2>

      <div className="space-y-3">
        <button
          onClick={handleExportData}
          disabled={isExporting}
          className="flex items-center w-full px-4 py-3 text-left border border-gray-200 rounded-xl hover:bg-gray-50 transition-all hover:border-blue-200 hover:shadow-sm group disabled:opacity-60 disabled:cursor-not-allowed"
        >
          <Download size={18} className="text-blue-600 mr-3 group-hover:scale-110 transition-transform flex-shrink-0" />
          <span className="text-gray-700 font-medium">{translations.exportData}</span>
          {isExporting && (
            <span className="ml-auto">
              <svg className="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
          )}
        </button>

        <button
          onClick={() => setIsDeleteModalOpen(true)}
          className="flex items-center w-full px-4 py-3 text-left border border-red-100 rounded-xl hover:bg-red-50 transition-all hover:border-red-200 hover:shadow-sm group"
        >
          <Trash2 size={18} className="text-red-600 mr-3 group-hover:scale-110 transition-transform flex-shrink-0" />
          <span className="text-red-700 font-medium">{translations.deleteAccount}</span>
        </button>
      </div>

      {/* 删除账户确认模态框 - 优化界面 */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4 backdrop-blur-sm">
          <div className="bg-white rounded-xl max-w-md w-full p-6 shadow-xl animate-fadeIn">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-red-600 flex items-center">
                <AlertTriangle className="mr-2" size={20} />
                Delete Account
              </h3>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 rounded-full p-1"
              >
                <X size={20} />
              </button>
            </div>

            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded">
              <p className="text-red-700 text-sm">
                This action cannot be undone. All your data will be permanently deleted.
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type DELETE to confirm
              </label>
              <input
                type="text"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Type DELETE"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteAccount}
                disabled={confirmText !== 'DELETE' || isSubmitting}
                className={`px-4 py-2 bg-red-600 text-white rounded-md flex items-center font-medium ${confirmText === 'DELETE' && !isSubmitting ? 'hover:bg-red-700' : 'opacity-60 cursor-not-allowed'
                  }`}
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2" size={16} />
                    Delete Account
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 