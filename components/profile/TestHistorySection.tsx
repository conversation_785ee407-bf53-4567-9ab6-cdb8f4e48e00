'use client';

import Link from 'next/link';
import { ChevronRight, Calendar, BarChart2, Clock } from 'lucide-react';
import { formatDate } from '@/utils/formatters';
import { getLocalizedPath } from '@/utils/utils';

// Helper function to generate correct result URL based on test type
const getTestResultUrl = (testType: string, testId: string, locale: string): string => {
  const localePrefix = locale === 'en' ? '' : `/${locale}`;

  if (testType === 'raadsrTest' || testType === 'aq10Test') {
    return `${localePrefix}/autism/result/${testId}`;
  } else if (testType === 'adult' || testType === 'child' || testType === 'general') {
    return `${localePrefix}/adhd/result/${testId}`;
  } else {
    // For unknown test types, redirect to home page
    return `${localePrefix}/`;
  }
};

// Helper function to get test type visual representation
const getTestTypeIndicator = (testType: string, color: string) => {
  const indicators = {
    'raadsrTest': { label: 'R', fullName: 'RAADS-R' },
    'aq10Test': { label: 'AQ', fullName: 'AQ-10' },
    'adult': { label: 'AD', fullName: 'Adult ADHD' },
    'child': { label: 'CH', fullName: 'Child ADHD' },
    'general': { label: 'GN', fullName: 'General' },
  };

  const indicator = indicators[testType as keyof typeof indicators] || { label: 'T', fullName: testType };

  const colorClasses = {
    'indigo': 'bg-indigo-100 text-indigo-700 border-indigo-200',
    'blue': 'bg-blue-100 text-blue-700 border-blue-200',
    'purple': 'bg-purple-100 text-purple-700 border-purple-200',
    'teal': 'bg-teal-100 text-teal-700 border-teal-200',
    'gray': 'bg-gray-100 text-gray-700 border-gray-200'
  }[color] || 'bg-gray-100 text-gray-700 border-gray-200';

  return {
    element: (
      <div className={`flex-shrink-0 w-14 h-14 rounded-lg ${colorClasses} border flex items-center justify-center font-bold text-sm transition-all group-hover:scale-105`}>
        {indicator.label}
      </div>
    ),
    fullName: indicator.fullName
  };
};

interface TestResult {
  id: string;
  testType: string;
  createdAt: Date;
  isReportLocked: boolean;
  scores: any;
}

interface TestHistorySectionProps {
  testResults: TestResult[];
  locale: string;
  translations: {
    title: string;
    subtitle: string;
    viewAll: string;
    noTests: string;
    startTest: string;
    status: {
      analyzed: string;
      locked: string;
      completed: string;
    };
    viewAllReports: string;
    takeFirstTest: string;
  };
}

export default function TestHistorySection({ testResults, locale, translations }: TestHistorySectionProps) {
  // 测试类型映射
  const testTypeMap: Record<string, { name: string, color: string }> = {
    'raadsrTest': {
      name: 'RAADS-R',
      color: 'indigo'
    },
    'aq10Test': {
      name: 'AQ-10',
      color: 'blue'
    },
    'adult': {
      name: 'Adult ADHD',
      color: 'purple'
    },
    'child': {
      name: 'Child ADHD',
      color: 'teal'
    },
    'general': {
      name: 'General Assessment',
      color: 'gray'
    },
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6 transition-shadow hover:shadow-md">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-6">
        <div className="flex-1">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center font-heading mb-2">
            <BarChart2 size={20} className="mr-2 text-blue-600 flex-shrink-0" />
            {translations.title}
          </h2>
          <p className="text-gray-600 text-sm leading-relaxed">{translations.subtitle}</p>
        </div>

        {testResults.length > 0 && (
          <Link
            href={getLocalizedPath('/reports', locale)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center bg-blue-50 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors flex-shrink-0"
          >
            {translations.viewAllReports}
            <ChevronRight className="ml-1" size={16} />
          </Link>
        )}
      </div>

      {testResults.length === 0 ? (
        <div className="text-center py-8 sm:py-12 px-4 sm:px-6 border-2 border-dashed border-gray-200 rounded-xl bg-gradient-to-b from-gray-50 to-white">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-50 rounded-full flex items-center justify-center">
            <BarChart2 size={24} className="text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-800 mb-3 font-heading">{translations.noTests}</h3>
          <p className="text-gray-600 mb-6 max-w-sm mx-auto text-sm leading-relaxed">
            Take your first assessment to track your neurodevelopmental health and receive personalized insights.
          </p>
          <Link
            href={getLocalizedPath('/', locale)}
            className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm"
          >
            {translations.takeFirstTest}
            <ChevronRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
      ) : (
        <div className="space-y-3">
          {testResults.map((test) => {
            const testInfo = testTypeMap[test.testType] || {
              name: test.testType,
              color: 'gray'
            };

            const testDate = formatDate(test.createdAt, locale);
            const typeIndicator = getTestTypeIndicator(test.testType, testInfo.color);

            // 确定测试结果的状态和颜色 - 使用多语言
            let statusBadge;
            if (test.isReportLocked) {
              statusBadge = (
                <div className="flex-shrink-0">
                  {/* 移动端：显示彩色圆点 */}
                  <div className="sm:hidden w-3 h-3 rounded-full bg-amber-400 border-2 border-amber-100" title={translations.status.locked}></div>
                  {/* 桌面端：显示文字标签 */}
                  <span className="hidden sm:inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-amber-50 text-amber-700 border border-amber-200">
                    {translations.status.locked}
                  </span>
                </div>
              );
            } else {
              statusBadge = (
                <div className="flex-shrink-0">
                  {/* 移动端：显示彩色圆点 */}
                  <div className="sm:hidden w-3 h-3 rounded-full bg-green-400 border-2 border-green-100" title={translations.status.analyzed}></div>
                  {/* 桌面端：显示文字标签 */}
                  <span className="hidden sm:inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                    {translations.status.analyzed}
                  </span>
                </div>
              );
            }

            // 提取分数信息并优化显示效果 - 更紧凑的布局
            let scoreDisplay;
            let scorePercentage = 0;
            try {
              if (test.testType === 'aq10Test') {
                // AQ-10 测试可能有多种分数存储格式
                let score, max;

                // 尝试从 scores.score 中获取
                if (test.scores?.score) {
                  score = test.scores.score.score || test.scores.score;
                  max = test.scores.score.max || 10; // AQ-10 最大分数是 10
                }
                // 尝试从 scores 直接获取（如果 scores 本身就是分数对象）
                else if (test.scores && typeof test.scores === 'object' && 'score' in test.scores && 'max' in test.scores) {
                  score = test.scores.score;
                  max = test.scores.max;
                }
                // 如果都没找到，检查是否在 answers.score 中
                else if ((test as any).answers?.score) {
                  const scoreData = (test as any).answers.score;
                  score = scoreData.score || scoreData;
                  max = scoreData.max || 10;
                }

                if (score !== undefined && max) {
                  scorePercentage = (score / max) * 100;

                  scoreDisplay = (
                    <div className="bg-blue-50 rounded-lg p-2 text-center min-w-[70px] border border-blue-100 shadow-sm h-14 flex flex-col justify-center">
                      <div className="text-lg font-bold text-blue-700 font-heading leading-none">{score}</div>
                      <div className="text-xs text-blue-600 leading-tight mt-0.5">of {max}</div>
                      <div className="w-full bg-blue-200 rounded-full h-1 mt-1">
                        <div className="bg-blue-600 h-1 rounded-full transition-all duration-300" style={{ width: `${scorePercentage}%` }}></div>
                      </div>
                    </div>
                  );
                }
              } else if (test.scores?.total) {
                const score = test.scores.total.score;
                const max = test.scores.total.max;
                scorePercentage = (score / max) * 100;

                const colorClasses = {
                  indigo: 'bg-indigo-50 text-indigo-700 border-indigo-100',
                  purple: 'bg-purple-50 text-purple-700 border-purple-100',
                  teal: 'bg-teal-50 text-teal-700 border-teal-100',
                  gray: 'bg-gray-50 text-gray-700 border-gray-100'
                }[testInfo.color] || 'bg-gray-50 text-gray-700 border-gray-100';

                const progressColor = {
                  indigo: 'bg-indigo-600',
                  purple: 'bg-purple-600',
                  teal: 'bg-teal-600',
                  gray: 'bg-gray-600'
                }[testInfo.color] || 'bg-gray-600';

                scoreDisplay = (
                  <div className={`${colorClasses} rounded-lg p-2 text-center min-w-[70px] border shadow-sm h-14 flex flex-col justify-center`}>
                    <div className="text-lg font-bold font-heading leading-none">{score}</div>
                    <div className="text-xs leading-tight mt-0.5">of {max}</div>
                    <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                      <div className={`${progressColor} h-1 rounded-full transition-all duration-300`} style={{ width: `${scorePercentage}%` }}></div>
                    </div>
                  </div>
                );
              }
            } catch (e) {
              console.error("Error extracting score:", e);
            }

            return (
              <Link
                key={test.id}
                href={getTestResultUrl(test.testType, test.id, locale)}
                className="block border border-gray-100 rounded-xl p-3 sm:p-4 hover:shadow-lg hover:border-gray-300 transition-all duration-200 group bg-white cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label={`View ${typeIndicator.fullName} test result`}
              >
                {/* 优化的响应式布局 - 移动端纵向，桌面端横向 */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                  {/* 移动端：顶部测试类型和状态行 */}
                  <div className="flex items-center justify-between gap-2 sm:hidden">
                    <div className="flex items-center gap-3">
                      {/* 移动端测试类型指示器 - 统一h-14高度 */}
                      <div className="group-hover:scale-105 transition-transform">
                        {typeIndicator.element}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 font-heading text-sm truncate group-hover:text-blue-600 transition-colors">
                          {typeIndicator.fullName.split(' ')[0]}
                        </h3>
                        <div className="flex items-center text-xs text-gray-500 gap-1">
                          <Calendar size={11} className="flex-shrink-0" />
                          <span className="truncate">{testDate}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {statusBadge}
                      {scoreDisplay && (
                        <div className="flex-shrink-0 group-hover:scale-105 transition-transform">
                          {scoreDisplay}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 桌面端：水平布局，所有元素在同一行且垂直居中 */}
                  <div className="hidden sm:flex sm:items-center sm:w-full sm:gap-4">
                    {/* 测试类型指示器 - 统一h-14高度 */}
                    <div className="group-hover:scale-105 transition-transform">
                      {typeIndicator.element}
                    </div>

                    {/* 测试信息 - 垂直居中 */}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 font-heading text-base truncate mb-1 group-hover:text-blue-600 transition-colors">
                        {typeIndicator.fullName}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500 gap-1">
                        <Calendar size={13} className="flex-shrink-0" />
                        <span className="truncate">{testDate}</span>
                      </div>
                    </div>

                    {/* 状态标签 - 垂直居中 */}
                    <div className="flex-shrink-0">
                      {statusBadge}
                    </div>

                    {/* 分数显示 - 与icon高度一致 */}
                    {scoreDisplay && (
                      <div className="flex-shrink-0 group-hover:scale-105 transition-transform">
                        {scoreDisplay}
                      </div>
                    )}
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
}