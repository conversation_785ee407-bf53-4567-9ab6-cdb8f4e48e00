'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Crown, Calendar, CheckCircle, AlertCircle, CreditCard, Settings, Zap } from 'lucide-react';

interface SubscriptionInfo {
    type: string | null;
    status: string | null;
    currentPeriodEnd: Date | null;
    reportsRemaining: number;
    consultationsRemaining: number;
    hasHistoryAccess: boolean;
    hasVipFeatures: boolean;
    isUnlimited: boolean;
}

interface SubscriptionInfoProps {
    locale: string;
    translations: {
        title: string;
        noSubscription: string;
        upgradeNow: string;
        manageSubscription: string;
        currentPlan: string;
        status: string;
        renewsOn: string;
        reportsLeft: string;
        consultationsLeft: string;
        unlimited: string;
        features: {
            historyAccess: string;
            vipFeatures: string;
            prioritySupport: string;
        };
        planTypes: {
            single: string;
            pro: string;
            premium: string;
        };
        statusTypes: {
            active: string;
            canceled: string;
            past_due: string;
            incomplete: string;
        };
    };
}

export default function SubscriptionInfo({ locale, translations }: SubscriptionInfoProps) {
    const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchSubscriptionInfo();
    }, []);

    const fetchSubscriptionInfo = async () => {
        try {
            const response = await fetch('/api/subscription/info');
            const data = await response.json();

            if (response.ok) {
                setSubscriptionInfo(data);
            } else {
                setError(data.error || 'Failed to load subscription info');
            }
        } catch (error) {
            console.error('Error fetching subscription info:', error);
            setError('Failed to load subscription info');
        } finally {
            setLoading(false);
        }
    };

    const handleUpgrade = () => {
        // Redirect to pricing page or open upgrade modal
        window.location.href = `/${locale === 'en' ? '' : locale + '/'}`;
    };

    const handleManageSubscription = async () => {
        try {
            // Create Stripe customer portal session
            const response = await fetch('/api/subscription/create-portal', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (response.ok && data.url) {
                window.location.href = data.url;
            } else {
                throw new Error(data.error || 'Failed to create portal session');
            }
        } catch (error) {
            console.error('Error creating portal session:', error);
            setError('Failed to open subscription management');
        }
    };

    const getPlanIcon = (type: string | null) => {
        switch (type) {
            case 'premium':
                return <Crown className="w-5 h-5 text-purple-600" />;
            case 'pro':
                return <Zap className="w-5 h-5 text-blue-600" />;
            default:
                return <CreditCard className="w-5 h-5 text-emerald-600" />;
        }
    };

    const getPlanColor = (type: string | null) => {
        switch (type) {
            case 'premium':
                return 'purple';
            case 'pro':
                return 'blue';
            default:
                return 'emerald';
        }
    };

    const getStatusIcon = (status: string | null) => {
        switch (status) {
            case 'active':
                return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'canceled':
            case 'past_due':
            case 'incomplete':
                return <AlertCircle className="w-4 h-4 text-red-500" />;
            default:
                return <AlertCircle className="w-4 h-4 text-gray-400" />;
        }
    };

    if (loading) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                    <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="text-center">
                    <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
                    <p className="text-red-600 text-sm">{error}</p>
                </div>
            </div>
        );
    }

    if (!subscriptionInfo?.type) {
        return (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900">
                    <CreditCard className="w-5 h-5 mr-2 text-gray-600" />
                    {translations.title}
                </h2>
                <div className="text-center py-6">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <CreditCard className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-600 mb-4">{translations.noSubscription}</p>
                    <button
                        onClick={handleUpgrade}
                        className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                        {translations.upgradeNow}
                    </button>
                </div>
            </div>
        );
    }

    const planColor = getPlanColor(subscriptionInfo.type);

    return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 transition-shadow hover:shadow-md">
            <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
                {getPlanIcon(subscriptionInfo.type)}
                <span className="ml-2">{translations.title}</span>
            </h2>

            {/* Current Plan */}
            <div className={`bg-${planColor}-50 border border-${planColor}-200 rounded-lg p-4 mb-6`}>
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                        <span className={`text-${planColor}-800 font-semibold`}>
                            {translations.currentPlan}: {translations.planTypes[subscriptionInfo.type as keyof typeof translations.planTypes]}
                        </span>
                    </div>
                    <div className="flex items-center">
                        {getStatusIcon(subscriptionInfo.status)}
                        <span className="ml-1 text-sm text-gray-600">
                            {translations.statusTypes[subscriptionInfo.status as keyof typeof translations.statusTypes] || subscriptionInfo.status}
                        </span>
                    </div>
                </div>

                {subscriptionInfo.currentPeriodEnd && (
                    <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="w-4 h-4 mr-1" />
                        {translations.renewsOn}: {new Date(subscriptionInfo.currentPeriodEnd).toLocaleDateString(locale)}
                    </div>
                )}
            </div>

            {/* Usage Stats */}
            <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between">
                    <span className="text-gray-600">{translations.reportsLeft}</span>
                    <span className="font-semibold">
                        {subscriptionInfo.isUnlimited ? translations.unlimited : subscriptionInfo.reportsRemaining}
                    </span>
                </div>
                <div className="flex items-center justify-between">
                    <span className="text-gray-600">{translations.consultationsLeft}</span>
                    <span className="font-semibold">
                        {subscriptionInfo.isUnlimited ? translations.unlimited : subscriptionInfo.consultationsRemaining}
                    </span>
                </div>
            </div>

            {/* Features */}
            <div className="space-y-2 mb-6">
                <div className="flex items-center">
                    <CheckCircle className={`w-4 h-4 mr-2 ${subscriptionInfo.hasHistoryAccess ? 'text-green-500' : 'text-gray-300'}`} />
                    <span className={`text-sm ${subscriptionInfo.hasHistoryAccess ? 'text-gray-900' : 'text-gray-400'}`}>
                        {translations.features.historyAccess}
                    </span>
                </div>
                <div className="flex items-center">
                    <CheckCircle className={`w-4 h-4 mr-2 ${subscriptionInfo.hasVipFeatures ? 'text-green-500' : 'text-gray-300'}`} />
                    <span className={`text-sm ${subscriptionInfo.hasVipFeatures ? 'text-gray-900' : 'text-gray-400'}`}>
                        {translations.features.vipFeatures}
                    </span>
                </div>
                <div className="flex items-center">
                    <CheckCircle className={`w-4 h-4 mr-2 ${subscriptionInfo.type !== 'single' ? 'text-green-500' : 'text-gray-300'}`} />
                    <span className={`text-sm ${subscriptionInfo.type !== 'single' ? 'text-gray-900' : 'text-gray-400'}`}>
                        {translations.features.prioritySupport}
                    </span>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
                {subscriptionInfo.type !== 'single' && (
                    <button
                        onClick={handleManageSubscription}
                        className="flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                        <Settings className="w-4 h-4 mr-2" />
                        {translations.manageSubscription}
                    </button>
                )}
                {subscriptionInfo.type !== 'premium' && (
                    <button
                        onClick={handleUpgrade}
                        className={`flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-${planColor}-600 rounded-lg hover:bg-${planColor}-700 transition-colors`}
                    >
                        <Crown className="w-4 h-4 mr-2" />
                        {translations.upgradeNow}
                    </button>
                )}
            </div>
        </div>
    );
} 