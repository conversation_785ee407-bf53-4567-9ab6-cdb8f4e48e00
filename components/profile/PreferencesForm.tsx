'use client';

import { useState } from 'react';
import { Check, Save, Bell, Lock, Info } from 'lucide-react';

interface PreferencesFormProps {
  user: any;
  locale: string;
  translations: {
    title: string;
    notifications: string;
    dataPrivacy: string;
    saveChanges: string;
    saved: string;
    optIn: string;
    optOut: string;
  };
}

function PreferencesForm({ user, locale, translations }: PreferencesFormProps) {
  const [emailNotifications, setEmailNotifications] = useState(user.emailNotifications ?? true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSaved, setShowSaved] = useState(false);
  
  // 保存用户偏好设置
  const handleSavePreferences = async () => {
    setIsSaving(true);
    try {
      // 调用API更新用户设置
      const response = await fetch('/api/user/update-preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailNotifications,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update preferences');
      }
      
      // 显示保存成功消息
      setShowSaved(true);
      setTimeout(() => {
        setShowSaved(false);
      }, 3000);
    } catch (error) {
      console.error('Error updating preferences:', error);
      // 这里应该添加错误处理
    } finally {
      setIsSaving(false);
    }
  };
  
  // 处理通知设置切换
  const handleNotificationsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmailNotifications(e.target.checked);
  };
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6 transition-shadow hover:shadow-md">
      <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
        {translations.title}
      </h2>
      
      <div className="space-y-6">
        {/* 邮件通知设置 */}
        <div className="border border-gray-200 rounded-xl p-4 transition-colors hover:border-blue-200 bg-gray-50/50">
          <div className="flex items-start">
            <Bell size={20} className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <label htmlFor="notifications" className="block text-sm font-medium text-gray-700 mb-3">
                {translations.notifications}
              </label>
              
              <div className="flex items-center mb-4">
                <div className="relative inline-flex items-center">
                  <input
                    type="checkbox"
                    id="notifications"
                    checked={emailNotifications}
                    onChange={handleNotificationsChange}
                    className="sr-only"
                  />
                  <div className={`w-11 h-6 rounded-full transition-colors ${emailNotifications ? 'bg-blue-600' : 'bg-gray-300'}`}>
                    <div className={`absolute left-0.5 top-0.5 bg-white w-5 h-5 rounded-full transition-transform shadow-sm ${emailNotifications ? 'transform translate-x-5' : ''}`}></div>
                  </div>
                </div>
                <span className="ml-3 text-sm text-gray-600">
                  {emailNotifications ? translations.optIn : translations.optOut}
                </span>
              </div>
              
              <div className="flex items-start bg-blue-50 p-3 rounded-lg">
                <Info size={16} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-blue-800 leading-relaxed">
                  We&apos;ll only send important notifications about your tests and results. You can change this setting anytime.
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* 数据隐私设置 */}
        <div className="border border-gray-200 rounded-xl p-4 transition-colors hover:border-blue-200 bg-gray-50/50">
          <div className="flex items-start">
            <Lock size={20} className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-gray-700 mb-2">{translations.dataPrivacy}</h3>
              <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                Your data is securely stored and will never be shared with third parties without your explicit consent.
              </p>
              <a 
                href="https://raadstest.com/blog/private-policy/" 
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center font-medium"
              >
                View our Privacy Policy
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
        </div>
        
        <div className="pt-4 border-t border-gray-100">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <button
              onClick={handleSavePreferences}
              disabled={isSaving}
              className="inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none transition-colors disabled:bg-blue-400 disabled:cursor-not-allowed"
            >
              {isSaving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  {translations.saveChanges}
                </>
              )}
            </button>
            
            {showSaved && (
              <div className="text-green-600 flex items-center bg-green-50 px-3 py-2 rounded-lg border border-green-100 animate-fadeIn">
                <Check size={16} className="mr-2 flex-shrink-0" />
                <span className="text-sm">{translations.saved}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default PreferencesForm; 