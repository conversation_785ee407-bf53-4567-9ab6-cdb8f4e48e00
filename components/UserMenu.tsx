'use client';

import { useState, useRef, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';
import { Crown, Star, User2 } from 'lucide-react';
import Image from 'next/image';

interface UserMenuProps {
  locale: string;
}

interface SubscriptionInfo {
  type: string | null;
  status: string | null;
  currentPeriodEnd: Date | null;
  reportsRemaining: number;
  consultationsRemaining: number;
  hasHistoryAccess: boolean;
  hasVipFeatures: boolean;
  isUnlimited: boolean;
}

export default function UserMenu({ locale }: UserMenuProps) {
  const { data: session, status } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('userMenu');

  // 获取用户订阅信息
  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      if (status !== 'authenticated' || !session?.user?.email) return;

      setIsLoadingSubscription(true);
      try {
        const response = await fetch('/api/subscription/info');
        if (response.ok) {
          const data = await response.json();
          setSubscriptionInfo(data);
        }
      } catch (error) {
        console.error('Error fetching subscription info:', error);
      } finally {
        setIsLoadingSubscription(false);
      }
    };

    fetchSubscriptionInfo();
  }, [status, session?.user?.email]);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 获取订阅标签样式和内容
  const getSubscriptionBadge = () => {
    if (isLoadingSubscription) {
      return null;
    }

    const subscriptionType = subscriptionInfo?.type;
    const subscriptionStatus = subscriptionInfo?.status;

    // 只有当订阅状态为active时才显示标签
    if (subscriptionStatus !== 'active' || !subscriptionType) {
      // Free用户显示FREE标签
      return {
        label: 'FREE',
        icon: User2,
        bgColor: 'bg-gray-100',
        textColor: 'text-gray-600',
        borderColor: 'border-gray-200',
        glowColor: ''
      };
    }

    switch (subscriptionType) {
      case 'pro':
        return {
          label: 'PRO',
          icon: Star,
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-700',
          borderColor: 'border-blue-200',
          glowColor: 'shadow-blue-100'
        };
      case 'premium':
        return {
          label: 'PREMIUM',
          icon: Crown,
          bgColor: 'bg-gradient-to-r from-purple-100 to-pink-100',
          textColor: 'text-purple-700',
          borderColor: 'border-purple-200',
          glowColor: 'shadow-purple-100'
        };
      default:
        return {
          label: 'FREE',
          icon: User2,
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-600',
          borderColor: 'border-gray-200',
          glowColor: ''
        };
    }
  };

  // 判断是否在加载中
  const isLoading = status === 'loading';

  // 根据不同状态渲染不同内容
  if (isLoading) {
    return (
      <div className="animate-pulse flex items-center">
        <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
        <div className="ml-2 h-4 w-16 bg-gray-200 rounded"></div>
      </div>
    );
  }

  // 如果用户未登录，显示登录按钮
  if (status === 'unauthenticated') {
    return (
      <Link
        href={"/auth/signin" as Pathnames}
        className="inline-flex items-center justify-center px-4 py-2 min-w-[90px] text-center border border-transparent text-sm font-semibold rounded-lg text-white bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-sm transition-colors duration-200"
        aria-label={t('signIn')}
      >
        <span className="whitespace-nowrap">{t('signIn')}</span>
      </Link>
    );
  }

  const subscriptionBadge = getSubscriptionBadge();
  const BadgeIcon = subscriptionBadge?.icon;

  // 用户已登录，显示用户菜单
  return (
    <div className="relative" ref={menuRef}>
      <button
        className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="sr-only">{t('openUserMenu')}</span>
        <div className="relative">
          {/* 用户头像 */}
          <div className="relative w-9 h-9 rounded-full overflow-hidden bg-gray-100 border border-gray-300 shadow-sm">
            {session?.user?.image ? (
              <Image
                src={session.user.image}
                alt={session.user.name || 'User'}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-600 font-medium">
                {session?.user?.name?.[0]?.toUpperCase() || session?.user?.email?.[0]?.toUpperCase() || 'U'}
              </div>
            )}
          </div>

          {/* 订阅标签 */}
          {subscriptionBadge && (
            <div className={`absolute -top-1 -right-1 px-1.5 py-0.5 rounded-full text-xs font-bold uppercase tracking-wider border transition-all duration-200 ${subscriptionBadge.bgColor} ${subscriptionBadge.textColor} ${subscriptionBadge.borderColor} ${subscriptionBadge.glowColor} shadow-sm`}>
              <div className="flex items-center gap-0.5">
                {BadgeIcon && <BadgeIcon className="w-2.5 h-2.5" />}
                <span className="text-[9px] leading-none font-extrabold">
                  {subscriptionBadge.label}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* 用户名 - 响应式隐藏 */}
        <span className="ml-2 hidden sm:block text-sm font-medium text-gray-700 truncate max-w-[100px]">
          {session?.user?.name || session?.user?.email?.split('@')[0] || t('user')}
        </span>

        {/* 下拉箭头 */}
        <svg
          className="ml-1 h-4 w-4 text-gray-500"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10 border border-gray-100 overflow-hidden transform origin-top-right transition-all duration-200">
          <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="user-menu">
            {/* 用户信息区域 */}
            <div className="px-4 py-3 border-b border-gray-100">
              <div className="text-xs text-gray-500 truncate">
                {session?.user?.email}
              </div>
              {subscriptionBadge && (
                <div className="mt-1.5 flex items-center">
                  <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-semibold ${subscriptionBadge.bgColor} ${subscriptionBadge.textColor} ${subscriptionBadge.borderColor} border`}>
                    {BadgeIcon && <BadgeIcon className="w-3 h-3" />}
                    {subscriptionBadge.label}
                  </div>
                  {subscriptionInfo && subscriptionInfo.type !== 'premium' && (
                    <span className="ml-2 text-xs text-gray-400">
                      {subscriptionInfo.type === 'pro' ?
                        `${subscriptionInfo.reportsRemaining} reports left` :
                        'Upgrade available'
                      }
                    </span>
                  )}
                </div>
              )}
            </div>

            <Link
              href={"/profile" as Pathnames}
              className="block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              role="menuitem"
              onClick={() => setIsOpen(false)}
            >
              {t('profile')}
            </Link>

            <Link
              href={"/reports" as Pathnames}
              className="block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              role="menuitem"
              onClick={() => setIsOpen(false)}
            >
              {t('myReports')}
            </Link>

            <button
              className="w-full text-left block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              role="menuitem"
              onClick={() => {
                setIsOpen(false);
                signOut({ callbackUrl: '/' });
              }}
            >
              {t('signOut')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
} 