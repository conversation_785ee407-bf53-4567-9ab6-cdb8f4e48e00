"use client";

import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
    Elements,
    PaymentElement,
    useStripe,
    useElements
} from '@stripe/react-stripe-js';
import { X, Lock, Loader2, Check, CreditCard, Shield, Star, AlertTriangle } from 'lucide-react';

// 加载 Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface PaymentModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
    testId: string;
    email?: string;
    amount: number;
    currency?: string;
    userId?: string;
    locale?: string;
    stripePriceId?: string; // 新增 Stripe Price ID 支持
    translations: {
        paymentTitle: string;
        paymentDescription: string;
        cardDetails: string;
        payButton: string;
        processing: string;
        cancel: string;
        paymentSuccess: string;
        paymentError: string;
        securityTitle?: string;
        securityDescription?: string;
        securityMobile?: string;
        totalAmount?: string;
        valueProposition?: string;
        termsConditions?: string;
        stripeProcessing?: string;
        loadingPaymentForm?: string;
        initializingPayment?: string;
        unlockingReport?: string;
    };
}

// 获取默认翻译（根据locale）
const getDefaultTranslations = (locale: string = 'en'): Partial<PaymentModalProps['translations']> => {
    if (locale === 'zh') {
        return {
            securityTitle: "您的支付信息完全安全",
            securityDescription: "所有支付数据均通过 SSL 加密传输，我们不会存储您的银行卡信息",
            securityMobile: "安全支付 · SSL 加密保护",
            totalAmount: "支付总额",
            valueProposition: "包含详细分析报告 + 专业建议",
            termsConditions: "点击支付即表示您同意我们的服务条款",
            stripeProcessing: "支付由 Stripe 安全处理，符合 PCI DSS 标准",
            loadingPaymentForm: "正在加载支付表单...",
            initializingPayment: "正在初始化支付系统...",
            unlockingReport: "正在解锁您的报告..."
        };
    }

    return {
        securityTitle: "Your payment information is completely secure",
        securityDescription: "All payment data is transmitted via SSL encryption, and we never store your banking information",
        securityMobile: "Secure Payment · SSL Encrypted",
        totalAmount: "Total Amount",
        valueProposition: "Includes detailed analysis report + professional recommendations",
        termsConditions: "By clicking pay, you agree to our terms of service",
        stripeProcessing: "Payment processed securely by Stripe, PCI DSS compliant",
        loadingPaymentForm: "Loading payment form...",
        initializingPayment: "Initializing payment system...",
        unlockingReport: "Unlocking your report..."
    };
};

// 优化的支付表单组件
function PaymentForm({
    onSuccess,
    onError,
    testId,
    amount,
    locale,
    translations
}: {
    onSuccess: () => void;
    onError: (error: string) => void;
    testId: string;
    amount: number;
    locale: string;
    translations: PaymentModalProps['translations'] & Partial<PaymentModalProps['translations']>;
}) {
    const stripe = useStripe();
    const elements = useElements();
    const [isProcessing, setIsProcessing] = useState(false);
    const [isElementsReady, setIsElementsReady] = useState(false);

    // 轮询验证支付状态的函数
    const verifyPaymentStatus = async (paymentIntentId: string, testId: string) => {
        try {
            const response = await fetch('/api/verify-payment', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ paymentIntentId, testId }),
            });

            const data = await response.json();
            if (response.ok && data.success) {
                return data;
            } else {
                throw new Error(data.error || (locale === 'zh' ? '验证失败' : 'Verification failed'));
            }
        } catch (error) {
            console.error('支付验证失败:', error);
            throw error;
        }
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();

        if (!stripe || !elements || isProcessing) {
            return;
        }

        setIsProcessing(true);

        try {
            // 确认支付 - Stripe 会自动处理表单验证
            const { error, paymentIntent } = await stripe.confirmPayment({
                elements,
                confirmParams: {
                    return_url: `${window.location.origin}/payment-success?testId=${testId}`,
                },
                redirect: 'if_required',
            });

            if (error) {
                onError(error.message || (locale === 'zh' ? '支付失败' : 'Payment failed'));
            } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                // 验证支付并更新数据库
                try {
                    await verifyPaymentStatus(paymentIntent.id, testId);
                    onSuccess();
                } catch (verifyError) {
                    // 即使验证失败，支付已成功
                    onSuccess();
                }
            }
        } catch (error) {
            onError(error instanceof Error ? error.message : (locale === 'zh' ? '支付处理出错' : 'Payment processing error'));
        } finally {
            setIsProcessing(false);
        }
    };

    // PaymentElement 准备就绪的回调
    const handleElementsReady = () => {
        setIsElementsReady(true);
    };

    return (
        <div className="space-y-4 sm:space-y-6">
            {/* 安全信息提示 - 移动端简化版 */}
            <div className="hidden sm:flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {translations.securityTitle}
                    </p>
                    <p className="text-xs text-blue-700 dark:text-blue-300">
                        {translations.securityDescription}
                    </p>
                </div>
            </div>

            {/* 移动端简化安全提示 */}
            <div className="sm:hidden flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Shield className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <p className="text-sm text-blue-800 dark:text-blue-200">
                    {translations.securityMobile}
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                {/* PaymentElement 容器 - 响应式优化 */}
                <div className="payment-element-container">
                    <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {translations.cardDetails}
                        </label>
                    </div>

                    <div className="relative">
                        <PaymentElement
                            onReady={handleElementsReady}
                            options={{
                                layout: {
                                    type: 'tabs',
                                    defaultCollapsed: false,
                                    radios: false,
                                    spacedAccordionItems: true
                                },
                                paymentMethodOrder: ['card', 'apple_pay', 'google_pay'],
                                fields: {
                                    billingDetails: {
                                        name: 'auto',
                                        email: 'auto',
                                        phone: 'auto',
                                        address: {
                                            country: 'auto',
                                            line1: 'auto',
                                            line2: 'auto',
                                            city: 'auto',
                                            state: 'auto',
                                            postalCode: 'auto'
                                        }
                                    }
                                }
                            }}
                        />

                        {/* 加载状态遮罩 */}
                        {!isElementsReady && (
                            <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center rounded-lg">
                                <div className="flex items-center space-x-2">
                                    <Loader2 className="w-4 h-4 animate-spin text-emerald-600" />
                                    <span className="text-sm text-gray-600 dark:text-gray-400">
                                        {translations.loadingPaymentForm}
                                    </span>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* 金额显示 - 响应式优化 */}
                <div className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-xl p-4 border border-emerald-200 dark:border-emerald-800">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <CreditCard className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                            <span className="text-sm font-medium text-emerald-700 dark:text-emerald-300">
                                {translations.totalAmount}
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="text-xl sm:text-2xl font-bold text-emerald-900 dark:text-emerald-100">
                                ${amount.toFixed(2)}
                            </span>
                            <span className="text-sm text-emerald-600 dark:text-emerald-400 ml-1">
                                USD
                            </span>
                        </div>
                    </div>

                    {/* 价值提示 */}
                    <div className="mt-3 pt-3 border-t border-emerald-200 dark:border-emerald-700">
                        <div className="flex items-center space-x-2 text-xs text-emerald-700 dark:text-emerald-300">
                            <Star className="w-3 h-3 fill-current" />
                            <span>{translations.valueProposition}</span>
                        </div>
                    </div>
                </div>

                {/* 支付按钮 - 强化设计，提升转化率 */}
                <button
                    type="submit"
                    disabled={!stripe || !isElementsReady || isProcessing}
                    className="w-full bg-gradient-to-r from-emerald-600 via-emerald-600 to-green-600 hover:from-emerald-700 hover:via-emerald-700 hover:to-green-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 sm:py-5 px-6 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center justify-center shadow-lg hover:shadow-2xl transform hover:scale-[1.02] active:scale-[0.98] text-base sm:text-lg min-h-[56px] relative overflow-hidden group"
                >
                    {/* 渐变动效背景 */}
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-700 via-green-600 to-emerald-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <div className="relative flex items-center">
                        {isProcessing ? (
                            <>
                                <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 mr-3 animate-spin" />
                                <span>{translations.processing}</span>
                            </>
                        ) : (
                            <>
                                <div className="w-5 h-5 sm:w-6 sm:h-6 mr-3 rounded-full bg-white/20 flex items-center justify-center">
                                    <Lock className="w-3 h-3 sm:w-4 sm:h-4" />
                                </div>
                                <span className="font-bold">
                                    {translations.payButton} ${amount.toFixed(2)}
                                </span>
                            </>
                        )}
                    </div>
                </button>

                {/* 条款说明 - 响应式字体 */}
                <div className="text-center space-y-2">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                        {translations.termsConditions}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 flex items-center justify-center space-x-1">
                        <Shield className="w-3 h-3" />
                        <span>{translations.stripeProcessing}</span>
                    </p>
                </div>
            </form>

            <style jsx>{`
                .payment-element-container {
                    min-height: 160px;
                }
                
                /* 移动端优化 */
                @media (max-width: 640px) {
                    .payment-element-container {
                        min-height: 200px;
                    }
                }
                
                @media (max-width: 480px) {
                    .payment-element-container {
                        min-height: 240px;
                    }
                }
                
                /* 超小屏幕优化 */
                @media (max-width: 375px) {
                    .payment-element-container {
                        min-height: 260px;
                    }
                }
            `}</style>
        </div>
    );
}

export default function PaymentModal({
    isOpen,
    onClose,
    onSuccess,
    testId,
    email,
    amount,
    currency = 'usd',
    userId,
    locale = 'en',
    stripePriceId,
    translations
}: PaymentModalProps) {
    const [error, setError] = useState<string>('');
    const [success, setSuccess] = useState(false);
    const [elementsOptions, setElementsOptions] = useState<any>(null);

    // 合并默认翻译
    const defaultTranslations = getDefaultTranslations(locale);
    const mergedTranslations = { ...defaultTranslations, ...translations };

    const handleSuccess = () => {
        setSuccess(true);
        setTimeout(() => {
            onSuccess();
            onClose();
        }, 2000);
    };

    const handleError = (errorMessage: string) => {
        setError(errorMessage);
        // 自动清除错误信息
        setTimeout(() => setError(''), 5000);
    };

    const handleClose = () => {
        if (!success) {
            onClose();
        }
    };

    // 创建 Elements 配置
    useEffect(() => {
        const createElementsOptions = async () => {
            try {
                const response = await fetch('/api/create-payment-intent', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        testId: testId || 'temp-test-id',
                        email: email,
                        amount: amount,
                        currency: currency,
                        userId: userId,
                        stripePriceId: stripePriceId
                    }),
                });

                const data = await response.json();

                if (response.ok && data.clientSecret) {
                    setElementsOptions({
                        clientSecret: data.clientSecret,
                        appearance: {
                            theme: 'stripe',
                            variables: {
                                colorPrimary: '#10b981',
                                colorBackground: '#ffffff',
                                colorText: '#374151',
                                colorDanger: '#ef4444',
                                fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, sans-serif',
                                borderRadius: '8px',
                                fontSizeBase: '16px', // 防止 iOS 缩放
                                spacingUnit: '6px',
                                fontWeightNormal: '400',
                                fontWeightMedium: '500'
                            },
                            rules: {
                                '.Tab': {
                                    border: '1px solid #e5e7eb',
                                    borderRadius: '8px',
                                    padding: '12px 16px',
                                    backgroundColor: '#ffffff',
                                    minHeight: '44px',
                                    transition: 'all 0.2s ease'
                                },
                                '.Tab:hover': {
                                    backgroundColor: '#f9fafb',
                                    borderColor: '#10b981',
                                    transform: 'translateY(-1px)',
                                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                                },
                                '.Tab--selected': {
                                    backgroundColor: '#ecfdf5',
                                    borderColor: '#10b981',
                                    color: '#047857'
                                },
                                '.Input': {
                                    border: '1px solid #d1d5db',
                                    borderRadius: '6px',
                                    padding: '12px 16px',
                                    fontSize: '16px',
                                    backgroundColor: '#ffffff',
                                    minHeight: '44px',
                                    transition: 'all 0.2s ease'
                                },
                                '.Input:focus': {
                                    borderColor: '#10b981',
                                    outline: 'none',
                                    boxShadow: '0 0 0 3px rgba(16, 185, 129, 0.1)'
                                },
                                '.Input--invalid': {
                                    borderColor: '#ef4444',
                                    boxShadow: '0 0 0 3px rgba(239, 68, 68, 0.1)'
                                },
                                '.Label': {
                                    fontSize: '14px',
                                    fontWeight: '500',
                                    color: '#374151',
                                    marginBottom: '6px'
                                },
                                '.Error': {
                                    fontSize: '14px',
                                    color: '#ef4444',
                                    marginTop: '4px'
                                }
                            }
                        }
                    });
                } else {
                    // 回退到基本配置
                    setElementsOptions({
                        mode: 'payment',
                        amount: Math.round(amount * 100),
                        currency: currency,
                        appearance: {
                            theme: 'stripe',
                            variables: {
                                colorPrimary: '#10b981',
                                fontSizeBase: '16px'
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('创建支付配置失败:', error);
                setError(locale === 'zh' ? '支付系统初始化失败，请稍后重试' : 'Payment system initialization failed, please try again later');
            }
        };

        if (isOpen && !elementsOptions) {
            createElementsOptions();
        }
    }, [isOpen, testId, email, amount, currency, userId, elementsOptions, locale]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-[60] bg-black/50 flex items-center justify-center p-3 sm:p-4 backdrop-blur-sm">
            {/* 响应式模态框容器 */}
            <div className="bg-white dark:bg-gray-800 rounded-xl sm:rounded-2xl w-full max-w-sm sm:max-w-md max-h-[95vh] shadow-2xl overflow-hidden flex flex-col">
                {/* 头部 - 响应式间距 */}
                <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center flex-shrink-0">
                            <Lock className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <div className="min-w-0">
                            <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                                {translations.paymentTitle}
                            </h3>
                            <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">
                                {translations.paymentDescription}
                            </p>
                        </div>
                    </div>
                    <button
                        onClick={handleClose}
                        disabled={success}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors disabled:opacity-50 flex-shrink-0 ml-2 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                    >
                        <X className="w-5 h-5" />
                    </button>
                </div>

                {/* 内容区域 - 可滚动 */}
                <div className="flex-1 overflow-y-auto">
                    <div className="p-4 sm:p-6">
                        {success ? (
                            <div className="text-center py-6 sm:py-8">
                                <div className="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mx-auto mb-4">
                                    <Check className="w-8 h-8 text-green-600 dark:text-green-400" />
                                </div>
                                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                    {translations.paymentSuccess}
                                </h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    {mergedTranslations.unlockingReport}
                                </p>
                                <Loader2 className="w-6 h-6 text-emerald-600 animate-spin mx-auto" />
                            </div>
                        ) : elementsOptions ? (
                            <Elements stripe={stripePromise} options={elementsOptions}>
                                <PaymentForm
                                    onSuccess={handleSuccess}
                                    onError={handleError}
                                    testId={testId}
                                    amount={amount}
                                    locale={locale}
                                    translations={mergedTranslations}
                                />
                            </Elements>
                        ) : (
                            <div className="flex items-center justify-center py-8 sm:py-12">
                                <div className="flex flex-col items-center space-y-4">
                                    <Loader2 className="w-8 h-8 text-emerald-600 animate-spin" />
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {mergedTranslations.initializingPayment}
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* 错误信息 - 响应式样式 */}
                        {error && (
                            <div className="mt-4 p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                <div className="flex items-start space-x-2 sm:space-x-3">
                                    <AlertTriangle className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                                    <p className="text-xs sm:text-sm text-red-700 dark:text-red-300">
                                        {error}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* 底部取消按钮 - 弱化设计，降低干扰 */}
                {!success && (
                    <div className="p-4 sm:p-6 border-t border-gray-100 dark:border-gray-700 flex-shrink-0">
                        <button
                            onClick={handleClose}
                            className="w-full bg-transparent hover:bg-gray-50 dark:hover:bg-gray-700/30 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-normal py-2 sm:py-2.5 px-4 rounded-lg transition-all duration-200 text-sm border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 min-h-[40px]"
                        >
                            {translations.cancel}
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
} 