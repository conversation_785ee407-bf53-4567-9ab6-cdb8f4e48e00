'use client';

import { useEffect, useRef } from 'react';

interface IframeResizerProps {
  testType: 'autism' | 'adhd';
  observeElement?: string; // CSS selector of element to observe for size changes
  resizeInterval?: number; // Milliseconds between resize checks
  compactMode?: boolean; // 是否使用紧凑模式，适合博客文章中的嵌入
}

export default function IframeResizer({ 
  testType, 
  observeElement = '#root', 
  resizeInterval = 300, // 更快的间隔检查
  compactMode = true // 默认启用紧凑模式，适合博客文章
}: IframeResizerProps) {
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const lastHeightRef = useRef<number>(0);
  const resizeAttemptsRef = useRef<number>(0);
  
  // Function to measure and send height
  const sendHeight = () => {
    if (typeof window === 'undefined' || window.self === window.top) {
      // Not in an iframe, no need to run
      return;
    }
    
    // Get current document height
    const height = Math.max(
      document.body.scrollHeight,
      document.documentElement.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.offsetHeight,
      document.body.clientHeight,
      document.documentElement.clientHeight
    );
    
    // 确保我们有高度变化或者这是初始设置
    const shouldUpdate = height !== lastHeightRef.current || resizeAttemptsRef.current < 3;
    
    if (shouldUpdate) {
      lastHeightRef.current = height;
      resizeAttemptsRef.current++;
      
      // 根据模式添加不同的填充空间
      const padding = compactMode ? 16 : 40; // 紧凑模式使用更小的填充
      const adjustedHeight = height + padding;
      
      // 向父窗口发送消息
      window.parent.postMessage({
        type: 'resize-iframe',
        height: adjustedHeight,
        test: testType,
        compact: compactMode
      }, '*');
      
      // 打印日志以便调试
      console.log(`[IframeResizer] 发送高度更新: ${adjustedHeight}px (原始: ${height}px, 模式: ${compactMode ? '紧凑' : '标准'})`);
    }
  };
  
  // 设置 ResizeObserver 以获得更精确的大小调整
  useEffect(() => {
    if (typeof window === 'undefined' || window.self === window.top) {
      return; // 不在 iframe 中
    }
    
    // 创建 ResizeObserver
    const resizeObserver = new ResizeObserver(() => {
      sendHeight();
    });
    
    // 观察多个可能的目标元素
    const targetElementSelectors = [
      observeElement,
      '.container',
      'main',
      '#root',
      '#raadsr-test-container',
      '#aq10-test-container',
      '#adhd-prescreening-container'
    ];
    
    // 尝试观察找到的所有元素
    let observedElements = 0;
    targetElementSelectors.forEach(selector => {
      if (selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          resizeObserver.observe(el);
          observedElements++;
        });
      }
    });
    
    // 如果没有找到特定元素，就观察 body
    if (observedElements === 0) {
      resizeObserver.observe(document.body);
    }
    
    // 清理
    return () => {
      resizeObserver.disconnect();
    };
  }, [observeElement, sendHeight]);
  
  // 设置间隔检查作为备用
  useEffect(() => {
    if (typeof window === 'undefined' || window.self === window.top) {
      return; // 不在 iframe 中
    }
    
    // 初始高度发送
    sendHeight();
    
    // 设置周期性检查的间隔
    timerRef.current = setInterval(sendHeight, resizeInterval);
    
    // 卸载时清理间隔
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [resizeInterval, sendHeight]);
  
  // 在各种事件上检查高度变化
  useEffect(() => {
    if (typeof window === 'undefined' || window.self === window.top) {
      return; // 不在 iframe 中
    }
    
    const handleUpdate = () => {
      resizeAttemptsRef.current = 0; // 重置尝试计数以确保更新
      sendHeight();
    };
    
    // 标准事件
    window.addEventListener('load', handleUpdate);
    window.addEventListener('DOMContentLoaded', handleUpdate);
    window.addEventListener('resize', handleUpdate);
    
    // 内容变化事件
    document.addEventListener('click', handleUpdate);
    document.addEventListener('submit', handleUpdate);
    document.addEventListener('input', handleUpdate);
    
    // 图片和资源加载
    window.addEventListener('error', handleUpdate); // 捕获资源加载错误
    window.addEventListener('loadeddata', handleUpdate);
    window.addEventListener('loadedmetadata', handleUpdate);
    
    // 定时额外检查，确保在动态内容加载后能正确调整大小
    const delayedChecks = [100, 500, 1000, 2000, 5000];
    delayedChecks.forEach(delay => {
      setTimeout(handleUpdate, delay);
    });
    
    // 清理
    return () => {
      window.removeEventListener('load', handleUpdate);
      window.removeEventListener('DOMContentLoaded', handleUpdate);
      window.removeEventListener('resize', handleUpdate);
      document.removeEventListener('click', handleUpdate);
      document.removeEventListener('submit', handleUpdate);
      document.removeEventListener('input', handleUpdate);
      window.removeEventListener('error', handleUpdate);
      window.removeEventListener('loadeddata', handleUpdate);
      window.removeEventListener('loadedmetadata', handleUpdate);
    };
  }, [sendHeight]);
  
  // 此组件不渲染任何内容
  return null;
} 