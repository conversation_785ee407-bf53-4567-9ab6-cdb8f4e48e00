'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import LockedReportUnlock from './LockedReportUnlock';
import ReportTemplate from './ReportTemplate';

interface RealContentLockedReportProps {
    id: string;
    locale: string;
    type: 'adhd' | 'autism';
    email?: string;
    userId?: string;
    onUnlock: () => void;
    translations: any;
    testType?: 'raadsrTest' | 'aq10Test' | 'adhdAdultTest' | 'adhdChildTest';
    answers?: Record<number, number>;
    scores?: any;
}

export default function RealContentLockedReport({
    id,
    locale,
    type,
    email,
    userId,
    onUnlock,
    translations,
    testType,
    answers,
    scores
}: RealContentLockedReportProps) {
    const [realContent, setRealContent] = useState<string | null>(null);
    const [isLoadingContent, setIsLoadingContent] = useState(true);
    const [contentError, setContentError] = useState<string | null>(null);

    // Fetch real report content for preview
    useEffect(() => {
        const fetchRealContent = async () => {
            if (!answers || !scores || !testType) {
                setIsLoadingContent(false);
                return;
            }

            try {
                setIsLoadingContent(true);
                setContentError(null);

                const endpoint = type === 'adhd' ? '/api/adhd/report' : '/api/autism/report';
                
                const requestData = type === 'adhd' ? {
                    testType: testType.replace('Test', '').replace('adhd', '').replace('Adult', 'adult').replace('Child', 'child'),
                    answers,
                    scores,
                    locale
                } : {
                    type: testType,
                    answers,
                    score: scores,
                    locale
                };

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept-Language': locale,
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch content');
                }

                const data = await response.json();
                if (data.report) {
                    setRealContent(data.report);
                }
            } catch (error) {
                console.error('Error fetching real content for preview:', error);
                setContentError('Failed to load content preview');
            } finally {
                setIsLoadingContent(false);
            }
        };

        fetchRealContent();
    }, [id, type, testType, answers, scores, locale]);

    // Render content with blur effect
    const renderBlurredContent = () => {
        if (isLoadingContent) {
            return (
                <div className="space-y-6 p-6">
                    {/* Loading skeleton */}
                    <div className="animate-pulse space-y-4">
                        <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                        <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded"></div>
                            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                        </div>
                        <div className="h-6 bg-gray-200 rounded w-1/2 mt-6"></div>
                        <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded"></div>
                            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                        </div>
                    </div>
                </div>
            );
        }

        if (contentError || !realContent) {
            // Fallback to template if real content fails to load
            return (
                <ReportTemplate
                    testType={testType || 'adhdAdultTest'}
                    locale={locale}
                />
            );
        }

        // Show real content with blur effect
        return (
            <div className="relative">
                <div className="prose dark:prose-invert max-w-none p-6">
                    <ReactMarkdown
                        rehypePlugins={[rehypeRaw, rehypeHighlight]}
                        remarkPlugins={[remarkGfm]}
                    >
                        {realContent}
                    </ReactMarkdown>
                </div>
            </div>
        );
    };

    return (
        <LockedReportUnlock
            id={id}
            locale={locale}
            type={type}
            email={email}
            userId={userId}
            onUnlock={onUnlock}
            translations={translations}
        >
            {renderBlurredContent()}
        </LockedReportUnlock>
    );
}
