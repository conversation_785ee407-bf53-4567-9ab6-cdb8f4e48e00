'use client'

import { useState } from 'react';
import { notifyFeishu } from '@/utils/notifyFeishu';

interface ErrorReportModalProps {
    isOpen: boolean;
    onClose: () => void;
    locale?: string;
    testId?: string;
    testType?: string;
    reportType?: string;
}

export default function ErrorReportModal({
    isOpen,
    onClose,
    locale = 'en',
    testId,
    testType,
    reportType
}: ErrorReportModalProps) {
    const [description, setDescription] = useState('');
    const [email, setEmail] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitted, setSubmitted] = useState(false);

    const translations = {
        en: {
            title: 'Report an Issue',
            description: 'Help us improve by reporting any issues you encountered',
            emailLabel: 'Email (optional)',
            emailPlaceholder: '<EMAIL>',
            issueLabel: 'Describe the issue',
            issuePlaceholder: 'Please describe what went wrong...',
            submitButton: 'Submit Report',
            submitting: 'Submitting...',
            cancel: 'Cancel',
            successTitle: 'Thank you!',
            successMessage: 'Your report has been submitted. We appreciate your feedback.',
            close: 'Close',
            required: 'Please describe the issue before submitting.',
            errorSubmitting: 'Failed to submit report. Please try again.'
        },
        zh: {
            title: '报告问题',
            description: '帮助我们改进，请报告您遇到的任何问题',
            emailLabel: '邮箱（可选）',
            emailPlaceholder: '<EMAIL>',
            issueLabel: '描述问题',
            issuePlaceholder: '请描述遇到的问题...',
            submitButton: '提交报告',
            submitting: '提交中...',
            cancel: '取消',
            successTitle: '谢谢！',
            successMessage: '您的报告已提交。我们感谢您的反馈。',
            close: '关闭',
            required: '请在提交前描述问题。',
            errorSubmitting: '提交报告失败，请重试。'
        },
        es: {
            title: 'Reportar un Problema',
            description: 'Ayúdanos a mejorar reportando cualquier problema que hayas encontrado',
            emailLabel: 'Email (opcional)',
            emailPlaceholder: '<EMAIL>',
            issueLabel: 'Describe el problema',
            issuePlaceholder: 'Por favor describe qué salió mal...',
            submitButton: 'Enviar Reporte',
            submitting: 'Enviando...',
            cancel: 'Cancelar',
            successTitle: '¡Gracias!',
            successMessage: 'Tu reporte ha sido enviado. Apreciamos tu comentario.',
            close: 'Cerrar',
            required: 'Por favor describe el problema antes de enviar.',
            errorSubmitting: 'Error al enviar el reporte. Por favor intenta nuevamente.'
        },
        fr: {
            title: 'Signaler un Problème',
            description: 'Aidez-nous à améliorer en signalant les problèmes rencontrés',
            emailLabel: 'Email (optionnel)',
            emailPlaceholder: '<EMAIL>',
            issueLabel: 'Décrivez le problème',
            issuePlaceholder: 'Veuillez décrire ce qui s\'est mal passé...',
            submitButton: 'Envoyer le Rapport',
            submitting: 'Envoi en cours...',
            cancel: 'Annuler',
            successTitle: 'Merci !',
            successMessage: 'Votre rapport a été soumis. Nous apprécions vos commentaires.',
            close: 'Fermer',
            required: 'Veuillez décrire le problème avant de soumettre.',
            errorSubmitting: 'Échec de l\'envoi du rapport. Veuillez réessayer.'
        },
        de: {
            title: 'Problem Melden',
            description: 'Helfen Sie uns zu verbessern, indem Sie Probleme melden',
            emailLabel: 'E-Mail (optional)',
            emailPlaceholder: '<EMAIL>',
            issueLabel: 'Problem beschreiben',
            issuePlaceholder: 'Bitte beschreiben Sie, was schief gelaufen ist...',
            submitButton: 'Bericht Senden',
            submitting: 'Wird gesendet...',
            cancel: 'Abbrechen',
            successTitle: 'Vielen Dank!',
            successMessage: 'Ihr Bericht wurde übermittelt. Wir schätzen Ihr Feedback.',
            close: 'Schließen',
            required: 'Bitte beschreiben Sie das Problem vor dem Senden.',
            errorSubmitting: 'Bericht konnte nicht gesendet werden. Bitte versuchen Sie es erneut.'
        },
        ja: {
            title: '問題を報告',
            description: '遭遇した問題を報告して改善にご協力ください',
            emailLabel: 'メール（任意）',
            emailPlaceholder: '<EMAIL>',
            issueLabel: '問題を説明',
            issuePlaceholder: '何が問題だったかを説明してください...',
            submitButton: 'レポートを送信',
            submitting: '送信中...',
            cancel: 'キャンセル',
            successTitle: 'ありがとうございます！',
            successMessage: 'レポートが送信されました。フィードバックをありがとうございます。',
            close: '閉じる',
            required: '送信前に問題を説明してください。',
            errorSubmitting: 'レポートの送信に失敗しました。もう一度お試しください。'
        }
    };

    const t = translations[locale as keyof typeof translations] || translations.en;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!description.trim()) {
            alert(t.required);
            return;
        }

        setIsSubmitting(true);

        try {
            // 收集系统信息
            const userAgent = navigator.userAgent;
            const timestamp = new Date().toISOString();
            const url = window.location.href;
            const screenSize = `${window.innerWidth}x${window.innerHeight}`;

            // 构建报告消息
            const reportMessage = `
🐛 Error Report
Time: ${timestamp}
URL: ${url}
Test ID: ${testId || 'N/A'}
Test Type: ${testType || 'N/A'}
Report Type: ${reportType || 'N/A'}
Language: ${locale}
Email: ${email || 'Not provided'}
User Agent: ${userAgent}
Screen Size: ${screenSize}

Issue Description:
${description}
      `.trim();

            // 发送到飞书
            await notifyFeishu.keyNotify(reportMessage);

            setSubmitted(true);
        } catch (error) {
            console.error('Failed to submit error report:', error);
            alert(t.errorSubmitting);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleClose = () => {
        setDescription('');
        setEmail('');
        setSubmitted(false);
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            {/* 背景遮罩 */}
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <div
                    className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                    onClick={handleClose}
                />

                {/* 模态内容 */}
                <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                    {submitted ? (
                        // 成功状态
                        <div className="text-center">
                            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
                                <svg className="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                </svg>
                            </div>
                            <div className="mt-3 text-center sm:mt-5">
                                <h3 className="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">
                                    {t.successTitle}
                                </h3>
                                <div className="mt-2">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        {t.successMessage}
                                    </p>
                                </div>
                            </div>
                            <div className="mt-5 sm:mt-6">
                                <button
                                    type="button"
                                    className="inline-flex w-full justify-center rounded-md bg-emerald-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-emerald-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-600 dark:bg-emerald-500 dark:hover:bg-emerald-400"
                                    onClick={handleClose}
                                >
                                    {t.close}
                                </button>
                            </div>
                        </div>
                    ) : (
                        // 表单状态
                        <>
                            <div className="mb-4">
                                <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
                                    {t.title}
                                </h3>
                                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    {t.description}
                                </p>
                            </div>

                            <form onSubmit={handleSubmit} className="space-y-4">
                                {/* 邮箱输入 */}
                                <div>
                                    <label htmlFor="error-email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {t.emailLabel}
                                    </label>
                                    <input
                                        type="email"
                                        id="error-email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        placeholder={t.emailPlaceholder}
                                        className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:border-emerald-400 dark:focus:ring-emerald-400"
                                    />
                                </div>

                                {/* 问题描述 */}
                                <div>
                                    <label htmlFor="error-description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {t.issueLabel} *
                                    </label>
                                    <textarea
                                        id="error-description"
                                        rows={4}
                                        value={description}
                                        onChange={(e) => setDescription(e.target.value)}
                                        placeholder={t.issuePlaceholder}
                                        required
                                        className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:border-emerald-400 dark:focus:ring-emerald-400"
                                    />
                                </div>

                                {/* 按钮 */}
                                <div className="flex flex-col-reverse gap-3 sm:flex-row sm:justify-end">
                                    <button
                                        type="button"
                                        onClick={handleClose}
                                        disabled={isSubmitting}
                                        className="inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:text-gray-100 dark:ring-gray-600 dark:hover:bg-gray-600"
                                    >
                                        {t.cancel}
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={isSubmitting || !description.trim()}
                                        className="inline-flex justify-center rounded-md bg-emerald-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-emerald-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-emerald-500 dark:hover:bg-emerald-400"
                                    >
                                        {isSubmitting ? (
                                            <>
                                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                {t.submitting}
                                            </>
                                        ) : (
                                            t.submitButton
                                        )}
                                    </button>
                                </div>
                            </form>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
} 