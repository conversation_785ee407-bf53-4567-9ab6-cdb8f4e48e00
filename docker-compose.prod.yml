services:
  raads_plus:
    image: blowxian/raads_plus:latest
    container_name: raads_plus
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    ports:
      - "3002:3000"
    environment:
      NODE_ENV: production
      GOOGLE_APPLICATION_CREDENTIALS: /app/google_application_credentials.json
    env_file:
      - .env.production
    volumes:
      - ./google_application_credentials.json:/app/google_application_credentials.json:ro
    depends_on:
      - raads_plus_db
    restart: unless-stopped

  raads_plus_db:
    image: postgres:alpine
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    env_file:
      - .env.production
    volumes:
      - ./postgres_data_prod:/var/lib/postgresql/data
    container_name: raads_plus_db
    restart: unless-stopped

volumes:
  postgres_data_prod:






