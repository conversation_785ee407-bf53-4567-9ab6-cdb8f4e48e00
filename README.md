# TMPL Lite

一个使用 Next.js、Prisma 和 PostgreSQL 构建的轻量级模板应用。

## 目录

- [项目结构](#项目结构)
- [快速开始](#快速开始)
- [环境变量配置](#环境变量配置)
- [数据库模型](#数据库模型)
- [开发指南](#开发指南)
- [部署指南](#部署指南)
- [脚本说明](#脚本说明)

## 项目结构

```
/
├── app/                    # Next.js 应用目录
│   └── [locale]/           # 国际化路由
├── components/             # React 组件
├── hooks/                  # 自定义 React hooks
├── i18n/                   # 国际化配置
├── locales/                # 翻译文件
├── prisma/                 # Prisma 模式和迁移
├── public/                 # 静态文件
├── utils/                  # 工具函数
├── Dockerfile              # Docker 构建配置
├── docker-compose.yml      # 开发环境 Docker 配置
├── docker-compose.prod.yml # 生产环境 Docker 配置
├── build.sh                # 构建生产镜像脚本
├── deploy.sh               # 部署生产环境脚本
└── init.sh                 # 项目初始化脚本
```

## 快速开始

### 本地开发

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动开发环境**
   ```bash
   # 仅启动数据库
   docker-compose up -d raads_plus_db
   
   # 或启动完整开发环境
   docker-compose up -d
   
   # 启动开发服务器
   npm run dev
   ```

3. **数据库设置**
   ```bash
   # 运行迁移
   npx prisma migrate dev
   
   # 生成客户端
   npx prisma generate
   ```

### 本地测试生产环境

```bash
# 使用测试脚本
./test-prod.sh

# 或使用高级测试脚本（支持更多选项）
./test-prod-advanced.sh [--debug] [--clean] [--reset-db] [--force-rebuild] [--skip-build]
```

访问：
- 应用：http://localhost:3001
- 数据库：localhost:5433

## 环境变量配置

### 配置文件

| 文件 | 用途 | Git 管理 |
|------|------|----------|
| `.env` | 本地开发环境 | ❌ 不提交 |
| `.env.production` | 生产环境 | ❌ 不提交 |
| `.env.example` | 示例配置 | ✅ 提交 |

### 主要配置项

<details>
<summary><b>应用程序配置</b> (点击展开)</summary>

```
# 环境标识
NODE_ENV=development|production

# 应用程序信息
NEXT_PUBLIC_APP_NAME="TMPL Lite"
NEXT_PUBLIC_APP_DESCRIPTION="A lightweight template application"
NEXT_PUBLIC_PROJECT_IDENTIFIER=raads_plus

# API 和站点 URL
NEXT_PUBLIC_API_URL=http://localhost:3000|https://api.tmpl-lite.com
NEXT_PUBLIC_SITE_URL=http://localhost:3000|https://domain.com
NEXTAUTH_URL=http://localhost:3000|https://domain.com
```
</details>

<details>
<summary><b>数据库配置</b> (点击展开)</summary>

```
# 数据库凭证
POSTGRES_USER=raads_plus_admin
POSTGRES_PASSWORD=raads_plus_2024|raads_plus_2024_prod
POSTGRES_DB=raads_plus

# 数据库连接 URL
DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/${POSTGRES_DB}?schema=public"
```
</details>

<details>
<summary><b>安全配置</b> (点击展开)</summary>

```
# 安全密钥
NEXTAUTH_SECRET=raads_plus_dev_secret|generate_a_strong_secret_key_here
API_SECRET_KEY=dev_api_secret_key|generate_a_strong_api_key_here
```
</details>

<details>
<summary><b>第三方服务配置</b> (点击展开)</summary>

```
# Google OAuth
NEXT_PUBLIC_GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Google Cloud
GOOGLE_CLOUD_PROJECT=
GOOGLE_CLOUD_LOCATION=
GOOGLE_APPLICATION_CREDENTIALS=./google_application_credentials.json|/app/google_application_credentials.json

# 分析和监控
NEXT_PUBLIC_GA_ID=
NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL=
NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL=
```
</details>

## 数据库模型

### User
- 用户基本信息（ID、邮箱、名称、头像）
- 创建和更新时间
- 与 Todo 的关联

### Todo
- 任务管理（标题、描述、完成状态）
- 创建和更新时间
- 与用户的关联

## 开发指南

### 最佳实践

- **环境变量**：不要提交敏感信息到 Git
- **数据库**：
  - 始终为架构更改创建迁移
  - 在部署前在本地测试迁移
  - 定期备份生产数据库
- **Docker**：
  - 开发：使用 `docker-compose.yml`
  - 本地测试生产：使用 `docker-compose.prod.local.yml`
  - 生产部署：使用 `docker-compose.prod.yml`

### 项目初始化

如果要基于此模板创建新项目：

```bash
./init.sh
```

此脚本将引导您：
1. 输入新项目名称
2. 配置生产环境域名
3. 设置飞书 Webhook
4. 配置 Docker 和数据库信息

## 部署指南

### 构建生产镜像

```bash
# 标准构建（多架构）
./build.sh

# 仅本地构建
./build.sh --local-only

# 跳过推送
./build.sh --skip-push

# 单架构构建
./build.sh --single-arch
```

### 部署到生产服务器

```bash
# 标准部署
./deploy.sh

# 跳过拉取镜像
./deploy.sh --skip-pull

# 使用本地构建
./deploy.sh --local-build
```

部署流程：
1. 拉取最新的 Docker 镜像
2. 停止现有容器
3. 启动新容器
4. 运行数据库迁移

## 脚本说明

### 版本控制的脚本（应提交到 Git）

| 脚本 | 描述 | 参数 |
|------|------|------|
| **build.sh** | 构建 Docker 镜像 | `--local-only`、`--skip-push`、`--single-arch` |
| **deploy.sh** | 部署应用 | `--skip-pull`、`--local-build` |
| **Dockerfile** | 定义应用的 Docker 镜像 | - |
| **docker-compose.yml** | 开发环境配置 | - |
| **docker-compose.prod.yml** | 生产环境配置 | - |
| **init.sh** | 项目初始化 | - |

### 本地开发脚本（不应提交到 Git）

| 脚本 | 描述 | 参数 |
|------|------|------|
| **docker-compose.prod.local.yml** | 本地测试生产环境配置 | - |
| **test-prod.sh** | 本地测试生产环境基本脚本 | - |
| **test-prod-advanced.sh** | 高级本地测试脚本 | `--debug`、`--clean`、`--reset-db`、`--force-rebuild`、`--skip-build` |
| **docker-cleanup.sh** | 清理 Docker 资源 | - |
| **reset-env.sh** | 重置环境 | - |

### 多架构支持

项目脚本支持在不同架构间构建和部署：
- 在 macOS (ARM64/AMD64) 上开发
- 部署到 Linux 服务器 (AMD64)
- 自动检测系统架构并设置相应的 Docker 平台
- 使用 Docker buildx 构建多架构镜像

## Recent Updates - AI Consultation & Report Unlock Fixes (Jan 2025)

### Fixed Issues:

#### 1. AI Consultation Free Limit Enforcement
**Problem**: Users could ask 2 questions before being prompted to upgrade instead of just 1.
**Solution**: Modified `utils/chatService.ts` to enforce the free consultation limit correctly:
- Changed condition from `userMessageCount > 0` to `userMessageCount >= 1`
- Now properly blocks access after the first question for free users

#### 2. Report Unlock Modal Enhancement
**Problem**: When clicking upgrade button from one-time payment unlocked reports, the modal didn't properly reflect the report's payment status.
**Solution**: Enhanced `components/LockedReportUnlock.tsx`:
- Added report status checking via `/api/check-report-access`
- Dynamically adjusts modal display based on payment status
- Shows "Completed" status for already-paid reports
- Conditionally displays FREE badge only when relevant
- Auto-selects appropriate tab (payment/share) based on context

#### 3. User Experience Improvements
- Payment status is checked on component load
- Visual indicators (green checkmarks) for completed payments
- Disabled state for already-unlocked options
- Improved responsive design for different screen sizes

### Technical Changes:
- **File Modified**: `utils/chatService.ts` - Line 95
- **File Modified**: `components/LockedReportUnlock.tsx` - Multiple sections
- **API Integration**: Enhanced usage of `/api/check-report-access` endpoint

### Testing:
- ✅ Build passes without errors
- ✅ TypeScript validation successful
- ✅ ESLint warnings resolved
- ✅ Multi-language support maintained (EN/ZH)

### 3. Upgrade Subscription Button Click Issue
**Problem**: When users clicked the "Upgrade Subscription" button in AI consultation chat, nothing happened on unlocked report pages.
**Root Cause**: The upgrade button only triggered a global `openUnlockModal` event, which was only handled by the `LockedReportUnlock` component. On unlocked report pages, this component doesn't exist, so the event had no listener.
**Solution**: Enhanced the upgrade click handlers in both `ChatConsultation` and `ReportSidebar` components:
- Added event confirmation mechanism using `openUnlockModalHandled` event
- Implemented fallback logic that creates Stripe checkout session directly when no unlock modal is available
- Added proper error handling for unauthenticated users (redirects to sign-in)
- Added fallback redirect to homepage if all else fails

**Technical Changes**:
- Modified `handleUpgradeClick` in `components/ChatConsultation.tsx` and `components/ReportSidebar.tsx`
- Added event confirmation in `components/LockedReportUnlock.tsx`
- Default subscription plan: Pro Monthly ($9.99/month)

**User Experience**:
- ✅ Authenticated users: Direct redirect to Stripe checkout
- ✅ Unauthenticated users: Redirect to sign-in page with return URL
- ✅ Maintains current page context through URL parameters
- ✅ Consistent behavior across all report types and pages

## Summary

All reported issues have been successfully resolved:
1. ✅ AI consultation now properly limits to 1 free question per report
2. ✅ One-time payment unlock modal properly shows report status and disables completed options  
3. ✅ Upgrade subscription button now works reliably on all pages

The fixes maintain backward compatibility and enhance the user experience across both logged-in and anonymous users.

## 调试指南 - AI咨询免费限制问题

### 问题描述
用户报告在免费模式下可以在AI咨询中问两个问题，而不是预期的只能问一个问题。

### 日志系统
我们已经添加了完整的前后端日志系统来帮助调试这个问题。

#### 前端日志 (浏览器控制台)
- **发送消息时**: `=== FRONTEND: ChatConsultation.sendMessage START ===`
- **保存对话时**: `=== FRONTEND: saveCompletedConversation START ===`

#### 后端日志 (服务器控制台)
- **访问检查**: `=== CONSULTATION ACCESS CHECK START ===`
- **消息保存**: `=== SAVE MESSAGE API START ===`
- **ChatService访问检查**: `--- ChatService.checkConsultationAccess ---`
- **ChatService消息添加**: `--- ChatService.addMessage ---`

### 调试步骤

1. **打开浏览器开发者工具**，切换到Console选项卡

2. **在AI咨询中发送第一个问题**，观察日志序列：
   ```
   === FRONTEND: ChatConsultation.sendMessage START ===
   === CONSULTATION ACCESS CHECK START ===
   --- ChatService.checkConsultationAccess ---
   ✅ Decision: ALLOW - Free consultation available
   === FRONTEND: saveCompletedConversation START ===
   === SAVE MESSAGE API START ===
   --- ChatService.addMessage --- (用户消息)
   --- ChatService.addMessage --- (AI回复)
   ```

3. **立即发送第二个问题**，观察关键指标：
   - `userMessageCount`: 应该在第一个问题后变成1
   - `hasUsedFreeConsultation`: 应该在第二次检查时为true
   - 决策结果: 应该是`❌ Decision: DENY`

### 关键日志字段说明

#### 访问检查阶段
- `Current userMessageCount`: 当前用户消息计数
- `hasUsedFreeConsultation`: 是否已使用免费咨询
- `🔍 Path`: 用户类型路径 (NO_USER_ID/FREE_USER/SUBSCRIPTION_USER)
- `Decision`: 最终决策 (ALLOW/DENY)

#### 消息保存阶段
- `newUserMessageCount`: 新的用户消息计数
- `previousUserMessageCount`: 之前的用户消息计数
- `messageRole`: 消息角色 (user/assistant)
- `isUserMessage`: 是否为用户消息

### 预期行为

**第一个问题**:
```
userMessageCount: 0 → hasUsedFreeConsultation: false → ALLOW
保存后: userMessageCount: 1
```

**第二个问题**:
```
userMessageCount: 1 → hasUsedFreeConsultation: true → DENY (403 Forbidden)
```

### 可能的问题原因

1. **时序问题**: 访问检查在消息保存之前，可能存在竞态条件
2. **数据库更新延迟**: 消息计数更新可能没有立即生效
3. **缓存问题**: 可能存在数据缓存导致的延迟
4. **并发问题**: 多个请求同时到达时可能产生竞态条件

### 故障排除

如果发现问题：

1. **检查时间戳**: 确认请求的时序
2. **对比userMessageCount**: 查看访问检查时和保存后的计数
3. **确认数据库状态**: 检查`chatConversation`表中的实际数据
4. **查看错误日志**: 任何异常都会在控制台显示

### 调试命令

开发环境下可以使用以下方法直接检查数据库状态：

```javascript
// 在浏览器控制台中检查特定测试的对话状态
fetch('/api/chat-consultation/history', {
  method: 'POST', 
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({testResultId: 'YOUR_TEST_ID'})
}).then(r => r.json()).then(console.log)
```

通过这些详细的日志，我们可以精确地追踪每个请求的处理过程，并识别为什么免费用户能够发送第二个问题。

## Recent Fixes

### 🔧 报告重复生成问题修复 (2025-07-01)

**问题**: 每次访问结果页面时，系统都会重新生成报告，即使数据库中已经有保存的报告。

**现象**: 
- 从服务器日志可以看到报告生成成功，但保存时被跳过（"报告已存在且未指定强制覆盖，跳过保存"）
- 用户每次刷新页面都会看到新的报告内容
- 浪费API调用和服务器资源

**根本原因**:
1. **组件初始化逻辑问题**: `AutismReport`组件每次挂载时都会重新初始化
2. **缓存状态不同步**: 内存缓存在页面刷新时被清空，但数据库中仍有报告
3. **检查逻辑不充分**: 组件没有正确检测到数据库中已存在的报告
4. **并发控制不足**: 多个组件实例可能同时尝试生成报告

**修复方案**:
1. **增强初始化检查**: 在`initializeReport`中添加更严格的数据库检查
2. **改进获取逻辑**: 在`fetchSavedReport`中增加详细日志，更好地诊断API调用
3. **防止重复生成**: 在`generateNewReport`中添加二次检查，非强制模式下先确认数据库中确实没有报告
4. **优化缓存管理**: 改进内存缓存的使用和清理逻辑

**关键改进**:
- 📊 增加详细的前端日志，使用emoji标识不同操作类型
- 🔍 在生成新报告前进行二次数据库检查
- 💾 改进报告保存和缓存同步逻辑
- 🚫 防止并发情况下的重复生成

**测试验证**:
现在访问已有报告的结果页面时，应该看到类似的日志：
```
[AutismReport] 🔍 开始从数据库获取报告: testId-locale
[AutismReport] ✅ 报告获取成功并缓存: testId-locale, 长度: xxxx字符
[AutismReport] ✅ 发现已保存的报告，直接显示（避免重新生成）
```

**修改文件**:
- `app/components/AutismReport.tsx`: 优化初始化和生成逻辑

---

### 🔧 AI Consultation Free Limit Issue (2025-07-01)

**问题**: 用户可以问第二个问题而不触发使用限制，虽然系统设计为只允许一个免费问题。

**根本原因**: 
第一个问题的对话没有被正确保存到数据库，导致系统认为用户还没有使用过免费咨询次数。

**详细分析**:
1. 在流式响应完成时，`saveCompletedConversation` 函数依赖于 `messages` state 来查找最后一条用户消息
2. 由于React状态更新是异步的，当`saveCompletedConversation`被调用时，`setMessages`的更新可能还没有完成
3. 这导致函数内部访问`messages`时看到的是旧状态，找不到用户消息，从而跳过保存
4. 第一个问题没有被保存，所以第二个问题仍然被允许

**修复方案**:
- 修改`handleStreamResponse`和`saveCompletedConversation`函数签名，直接传递用户消息内容而不是依赖state
- 在`sendMessage`函数中保存当前用户消息内容
- 修复了流式响应和非流式响应两种情况
- 同时修复了`ChatConsultation.tsx`和`ReportSidebar.tsx`

**修改文件**:
- `components/ChatConsultation.tsx`: 更新流式响应处理和对话保存逻辑
- `components/ReportSidebar.tsx`: 更新流式响应处理和对话保存逻辑

**测试建议**:
使用前端控制台日志监控对话保存过程，确保每个用户消息都被正确保存。

---

## 调试指南

### AI 咨询免费限制调试

使用浏览器开发者工具的控制台来跟踪AI咨询的交互流程：

**前端日志** (浏览器控制台):
- `sendMessage` 开始: 显示消息内容、当前消息数量、对话状态
- `saveCompletedConversation`: 显示用户消息内容、响应长度、保存状态

**后端日志** (服务器控制台):
- `/api/chat-consultation`: 访问控制检查、用户信息、决策推理
- `checkConsultationAccess`: 详细决策逻辑和用户消息计数
- `/api/chat-consultation/save-message`: 消息保存过程和数据库更新

**关键调试信息**:
- 当前 `userMessageCount` 在每个步骤
- `hasUsedFreeConsultation` 状态
- 用户类型路径 (匿名/免费/订阅)
- 最终决策 (ALLOW/DENY) 及原因
- 保存前后的消息计数变化

### 如何调试免费限制问题:

1. **打开浏览器开发者工具控制台**
2. **发送第一个问题**，观察日志:
   ```
   === FRONTEND: sendMessage START ===
   === FRONTEND: saveCompletedConversation START ===
   ✅ Conversation saved successfully
   ```
3. **检查后端日志**，确认消息被保存且计数增加
4. **发送第二个问题**，应该看到 403 错误和限制消息

如果第二个问题被允许，检查：
- 第一个问题的保存是否成功
- `userMessageCount` 是否正确增加
- `checkConsultationAccess` 的决策逻辑

---

## 其他已修复问题

### 🔧 解锁模态框增强 (Issue 2)
增强了报告解锁模态框，能够根据支付状态动态调整显示内容。

### 🔧 升级订阅按钮修复 (Issue 3) 
修复了在已解锁报告页面点击"升级订阅"按钮无响应的问题。

---

## 最新功能更新 (Latest Feature Updates)

### 1. 用户订阅状态标签 (User Subscription Status Labels)

在UI header的用户菜单头像部分添加了订阅状态标签：

**功能特点：**
- **FREE标签**: 免费用户或无有效订阅的用户
- **PRO标签**: Pro订阅用户，显示蓝色星星图标
- **PREMIUM标签**: Premium订阅用户，显示紫色皇冠图标，渐变背景
- **响应式设计**: 支持移动端和桌面端的完美显示
- **实时更新**: 订阅状态变化时自动更新标签
- **详细信息**: 下拉菜单中显示剩余解锁次数和升级提示

**技术实现：**
- 使用 `/api/subscription/info` API获取用户订阅信息
- 动态样式根据订阅类型调整颜色和图标
- Lucide React图标库提供视觉元素
- Tailwind CSS响应式类确保多端适配

### 2. Pro用户报告解锁功能 (Pro User Report Unlock Feature)

为Pro订阅用户实现了基于次数的报告解锁系统：

**功能特点：**
- **剩余次数显示**: 解锁按钮上显示剩余解锁次数
- **一键解锁**: Pro用户可直接解锁报告，消耗月度解锁次数
- **权限验证**: 自动检查用户订阅状态和剩余次数
- **优雅降级**: 次数用完时显示升级提示
- **实时反馈**: 解锁后立即更新剩余次数和报告状态

**API端点：**
- `POST /api/unlock-single-report`: 处理Pro用户报告解锁
- 集成 `SubscriptionService.canPerformAction()` 进行权限检查
- 使用数据库事务确保解锁操作的原子性

**UI改进：**
- 在解锁浮层中添加Pro用户专属解锁选项
- 蓝色渐变设计区分Pro解锁和付费解锁
- 支持中英文界面
- 加载状态和错误处理

### 3. AI咨询次数限制和升级提示 (AI Consultation Limits & Upgrade Prompts)

完善了AI咨询的订阅限制处理：

**功能特点：**
- **Pro用户限制**: 月度15次AI咨询限制
- **次数耗尽提示**: 自动显示升级到Premium的建议
- **优雅错误处理**: 美观的限制达成提示界面
- **升级引导**: 一键跳转到订阅升级界面

**技术实现：**
- API层面的权限检查和错误返回
- 前端特殊消息类型处理 (`type: 'subscription_limit'`)
- 升级按钮集成现有的升级流程
- 详细的使用统计和重置逻辑

### 4. 系统架构改进 (System Architecture Improvements)

**订阅服务架构：**
- `SubscriptionService`: 统一的订阅状态管理
- `checkUserLimits()`: 智能的使用限制检查
- `trackUsage()`: 准确的使用量追踪
- 月度自动重置机制

**权限控制系统：**
- 基于角色的访问控制 (RBAC)
- 细粒度的功能权限检查
- 订阅状态实时验证
- 优雅的权限不足处理

**用户体验优化：**
- 订阅状态的视觉化展示
- 平滑的功能降级体验
- 直观的升级引导流程
- 响应式设计确保多端一致性

### 5. 数据库架构 (Database Schema)

**用户表扩展：**
```sql
-- 订阅相关字段
subscriptionType: 'single' | 'pro' | 'premium'
subscriptionStatus: 'active' | 'canceled' | 'past_due'
subscriptionId: string (Stripe subscription ID)
currentPeriodStart: DateTime
currentPeriodEnd: DateTime

-- 使用统计字段
monthlyReportsUsed: number
monthlyConsultationsUsed: number
lastResetDate: DateTime

-- 权限字段
hasHistoryAccess: boolean
hasVipFeatures: boolean
```

**测试结果表扩展：**
```sql
-- 解锁状态跟踪
unlockedByPayment: boolean
paymentIntentId: string (用于单次付费解锁)
```

### 6. 开发和调试支持 (Development & Debugging Support)

**日志系统：**
- 详细的订阅检查日志
- 权限验证过程追踪
- 使用统计变化记录
- 错误处理和恢复机制

**开发工具：**
- 订阅状态模拟
- 权限测试工具
- 使用量重置功能
- API响应验证

### 7. 安全和稳定性 (Security & Stability)

**安全措施：**
- 服务端权限验证
- 订阅状态加密验证
- 防止权限绕过攻击
- 数据一致性保证

**错误处理：**
- 网络请求失败恢复
- 订阅状态异常处理
- 用户界面状态同步
- 优雅的功能降级

### 8. 测试和质量保证 (Testing & Quality Assurance)

**功能测试：**
- Pro用户解锁流程测试
- 订阅状态标签显示测试
- AI咨询限制功能测试
- 升级流程完整性测试

**性能优化：**
- 订阅信息缓存策略
- API请求去重机制
- 状态更新批量处理
- 响应式加载优化

---

## 技术栈更新 (Technology Stack Updates)

### 新增依赖 (New Dependencies)
- `lucide-react`: 图标库，用于订阅状态标签
- 增强的 Stripe 集成用于订阅管理
- 改进的 Prisma 查询用于权限检查

### API路由更新 (API Routes Updates)
- `/api/subscription/info`: 获取用户订阅详细信息
- `/api/unlock-single-report`: Pro用户报告解锁
- `/api/check-report-access`: 报告访问权限检查
- 增强的 `/api/chat-consultation`: 改进的权限检查

### 组件架构 (Component Architecture)
- `UserMenu`: 订阅状态可视化
- `LockedReportUnlock`: Pro用户解锁选项
- `ChatConsultation`: 订阅限制处理
- `SubscriptionInfo`: 详细订阅信息展示

这些更新显著提升了用户体验，为不同订阅层级的用户提供了清晰的功能边界和升级路径。

---
