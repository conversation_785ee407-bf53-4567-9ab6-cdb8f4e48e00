// 自闭症测试类型迁移脚本
// 运行命令: node scripts/migrateTestTypes.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function migrateTestTypes() {
  console.log('开始迁移测试类型...');
  
  try {
    // 查找所有需要更新的自闭症测试记录
    const autismTests = await prisma.testResult.findMany({
      where: {
        OR: [
          { testType: 'autism_raadsr' },
          { testType: 'autism_aq10' }
        ]
      }
    });
    
    console.log(`找到 ${autismTests.length} 条需要更新的记录`);
    
    // 更新每条记录
    let updatedCount = 0;
    
    for (const test of autismTests) {
      const newTestType = test.testType === 'autism_raadsr' 
        ? 'raadsrTest' 
        : 'aq10Test';
      
      await prisma.testResult.update({
        where: { id: test.id },
        data: { testType: newTestType }
      });
      
      updatedCount++;
      if (updatedCount % 10 === 0) {
        console.log(`已更新 ${updatedCount}/${autismTests.length} 条记录`);
      }
    }
    
    console.log(`迁移完成，共更新了 ${updatedCount} 条记录`);
  } catch (error) {
    console.error('迁移过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateTestTypes()
  .then(() => console.log('迁移脚本执行完毕'))
  .catch(e => console.error('执行迁移脚本时出错:', e)); 