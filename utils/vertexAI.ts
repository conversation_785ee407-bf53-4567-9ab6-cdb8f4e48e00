import { VertexAI } from '@google-cloud/vertexai';
import { GoogleAuth } from 'google-auth-library';
import * as fs from 'fs';
import * as path from 'path';
import { generateContentWithOpenRouter, generateContentStreamWithOpenRouter, checkOpenRouterHealth } from './openrouterService';
import { notifyFeishu } from './notifyFeishu';

// 常量配置
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT_ID || 'projectatm-89b3a'; // 提供备用项目ID
const LOCATION = process.env.VERTEX_LOCATION || 'us-central1';
const MODEL_NAME = process.env.VERTEX_MODEL || 'gemini-1.5-pro';
const MAX_OUTPUT_TOKENS = 8192;
const TEMPERATURE = 0.2;
const TOP_P = 0.95;
const TOP_K = 40;

// Initialize authentication
const initializeAuth = () => {
  try {
    return new GoogleAuth({
      scopes: 'https://www.googleapis.com/auth/cloud-platform',
    });
  } catch (error) {
    console.error('Error initializing Google Auth:', error);
    throw error;
  }
};

// Initialize Vertex AI
let vertexAIInstance: VertexAI | null = null;

const initializeVertexAI = () => {
  if (vertexAIInstance) {
    return vertexAIInstance;
  }

  try {
    console.log(`Initializing Vertex AI client for project ${PROJECT_ID} in ${LOCATION}`);
    
    // 获取凭据文件路径
    const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    console.log(`Using credentials path: ${credentialsPath}`);
    
    // 检查凭据文件是否存在
    if (credentialsPath) {
      try {
        if (fs.existsSync(credentialsPath)) {
          console.log(`Credentials file exists at: ${credentialsPath}`);
        } else {
          console.warn(`Credentials file not found at: ${credentialsPath}`);
          console.warn('Make sure the credentials file is correctly mounted in the container');
        }
      } catch (e) {
        console.error(`Error checking credentials file:`, e);
      }
    }
    
    // 手动设置环境变量确保 Google Auth 库能找到凭据文件
    if (credentialsPath) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialsPath;
    }
    
    // 初始化VertexAI客户端
    vertexAIInstance = new VertexAI({
      project: PROJECT_ID,
      location: LOCATION
    });
    
    return vertexAIInstance;
  } catch (error) {
    console.error('Error initializing Vertex AI:', error);
    throw error;
  }
};

// Get generative model
const getGenerativeModel = (modelName = MODEL_NAME) => {
  const vertexAI = initializeVertexAI();
  return vertexAI.getGenerativeModel({
    model: modelName,
    generationConfig: {
      maxOutputTokens: MAX_OUTPUT_TOKENS,
      temperature: TEMPERATURE,
      topP: TOP_P,
      topK: TOP_K,
    },
    // Safety settings removed due to type compatibility issues
  });
};

/**
 * 生成AI内容，包含Vertex AI和OpenRouter降级方案
 */
export async function generateAIContentWithFallback(
  prompt: string,
  locale: string = 'en',
  modelName: string = MODEL_NAME
): Promise<string> {
  const startTime = Date.now();
  let content = '';
  let error = null;

  // 首先尝试使用 Vertex AI
  try {
    console.log('[vertexAI] Attempting to generate content with Vertex AI...');
    const generativeModel = getGenerativeModel(modelName);

    const result = await generativeModel.generateContent(prompt);
    const response = await result.response;

    if (response.candidates && response.candidates.length > 0) {
      const textParts = response.candidates[0].content.parts;
      content = textParts.map(part => part.text).join('');
    }

    if (content && content.trim().length > 0) {
      const responseTime = Date.now() - startTime;

      // 记录成功的API调用
      await notifyFeishu.paidApiLog({
        provider: 'vertex-ai',
        model: modelName,
        prompt,
        response: content,
        success: true,
        responseTime
      });

      console.log(`[vertexAI] ✅ Successfully generated content with Vertex AI (${responseTime}ms)`);
      return content;
    } else {
      throw new Error('Vertex AI returned empty content');
    }

  } catch (vertexError) {
    error = vertexError instanceof Error ? vertexError.message : String(vertexError);
    console.error('[vertexAI] ❌ Vertex AI failed:', error);

    const responseTime = Date.now() - startTime;

    // 记录失败的API调用
    await notifyFeishu.paidApiLog({
      provider: 'vertex-ai',
      model: modelName,
      prompt,
      success: false,
      responseTime,
      error
    });

    // 发送AI生成错误通知
    await notifyFeishu.aiGenError({
      provider: 'vertex-ai',
      model: modelName,
      error,
      prompt,
      endpoint: 'utils/vertexAI',
      fallbackUsed: false
    });
  }

  // 如果 Vertex AI 失败，尝试使用 OpenRouter
  try {
    console.log('[vertexAI] Vertex AI failed, attempting OpenRouter fallback...');

    // 检查 OpenRouter 健康状态
    const isOpenRouterHealthy = await checkOpenRouterHealth();
    if (!isOpenRouterHealthy) {
      throw new Error('OpenRouter service is not available');
    }

    const fallbackStartTime = Date.now();
    content = await generateContentWithOpenRouter(prompt, {
      maxOutputTokens: MAX_OUTPUT_TOKENS,
      temperature: TEMPERATURE,
      topP: TOP_P
    });

    if (content && content.trim().length > 0) {
      const responseTime = Date.now() - fallbackStartTime;

      console.log(`[vertexAI] ✅ Successfully generated content with OpenRouter fallback (${responseTime}ms)`);

      // 发送降级成功通知
      await notifyFeishu.aiGenError({
        provider: 'openrouter',
        model: 'google/gemini-2.0-flash-exp:free',
        error: `Vertex AI failed: ${error}`,
        prompt,
        endpoint: 'utils/vertexAI',
        fallbackUsed: true
      });

      return content;
    } else {
      throw new Error('OpenRouter returned empty content');
    }

  } catch (openrouterError) {
    const fallbackError = openrouterError instanceof Error ? openrouterError.message : String(openrouterError);
    console.error('[vertexAI] ❌ OpenRouter fallback also failed:', fallbackError);

    // 发送最终失败通知
    await notifyFeishu.aiGenError({
      provider: 'openrouter',
      model: 'google/gemini-2.0-flash-exp:free',
      error: `Both Vertex AI and OpenRouter failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`,
      prompt,
      endpoint: 'utils/vertexAI',
      fallbackUsed: true
    });

    // 抛出最终错误
    throw new Error(`Both AI providers failed. Vertex AI: ${error}, OpenRouter: ${fallbackError}`);
  }
}

/**
 * 构建成人ADHD报告提示语
 */
function buildAdultPrompt(answers: Record<number, number>, scores: any, locale: string): string {
  const language = locale === 'zh' ? '简体中文' : 'English';
  const questionLabel = locale === 'zh' ? '问题' : 'Question';
  
  // 构建问题答案部分
  let answerText = '';
  Object.entries(answers).forEach(([questionId, value]) => {
    answerText += `${questionLabel}${questionId}: ${value}\n`;
  });
  
  // 基础提示语
  return `你是一位专业的精神健康从业者。根据用户的ADHD测试结果，你需要生成一份专业、全面的ADHD评估报告。

测试类型: 成人ADHD自评量表
测试得分: ${scores.total}/${scores.maxScore} (${scores.percentage}%)
测试结果解释: ${scores.interpretation}

用户的回答:
${answerText}

请生成一份详细的ADHD评估报告，包括以下部分:
1. 首先以"# 结果摘要"作为主标题
2. 总结患者的ADHD症状表现和严重程度
3. 分析可能的症状影响领域(如工作、人际关系等)
4. 提供专业的建议和下一步可能的措施

重要事项:
- 报告语言: ${language}
- 使用专业但易于理解的语言
- 格式必须使用Markdown格式
- 报告应该篇幅适中但内容充实
- 确保每个主要部分都有明确的标题(使用markdown标题语法)
- 不要在回复中加入"好的"、"我理解"等与报告无关的内容
- 直接从标题"# 结果摘要"开始写作，不要有其他前缀文字

生成的内容将直接呈现给用户，所以请确保格式规范、内容专业。`;
}

/**
 * 构建儿童ADHD报告提示语
 */
function buildChildPrompt(answers: Record<number, number>, scores: any, locale: string): string {
  const language = locale === 'zh' ? '简体中文' : 'English';
  const questionLabel = locale === 'zh' ? '问题' : 'Question';
  
  // 获取详细分数信息
  const inattention = scores.inattention;
  const hyperactivity = scores.hyperactivity;
  const opposition = scores.opposition;
  const respondent = scores.respondent;
  const subtype = scores.subtype;
  
  // 构建问题答案部分
  let answerText = '';
  Object.entries(answers).forEach(([questionId, value]) => {
    answerText += `${questionLabel}${questionId}: ${value}\n`;
  });
  
  // 基础提示语
  return `你是一位专业的儿童精神健康从业者。根据填写者(${respondent})对孩子的ADHD评估测试结果，你需要生成一份专业、全面的儿童ADHD评估报告。

测试类型: 儿童ADHD评估量表
填写人: ${respondent}
可能的亚型: ${subtype}

分项得分:
- 注意力不集中: ${inattention.score}/${inattention.max} (${inattention.significant ? '显著' : '不显著'})
- 多动冲动: ${hyperactivity.score}/${hyperactivity.max} (${hyperactivity.significant ? '显著' : '不显著'})
- 对立违抗: ${opposition.score}/${opposition.max} (${opposition.significant ? '显著' : '不显著'})

用户的回答:
${answerText}

请生成一份详细的儿童ADHD评估报告，包括以下部分:
1. 首先以"# 结果摘要"作为主标题
2. 总结孩子的ADHD症状表现和严重程度，特别关注显著的分项
3. 分析症状可能对孩子生活的不同方面(如学习、社交等)的影响
4. 提供给家长/照顾者的建议和可能的下一步措施

重要事项:
- 报告语言: ${language}
- 使用专业但对非专业人士友好的语言
- 格式必须使用Markdown格式
- 确保每个主要部分都有明确的标题(使用markdown标题语法)
- 对可能的干预措施给出具体、实用的建议
- 保持同理心和支持性的语言风格
- 不要在回复中加入"好的"、"我理解"等与报告无关的内容
- 直接从标题"# 结果摘要"开始写作，不要有其他前缀文字

生成的内容将直接呈现给用户，所以请确保格式规范、内容专业。`;
}

/**
 * 生成ADHD报告 (非流式)
 */
export const generateAdhdReport = async (
  testType: 'adult' | 'child',
  answers: Record<number, number>,
  scores: any,
  locale: string = 'en'
) => {
  try {
    console.log(`Starting ${testType} ADHD report generation in ${locale}`);

    // 根据测试类型构建不同的提示语
    const prompt = testType === 'adult'
      ? buildAdultPrompt(answers, scores, locale)
      : buildChildPrompt(answers, scores, locale);

    // 使用降级方案生成报告
    const content = await generateAIContentWithFallback(prompt, locale);

    // 确保内容以标题开始
    let finalContent = content;
    if (!finalContent.startsWith('#')) {
      const defaultTitle = locale === 'zh' ? '# 结果摘要' : '# Summary of Results';
      finalContent = `${defaultTitle}\n\n${finalContent}`;
    }

    console.log(`Generated report with ${finalContent.length} characters`);
    return finalContent;
  } catch (error) {
    console.error('Error generating ADHD report:', error);
    return locale === 'zh'
      ? '抱歉，生成报告时遇到错误。请稍后再试。'
      : 'Sorry, we encountered an error generating your report. Please try again later.';
  }
};

/**
 * 流式生成ADHD报告
 */
export async function* streamAdhdReport(
  testType: 'adult' | 'child',
  answers: Record<number, number>,
  scores: any,
  locale: string = 'en'
) {
  const streamId = `stream_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  console.log(`[${streamId}] Starting Vertex AI stream for ${testType} test in ${locale}`);
  let chunkCount = 0;
  let totalChars = 0;
  let bufferedContent = '';
  let lastYieldTime = Date.now();
  let completeContent = ''; // 完整记录所有内容
  let logChunks = true; // 控制是否记录每个小块的内容

  try {
    // 根据测试类型构建不同的提示语
    const prompt = testType === 'adult'
      ? buildAdultPrompt(answers, scores, locale)
      : buildChildPrompt(answers, scores, locale);

    console.log(`[${streamId}] Prepared prompt with ${prompt.length} chars, prompt: "${prompt.substring(0, 100)}..."`);

    // 首先尝试使用 Vertex AI 流式API
    let streamingResult;
    try {
      const generativeModel = getGenerativeModel();
      streamingResult = await generativeModel.generateContentStream(prompt);
    } catch (vertexError) {
      console.error(`[${streamId}] Vertex AI streaming failed, falling back to non-streaming generation:`, vertexError);

      // 发送错误通知
      await notifyFeishu.aiGenError({
        provider: 'vertex-ai',
        model: MODEL_NAME,
        error: vertexError instanceof Error ? vertexError.message : String(vertexError),
        prompt,
        endpoint: 'utils/vertexAI (streaming)',
        fallbackUsed: true
      });

      // 回退到非流式生成
      try {
        const content = await generateAIContentWithFallback(prompt, locale);

        // 模拟流式输出
        const words = content.split('');
        const chunkSize = Math.max(1, Math.floor(words.length / 50));

        for (let i = 0; i < words.length; i += chunkSize) {
          const chunk = words.slice(i, i + chunkSize).join('');
          yield chunk;

          // 添加小延迟模拟流式效果
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.log(`[${streamId}] Completed fallback streaming with ${content.length} characters`);
        return;
      } catch (fallbackError) {
        console.error(`[${streamId}] Fallback generation also failed:`, fallbackError);
        yield locale === 'zh'
          ? '抱歉，生成报告时遇到错误。请稍后再试。'
          : 'Sorry, we encountered an error generating your report. Please try again later.';
        return;
      }
    }
    
    // 处理流式响应
    try {
      console.log(`[${streamId}] Starting stream processing`);
      let emptyChunksCount = 0;
      let allTextGenerated = '';
      
      for await (const chunk of streamingResult.stream) {
        chunkCount++;
        
        // 提取文本
        let chunkText = '';
        if (chunk.candidates && chunk.candidates.length > 0) {
          const textParts = chunk.candidates[0].content.parts;
          chunkText = textParts.map(part => part.text).join('');
        }
        
        // 追踪空块
        if (!chunkText || chunkText.trim().length === 0) {
          emptyChunksCount++;
          console.log(`[${streamId}] Received empty chunk #${chunkCount}`);
          if (emptyChunksCount > 5) {
            console.warn(`[${streamId}] Received ${emptyChunksCount} empty chunks in a row`);
          }
          continue;
        }
        
        // 记录接收情况
        totalChars += chunkText.length;
        allTextGenerated += chunkText;
        completeContent += chunkText;
        bufferedContent += chunkText;
        
        // 记录每个chunk，对于较大的chunk详细记录
        const now = Date.now();
        const sinceLastYield = now - lastYieldTime;
        
        // 对每个chunk进行详细记录 
        console.log(`[${streamId}] Chunk #${chunkCount}: ${chunkText.length} chars, total: ${totalChars}, time since last: ${sinceLastYield}ms, content: "${chunkText.substring(0, Math.min(200, chunkText.length))}${chunkText.length > 200 ? '...' : ''}"`);
        
        // 确保及时yield内容，不让buffer太大
        if (bufferedContent.length > 0) {
          lastYieldTime = now;
          
          // 详细记录返回内容
          console.log(`[${streamId}] Yielding content (${bufferedContent.length} chars): "${bufferedContent.substring(0, Math.min(200, bufferedContent.length))}${bufferedContent.length > 200 ? '...' : ''}"`);
          
          yield bufferedContent;
          bufferedContent = '';
        }
      }
      
      // 确保最后的内容也被返回
      if (bufferedContent.length > 0) {
        console.log(`[${streamId}] Yielding final buffer (${bufferedContent.length} chars): "${bufferedContent.substring(0, Math.min(200, bufferedContent.length))}${bufferedContent.length > 200 ? '...' : ''}"`);
        yield bufferedContent;
        bufferedContent = '';
      }
      
      // 完成时记录全部内容以便于调试
      console.log(`[${streamId}] Stream complete: ${chunkCount} chunks received, total ${totalChars} chars`);
      
      // 记录内容预览
      console.log(`[${streamId}] Content preview - 
Begin: "${completeContent.substring(0, 100)}..." 
Middle: "...${completeContent.substring(Math.max(0, completeContent.length / 2 - 50), Math.min(completeContent.length, completeContent.length / 2 + 50))}..."
End: "...${completeContent.substring(Math.max(0, completeContent.length - 100))}"
`);
      
      // 记录完整内容用于调试
      console.log(`[${streamId}] [FULL CONTENT START] 
${completeContent}
[${streamId}] [FULL CONTENT END]`);
      
      // 如果内容太少，可能存在问题
      if (totalChars < 100) {
        console.warn(`[${streamId}] WARNING: Very small response (${totalChars} chars), likely an issue`);
        // 如果内容太小，添加错误信息
        if (locale === 'zh') {
          yield "\n\n**生成内容时出现问题，内容不完整。请稍后重试。**";
        } else {
          yield "\n\n**There was a problem generating the content. The result may be incomplete. Please try again later.**";
        }
      }
      
    } catch (streamError) {
      console.error(`[${streamId}] Error processing stream:`, streamError);
      console.log(`[${streamId}] Content received before error (${completeContent.length} chars):\n${completeContent}`);
      yield locale === 'zh' 
        ? "\n\n处理内容流时发生错误，请重试。" 
        : "\n\nError occurred while processing the content stream. Please try again.";
    }
  } catch (error) {
    console.error(`[${streamId}] Error starting stream:`, error);
    yield locale === 'zh' 
      ? '抱歉，生成报告时遇到错误。请稍后再试。' 
      : 'Sorry, we encountered an error generating your report. Please try again later.';
  }
} 