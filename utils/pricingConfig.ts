export interface PricingPlan {
    id: string;
    name: string;
    description: string;
    price: number;
    stripePriceId: string;
    features: string[];
    type: 'single' | 'subscription';
    billingPeriod?: 'monthly' | 'yearly';
    limits?: {
        reportsPerMonth?: number;
        consultationsPerMonth?: number;
        hasHistoryAccess?: boolean;
        hasVipFeatures?: boolean;
        isUnlimited?: boolean;
        dailyConsultationLimit?: number;
    };
    // 多语言支持
    nameZh?: string;
    descriptionZh?: string;
    featuresZh?: string[];
}

export interface PricingConfig {
    singleReport: PricingPlan;
    proMonthly: PricingPlan;
    proYearly: PricingPlan;
    premiumMonthly: PricingPlan;
    premiumYearly: PricingPlan;
}

export function getPricingConfig(locale: string = 'en'): PricingConfig {
    return {
        singleReport: {
            id: 'single-report',
            name: 'Single Report Unlock',
            nameZh: '单份报告解锁',
            description: 'One-time payment to unlock a specific report',
            descriptionZh: '一次性付费解锁特定报告',
            price: 15.99,
            stripePriceId: process.env.STRIPE_PRICE_SINGLE_REPORT || '',
            type: 'single',
            features: [
                'Unlock one specific report',
                'Permanent access to unlocked report',
                'No subscription required'
            ],
            featuresZh: [
                '解锁一份特定报告',
                '永久访问已解锁的报告',
                '无需订阅'
            ]
        },
        proMonthly: {
            id: 'pro-monthly',
            name: 'Pro Subscription',
            nameZh: 'Pro订阅',
            description: 'Monthly subscription with 3 reports and 15 consultations',
            descriptionZh: '订阅制，每月 3 份报告 + 15 次咨询',
            price: 9.99,
            stripePriceId: process.env.STRIPE_PRICE_PRO_MONTHLY || '',
            type: 'subscription',
            billingPeriod: 'monthly',
            features: [
                'Up to 3 reports per month',
                '15 AI consultations per month',
                'Priority support',
                'Advanced analytics'
            ],
            featuresZh: [
                '每月最多 3 份报告',
                '每月 15 次 AI 咨询',
                '优先支持',
                '高级分析'
            ],
            limits: {
                reportsPerMonth: 3,
                consultationsPerMonth: 15,
                hasHistoryAccess: false,
                hasVipFeatures: false,
                isUnlimited: false
            }
        },
        proYearly: {
            id: 'pro-yearly',
            name: 'Pro Yearly',
            nameZh: 'Pro年付',
            description: 'Yearly subscription with 2 months free',
            descriptionZh: '年付订阅，免费2个月',
            price: 99.99,
            stripePriceId: process.env.STRIPE_PRICE_PRO_YEARLY || '',
            type: 'subscription',
            billingPeriod: 'yearly',
            features: [
                'Up to 3 reports per month',
                '15 AI consultations per month',
                'Priority support',
                'Advanced analytics',
                '2 months free'
            ],
            featuresZh: [
                '每月最多 3 份报告',
                '每月 15 次 AI 咨询',
                '优先支持',
                '高级分析',
                '免费2个月'
            ],
            limits: {
                reportsPerMonth: 3,
                consultationsPerMonth: 15,
                hasHistoryAccess: false,
                hasVipFeatures: false,
                isUnlimited: false
            }
        },
        premiumMonthly: {
            id: 'premium-monthly',
            name: 'Premium Subscription',
            nameZh: 'Premium订阅',
            description: 'Unlimited reports + consultations (daily limit) + history + VIP features',
            descriptionZh: '无限报告 + 无限咨询（每日限量）+ 历史记录 + VIP功能',
            price: 39.99,
            stripePriceId: process.env.STRIPE_PRICE_PREMIUM_MONTHLY || '',
            type: 'subscription',
            billingPeriod: 'monthly',
            features: [
                'Unlimited test reports',
                'Unlimited AI consultations (daily limit)',
                'Full history access',
                'VIP features',
                'Expert consultation',
                'Personalized recommendations'
            ],
            featuresZh: [
                '无限测试报告',
                '无限AI咨询（每日限量）',
                '完整历史记录',
                'VIP功能',
                '专家咨询',
                '个性化推荐'
            ],
            limits: {
                reportsPerMonth: -1,
                consultationsPerMonth: -1,
                hasHistoryAccess: true,
                hasVipFeatures: true,
                isUnlimited: true,
                dailyConsultationLimit: 50
            }
        },
        premiumYearly: {
            id: 'premium-yearly',
            name: 'Premium Yearly',
            nameZh: 'Premium年付',
            description: 'Premium yearly with maximum savings',
            descriptionZh: 'Premium年付，最大节省',
            price: 399.99,
            stripePriceId: process.env.STRIPE_PRICE_PREMIUM_YEARLY || '',
            type: 'subscription',
            billingPeriod: 'yearly',
            features: [
                'Unlimited test reports',
                'Unlimited AI consultations (daily limit)',
                'Full history access',
                'VIP features',
                'Expert consultation',
                'Personalized recommendations',
                '2 months free'
            ],
            featuresZh: [
                '无限测试报告',
                '无限AI咨询（每日限量）',
                '完整历史记录',
                'VIP功能',
                '专家咨询',
                '个性化推荐',
                '免费2个月'
            ],
            limits: {
                reportsPerMonth: -1,
                consultationsPerMonth: -1,
                hasHistoryAccess: true,
                hasVipFeatures: true,
                isUnlimited: true,
                dailyConsultationLimit: 50
            }
        }
    };
}

export function getLocalizedPlan(plan: PricingPlan, locale: string): PricingPlan {
    if (locale === 'zh' || locale === 'zh-CN') {
        return {
            ...plan,
            name: plan.nameZh || plan.name,
            description: plan.descriptionZh || plan.description,
            features: plan.featuresZh || plan.features
        };
    }
    return plan;
}

export function getDefaultSingleReportPrice(): { price: number; stripePriceId: string } {
    const config = getPricingConfig();
    return {
        price: config.singleReport.price,
        stripePriceId: config.singleReport.stripePriceId
    };
}

export function checkUserLimits(user: any, action: 'report' | 'consultation'): {
    allowed: boolean;
    remaining?: number;
    resetDate?: Date;
    needsUpgrade?: boolean;
} {
    if (process.env.NODE_ENV === 'development') {
        console.log(`\n--- checkUserLimits ---`);
        console.log(`Action: ${action}`);
        console.log(`User subscription type: ${user.subscriptionType}`);
        console.log(`User subscription status: ${user.subscriptionStatus}`);
    }

    if (!user.subscriptionType || user.subscriptionStatus !== 'active') {
        if (process.env.NODE_ENV === 'development') {
            console.log('❌ No valid subscription - upgrade required');
        }
        return { allowed: false, needsUpgrade: true };
    }

    const config = getPricingConfig();
    let userPlan: PricingPlan;

    switch (user.subscriptionType) {
        case 'pro':
            userPlan = config.proMonthly;
            break;
        case 'premium':
            userPlan = config.premiumMonthly;
            break;
        default:
            if (process.env.NODE_ENV === 'development') {
                console.log(`❌ Unknown subscription type: ${user.subscriptionType}`);
            }
            return { allowed: false, needsUpgrade: true };
    }

    if (process.env.NODE_ENV === 'development') {
        console.log(`📋 User plan: ${userPlan.name} (${userPlan.id})`);
        console.log(`📊 Plan limits:`, userPlan.limits);
    }

    const limits = userPlan.limits;
    if (!limits) {
        if (process.env.NODE_ENV === 'development') {
            console.log('❌ No limits defined for plan');
        }
        return { allowed: false, needsUpgrade: true };
    }

    if (limits.isUnlimited) {
        if (process.env.NODE_ENV === 'development') {
            console.log('✅ Unlimited plan - action allowed');
        }
        if (action === 'consultation' && limits.dailyConsultationLimit) {
            return { allowed: true };
        }
        return { allowed: true };
    }

    const now = new Date();
    const lastReset = user.lastResetDate ? new Date(user.lastResetDate) : new Date(user.createdAt);
    const shouldReset = now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear();

    if (process.env.NODE_ENV === 'development') {
        console.log(`📅 Current date: ${now.toISOString()}`);
        console.log(`📅 Last reset date: ${lastReset.toISOString()}`);
        console.log(`🔄 Should reset usage: ${shouldReset}`);
    }

    if (action === 'report') {
        const limit = limits.reportsPerMonth || 0;
        const used = shouldReset ? 0 : (user.monthlyReportsUsed || 0);
        const result = {
            allowed: used < limit,
            remaining: Math.max(0, limit - used),
            resetDate: new Date(now.getFullYear(), now.getMonth() + 1, 1),
            needsUpgrade: used >= limit
        };

        if (process.env.NODE_ENV === 'development') {
            console.log(`📊 Reports - Limit: ${limit}, Used: ${used}, Remaining: ${result.remaining}`);
            console.log(`✅/❌ Reports allowed: ${result.allowed}`);
        }

        return result;
    } else if (action === 'consultation') {
        const limit = limits.consultationsPerMonth || 0;
        const used = shouldReset ? 0 : (user.monthlyConsultationsUsed || 0);
        const result = {
            allowed: used < limit,
            remaining: Math.max(0, limit - used),
            resetDate: new Date(now.getFullYear(), now.getMonth() + 1, 1),
            needsUpgrade: used >= limit
        };

        if (process.env.NODE_ENV === 'development') {
            console.log(`📊 Consultations - Limit: ${limit}, Used: ${used}, Remaining: ${result.remaining}`);
            console.log(`✅/❌ Consultation allowed: ${result.allowed}`);
        }

        return result;
    }

    if (process.env.NODE_ENV === 'development') {
        console.log(`❌ Unknown action: ${action}`);
    }
    return { allowed: false, needsUpgrade: true };
}
