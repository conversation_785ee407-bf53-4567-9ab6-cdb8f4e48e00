import { PrismaClient } from '@prisma/client'

const globalForPrisma = global as unknown as { prisma: PrismaClient }

// 根据环境变量控制日志级别
const getLogLevel = (): Array<'query' | 'info' | 'warn' | 'error'> => {
  // 可以通过环境变量控制日志级别
  const logLevel = process.env.PRISMA_LOG_LEVEL || 'error'

  switch (logLevel) {
    case 'query':
      return ['query', 'error', 'warn']
    case 'info':
      return ['info', 'error', 'warn']
    case 'warn':
      return ['error', 'warn']
    case 'error':
      return ['error']
    case 'none':
      return []
    default:
      // 开发环境默认只显示错误，生产环境不显示日志
      return process.env.NODE_ENV === 'development' ? ['error'] : []
  }
}

export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: getLogLevel(),
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma