import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

/**
 * 构建多语言URL路径
 * @param path - 路径，例如 '/autism' 或 '/auth/signin'
 * @param locale - 语言代码，例如 'en' 或 'zh'
 * @returns 完整的多语言路径
 */
export function getLocalizedPath(path: string, locale: string = 'en'): string {
    // 确保路径以 / 开头
    const normalizedPath = path.startsWith('/') ? path : `/${path}`;
    // 对于英语（默认语言），不添加语言前缀
    return locale === 'en' ? normalizedPath : `/${locale}${normalizedPath}`;
}

/**
 * 从当前路径中提取语言代码
 * @param pathname - 当前路径
 * @returns 语言代码
 */
export function getLocaleFromPath(pathname: string): string {
    const segments = pathname.split('/').filter(Boolean);
    const firstSegment = segments[0];

    // 检查第一个段是否是支持的语言代码
    if (firstSegment && ['zh'].includes(firstSegment)) {
        return firstSegment;
    }

    return 'en'; // 默认返回英语
} 