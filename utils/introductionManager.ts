// 引导提示管理工具
export interface IntroductionState {
    viewCount: number;
    lastViewTime: number;
    disabledUntil?: number; // 禁用到期时间戳
}

// 获取引导提示状态
export function getIntroductionState(key: string): IntroductionState {
    try {
        const stored = localStorage.getItem(key);
        if (!stored) {
            return { viewCount: 0, lastViewTime: 0 };
        }
        return JSON.parse(stored);
    } catch {
        return { viewCount: 0, lastViewTime: 0 };
    }
}

// 保存引导提示状态
export function saveIntroductionState(key: string, state: IntroductionState): void {
    try {
        localStorage.setItem(key, JSON.stringify(state));
    } catch (error) {
        console.warn('Failed to save introduction state:', error);
    }
}

// 检查是否应该显示引导提示
export function shouldShowIntroduction(key: string): boolean {
    const state = getIntroductionState(key);
    const now = Date.now();

    // 如果设置了禁用期限且还在禁用期内，不显示
    if (state.disabledUntil && now < state.disabledUntil) {
        return false;
    }

    // 如果已过禁用期，重置状态
    if (state.disabledUntil && now >= state.disabledUntil) {
        const newState: IntroductionState = {
            viewCount: 0,
            lastViewTime: 0
        };
        saveIntroductionState(key, newState);
        return true;
    }

    // 正常情况下总是显示
    return true;
}

// 记录一次查看
export function recordIntroductionView(key: string): IntroductionState {
    const state = getIntroductionState(key);
    const newState: IntroductionState = {
        viewCount: state.viewCount + 1,
        lastViewTime: Date.now(),
        disabledUntil: state.disabledUntil
    };
    saveIntroductionState(key, newState);
    return newState;
}

// 禁用引导提示7天
export function disableIntroductionFor7Days(key: string): void {
    const state = getIntroductionState(key);
    const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数
    const newState: IntroductionState = {
        ...state,
        disabledUntil: Date.now() + sevenDaysInMs
    };
    saveIntroductionState(key, newState);
}

// 检查是否应该显示"不再提示"按钮
export function shouldShowDisableOption(viewCount: number): boolean {
    return viewCount >= 3;
}

// 格式化剩余禁用时间（用于调试或显示）
export function getDisableTimeRemaining(key: string): string | null {
    const state = getIntroductionState(key);
    if (!state.disabledUntil) return null;

    const now = Date.now();
    if (now >= state.disabledUntil) return null;

    const remaining = state.disabledUntil - now;
    const days = Math.ceil(remaining / (24 * 60 * 60 * 1000));
    return `${days} days`;
} 