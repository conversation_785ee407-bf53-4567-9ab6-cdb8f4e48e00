import { prisma } from './prisma';

export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    type?: 'normal' | 'subscription_limit';
    upgradeRequired?: boolean;
}

export interface ChatUsageInfo {
    hasUsedFreeConsultation: boolean;
    canUseConsultation: boolean;
    reason?: string;
    upgradeRequired?: boolean;
    remainingConsultations?: number;
}

export class ChatService {
    // 获取报告的聊天记录
    static async getChatHistory(testResultId: string): Promise<ChatMessage[]> {
        const conversation = await prisma.chatConversation.findUnique({
            where: { testResultId }
        });

        if (!conversation) {
            return [];
        }

        const messages = conversation.messages as unknown as ChatMessage[];
        return messages.map(msg => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
        }));
    }

    // 添加消息到聊天记录
    static async addMessage(
        testResultId: string,
        message: Omit<ChatMessage, 'id' | 'timestamp'>,
        userId?: string
    ): Promise<ChatMessage> {
        console.log(`\n--- ChatService.addMessage ---`);
        console.log(`testResultId: ${testResultId}`);
        console.log(`message role: ${message.role}`);
        console.log(`message content length: ${message.content.length}`);
        console.log(`userId: ${userId || 'NO_USER_ID'}`);

        const newMessage: ChatMessage = {
            id: Date.now().toString(),
            ...message,
            timestamp: new Date()
        };

        // 获取现有对话或创建新的
        const existingConversation = await prisma.chatConversation.findUnique({
            where: { testResultId }
        });

        console.log(`Existing conversation found: ${!!existingConversation}`);

        if (existingConversation) {
            console.log(`Current conversation state:`, {
                messageCount: existingConversation.messageCount,
                userMessageCount: existingConversation.userMessageCount,
                messagesArrayLength: Array.isArray(existingConversation.messages) ? existingConversation.messages.length : 'NOT_ARRAY'
            });

            const currentMessages = existingConversation.messages as unknown as ChatMessage[];
            const updatedMessages = [...currentMessages, newMessage];

            // 计算用户消息数量
            const userMessageCount = updatedMessages.filter(msg => msg.role === 'user').length;

            console.log(`After adding message:`, {
                totalMessages: updatedMessages.length,
                newUserMessageCount: userMessageCount,
                previousUserMessageCount: existingConversation.userMessageCount,
                messageRole: message.role,
                isUserMessage: message.role === 'user'
            });

            await prisma.chatConversation.update({
                where: { testResultId },
                data: {
                    messages: updatedMessages as any,
                    messageCount: updatedMessages.length,
                    userMessageCount,
                    updatedAt: new Date()
                }
            });

            console.log(`✅ Updated conversation with userMessageCount: ${userMessageCount}`);
        } else {
            // 创建新的对话记录
            const userMessageCount = message.role === 'user' ? 1 : 0;

            console.log(`Creating new conversation:`, {
                messageCount: 1,
                userMessageCount,
                messageRole: message.role,
                isUserMessage: message.role === 'user'
            });

            await prisma.chatConversation.create({
                data: {
                    testResultId,
                    userId,
                    messages: [newMessage] as any,
                    messageCount: 1,
                    userMessageCount
                }
            });

            console.log(`✅ Created new conversation with userMessageCount: ${userMessageCount}`);
        }

        console.log(`--- ChatService.addMessage END ---\n`);
        return newMessage;
    }

    // 检查用户是否可以使用AI咨询
    static async checkConsultationAccess(testResultId: string, userId?: string): Promise<ChatUsageInfo> {
        console.log(`\n--- ChatService.checkConsultationAccess ---`);
        console.log(`testResultId: ${testResultId}`);
        console.log(`userId: ${userId || 'NO_USER_ID'}`);

        // 获取聊天记录
        const conversation = await prisma.chatConversation.findUnique({
            where: { testResultId }
        });

        console.log(`Conversation found: ${!!conversation}`);
        if (conversation) {
            console.log(`Conversation details:`, {
                id: conversation.id,
                messageCount: conversation.messageCount,
                userMessageCount: conversation.userMessageCount,
                createdAt: conversation.createdAt,
                updatedAt: conversation.updatedAt
            });
        }

        const hasUsedFreeConsultation = conversation ? conversation.userMessageCount >= 1 : false;
        console.log(`hasUsedFreeConsultation (userMessageCount >= 1): ${hasUsedFreeConsultation}`);
        console.log(`Current userMessageCount: ${conversation?.userMessageCount || 0}`);

        // 如果没有用户ID（未登录用户）
        if (!userId) {
            console.log(`🔍 Path: NO_USER_ID (anonymous user)`);
            if (hasUsedFreeConsultation) {
                console.log(`❌ Decision: DENY - Free consultation already used`);
                return {
                    hasUsedFreeConsultation: true,
                    canUseConsultation: false,
                    reason: 'Free consultation already used for this report',
                    upgradeRequired: true
                };
            }
            console.log(`✅ Decision: ALLOW - Free consultation available`);
            return {
                hasUsedFreeConsultation: false,
                canUseConsultation: true
            };
        }

        // 获取用户信息
        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        console.log(`User found: ${!!user}`);
        if (user) {
            console.log(`User details:`, {
                id: user.id,
                email: user.email,
                subscriptionType: user.subscriptionType,
                subscriptionStatus: user.subscriptionStatus,
                monthlyConsultationsUsed: user.monthlyConsultationsUsed,
                lastResetDate: user.lastResetDate
            });
        }

        if (!user) {
            console.log(`❌ Decision: DENY - User not found`);
            return {
                hasUsedFreeConsultation,
                canUseConsultation: false,
                reason: 'User not found'
            };
        }

        // 如果用户有有效订阅，使用订阅服务检查
        if (user.subscriptionType && user.subscriptionStatus === 'active') {
            console.log(`🔍 Path: SUBSCRIPTION_USER (${user.subscriptionType})`);
            const SubscriptionService = (await import('./subscriptionService')).default;
            const canConsult = await SubscriptionService.canPerformAction(userId, 'consultation');

            console.log(`Subscription service result:`, canConsult);
            console.log(`${canConsult.allowed ? '✅' : '❌'} Decision: ${canConsult.allowed ? 'ALLOW' : 'DENY'} - Subscription check`);

            return {
                hasUsedFreeConsultation,
                canUseConsultation: canConsult.allowed,
                reason: canConsult.reason,
                upgradeRequired: canConsult.upgradeRequired
            };
        }

        // 无订阅用户：每个报告可以免费使用一次
        console.log(`🔍 Path: FREE_USER (no active subscription)`);
        if (hasUsedFreeConsultation) {
            console.log(`❌ Decision: DENY - Free consultation already used`);
            return {
                hasUsedFreeConsultation: true,
                canUseConsultation: false,
                reason: 'Free consultation already used for this report',
                upgradeRequired: true
            };
        }

        console.log(`✅ Decision: ALLOW - Free consultation available`);
        return {
            hasUsedFreeConsultation: false,
            canUseConsultation: true
        };
    }

    // 跟踪咨询使用（仅对订阅用户）
    static async trackConsultationUsage(testResultId: string, userId?: string): Promise<void> {
        if (!userId) {
            // 非登录用户不需要额外跟踪，已在addMessage中处理
            return;
        }

        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        // 只有订阅用户需要跟踪全局使用量
        if (user?.subscriptionType && user.subscriptionStatus === 'active') {
            const SubscriptionService = (await import('./subscriptionService')).default;
            await SubscriptionService.trackUsage(userId, 'consultation');
        }
    }

    // 清除聊天记录（管理功能）
    static async clearChatHistory(testResultId: string): Promise<void> {
        await prisma.chatConversation.delete({
            where: { testResultId }
        });
    }

    // 获取聊天统计信息
    static async getChatStats(testResultId: string): Promise<{
        totalMessages: number;
        userMessages: number;
        assistantMessages: number;
        firstMessageAt?: Date;
        lastMessageAt?: Date;
    }> {
        const conversation = await prisma.chatConversation.findUnique({
            where: { testResultId }
        });

        if (!conversation) {
            return {
                totalMessages: 0,
                userMessages: 0,
                assistantMessages: 0
            };
        }

        const messages = conversation.messages as unknown as ChatMessage[];
        const userMessages = messages.filter(msg => msg.role === 'user').length;
        const assistantMessages = messages.filter(msg => msg.role === 'assistant').length;

        return {
            totalMessages: messages.length,
            userMessages,
            assistantMessages,
            firstMessageAt: messages.length > 0 ? new Date(messages[0].timestamp) : undefined,
            lastMessageAt: messages.length > 0 ? new Date(messages[messages.length - 1].timestamp) : undefined
        };
    }
}

export default ChatService; 