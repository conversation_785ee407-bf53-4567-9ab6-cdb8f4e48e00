import { NextAuthOptions } from "next-auth";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { prisma } from "@/utils/prisma";
import GoogleProvider from "next-auth/providers/google";
import EmailProvider from "next-auth/providers/email";
import { Session } from "next-auth";
import { Resend } from "resend";

// 初始化 Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// 扩展 Session 用户类型
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    }
  }
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
    }),
    EmailProvider({
      server: {
        host: "send.resend.com",
        port: 587,
        auth: {
          user: "resend",
          pass: process.env.RESEND_API_KEY,
        },
      },
      from: process.env.EMAIL_FROM || "<EMAIL>",
      // 自定义邮件发送函数
      sendVerificationRequest: async ({
        identifier: email,
        url,
        provider,
        theme,
      }) => {
        try {
          await resend.emails.send({
            from: provider.from!,
            to: email,
            subject: "Sign in to RaadsTest",
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                  <h1 style="color: #2563eb; margin: 0;">RaadsTest</h1>
                  <p style="color: #6b7280; margin: 5px 0 0 0;">Autism & ADHD Testing Platform</p>
                </div>
                
                <div style="background-color: #f9fafb; border-radius: 8px; padding: 30px; margin: 20px 0;">
                  <h2 style="color: #111827; margin: 0 0 20px 0;">Sign in to your account</h2>
                  <p style="color: #374151; margin: 0 0 25px 0; line-height: 1.6;">
                    Click the button below to securely sign in to your RaadsTest account. This link will expire in 24 hours.
                  </p>
                  
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${url}" 
                       style="display: inline-block; background-color: #2563eb; color: white; text-decoration: none; 
                              padding: 12px 30px; border-radius: 6px; font-weight: 600; font-size: 16px;">
                      Sign in to RaadsTest
                    </a>
                  </div>
                  
                  <p style="color: #6b7280; font-size: 14px; margin: 25px 0 0 0; line-height: 1.5;">
                    If you didn't request this email, you can safely ignore it. The link will expire automatically.
                  </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                  <p style="color: #9ca3af; font-size: 12px; margin: 0;">
                    This email was sent by RaadsTest. For support, please contact <NAME_EMAIL>
                  </p>
                </div>
              </div>
            `,
            text: `
Sign in to RaadsTest

Click the link below to sign in to your account:
${url}

This link will expire in 24 hours.

If you didn't request this email, you can safely ignore it.

---
RaadsTest - Autism & ADHD Testing Platform
            `,
          });
        } catch (error) {
          console.error("Failed to send email:", error);
          throw new Error("Failed to send verification email");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  callbacks: {
    async session({ session, user }: { session: Session; user: any }) {
      if (session.user) {
        session.user.id = user.id;
      }
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
}; 