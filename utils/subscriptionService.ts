import Stripe from 'stripe';
import { getPricingConfig, checkUserLimits } from './pricingConfig';
import { prisma } from './prisma';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export interface SubscriptionInfo {
    type: string | null;
    status: string | null;
    currentPeriodEnd: Date | null;
    reportsRemaining: number;
    consultationsRemaining: number;
    hasHistoryAccess: boolean;
    hasVipFeatures: boolean;
    isUnlimited: boolean;
}

export class SubscriptionService {
    // Get user's current subscription info
    static async getUserSubscriptionInfo(userId: string): Promise<SubscriptionInfo> {
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                subscriptionType: true,
                subscriptionStatus: true,
                currentPeriodEnd: true,
                monthlyReportsUsed: true,
                monthlyConsultationsUsed: true,
                lastResetDate: true,
                hasHistoryAccess: true,
                hasVipFeatures: true,
                createdAt: true
            }
        });

        if (!user) {
            throw new Error('User not found');
        }

        // Reset monthly usage if needed
        await this.resetMonthlyUsageIfNeeded(userId, user);

        const reportLimits = checkUserLimits(user, 'report');
        const consultationLimits = checkUserLimits(user, 'consultation');

        const config = getPricingConfig();
        let userPlan = config.singleReport;

        if (user.subscriptionType === 'pro') {
            userPlan = config.proMonthly;
        } else if (user.subscriptionType === 'premium') {
            userPlan = config.premiumMonthly;
        }

        return {
            type: user.subscriptionType,
            status: user.subscriptionStatus,
            currentPeriodEnd: user.currentPeriodEnd,
            reportsRemaining: reportLimits.remaining || 0,
            consultationsRemaining: consultationLimits.remaining || 0,
            hasHistoryAccess: user.hasHistoryAccess,
            hasVipFeatures: user.hasVipFeatures,
            isUnlimited: userPlan.limits?.isUnlimited || false
        };
    }

    // Check if user can perform an action
    static async canPerformAction(userId: string, action: 'report' | 'consultation'): Promise<{
        allowed: boolean;
        reason?: string;
        upgradeRequired?: boolean;
    }> {
        if (process.env.NODE_ENV === 'development') {
            console.log(`\n--- SubscriptionService.canPerformAction ---`);
            console.log(`User ID: ${userId}, Action: ${action}`);
        }

        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!user) {
            if (process.env.NODE_ENV === 'development') {
                console.log('❌ User not found in database');
            }
            return { allowed: false, reason: 'User not found' };
        }

        if (process.env.NODE_ENV === 'development') {
            console.log('✅ User found, checking limits...');
        }

        const limits = checkUserLimits(user, action);

        if (process.env.NODE_ENV === 'development') {
            console.log('Limits check result:', limits);
        }

        if (!limits.allowed) {
            const result = {
                allowed: false,
                reason: limits.needsUpgrade ? 'Subscription upgrade required' : 'Usage limit exceeded',
                upgradeRequired: limits.needsUpgrade
            };

            if (process.env.NODE_ENV === 'development') {
                console.log('❌ Action not allowed:', result);
            }

            return result;
        }

        if (process.env.NODE_ENV === 'development') {
            console.log('✅ Action allowed');
        }

        return { allowed: true };
    }

    // Track usage for an action
    static async trackUsage(userId: string, action: 'report' | 'consultation'): Promise<void> {
        if (process.env.NODE_ENV === 'development') {
            console.log(`\n--- SubscriptionService.trackUsage ---`);
            console.log(`User ID: ${userId}, Action: ${action}`);
        }

        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!user) {
            if (process.env.NODE_ENV === 'development') {
                console.log('❌ User not found for usage tracking');
            }
            throw new Error('User not found');
        }

        if (process.env.NODE_ENV === 'development') {
            console.log('📊 Current usage before tracking:', {
                monthlyReportsUsed: user.monthlyReportsUsed,
                monthlyConsultationsUsed: user.monthlyConsultationsUsed,
                lastResetDate: user.lastResetDate
            });
        }

        // Reset monthly usage if needed
        await this.resetMonthlyUsageIfNeeded(userId, user);

        // Increment usage counter
        if (action === 'report') {
            await prisma.user.update({
                where: { id: userId },
                data: {
                    monthlyReportsUsed: {
                        increment: 1
                    }
                }
            });

            if (process.env.NODE_ENV === 'development') {
                console.log('✅ Incremented monthlyReportsUsed by 1');
            }
        } else if (action === 'consultation') {
            await prisma.user.update({
                where: { id: userId },
                data: {
                    monthlyConsultationsUsed: {
                        increment: 1
                    }
                }
            });

            if (process.env.NODE_ENV === 'development') {
                console.log('✅ Incremented monthlyConsultationsUsed by 1');
            }
        }

        if (process.env.NODE_ENV === 'development') {
            // 获取更新后的用户数据以显示新的使用量
            const updatedUser = await prisma.user.findUnique({
                where: { id: userId },
                select: {
                    monthlyReportsUsed: true,
                    monthlyConsultationsUsed: true
                }
            });
            console.log('📊 Updated usage after tracking:', updatedUser);
        }
    }

    // Reset monthly usage counters if needed
    private static async resetMonthlyUsageIfNeeded(userId: string, user: any): Promise<void> {
        const now = new Date();
        const lastReset = user.lastResetDate ? new Date(user.lastResetDate) : new Date(user.createdAt);
        const shouldReset = now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear();

        if (process.env.NODE_ENV === 'development') {
            console.log(`\n--- resetMonthlyUsageIfNeeded ---`);
            console.log(`Current date: ${now.toISOString()}`);
            console.log(`Last reset date: ${lastReset.toISOString()}`);
            console.log(`Should reset: ${shouldReset}`);
        }

        if (shouldReset) {
            if (process.env.NODE_ENV === 'development') {
                console.log('🔄 Resetting monthly usage counters...');
                console.log('📊 Before reset:', {
                    monthlyReportsUsed: user.monthlyReportsUsed,
                    monthlyConsultationsUsed: user.monthlyConsultationsUsed
                });
            }

            await prisma.user.update({
                where: { id: userId },
                data: {
                    monthlyReportsUsed: 0,
                    monthlyConsultationsUsed: 0,
                    lastResetDate: now
                }
            });

            if (process.env.NODE_ENV === 'development') {
                console.log('✅ Monthly usage counters reset to 0');
            }
        } else if (process.env.NODE_ENV === 'development') {
            console.log('ℹ️ No reset needed - still in same billing period');
        }
    }

    // Create or update subscription from Stripe webhook
    static async handleStripeSubscription(stripeSubscription: Stripe.Subscription): Promise<void> {
        const customerId = stripeSubscription.customer as string;

        // Find user by Stripe customer ID
        const user = await prisma.user.findFirst({
            where: { stripeCustomerId: customerId }
        });

        if (!user) {
            console.error(`User not found for Stripe customer: ${customerId}`);
            return;
        }

        // Determine subscription type from price ID
        const priceId = stripeSubscription.items.data[0]?.price.id;
        const subscriptionType = this.getSubscriptionTypeFromPriceId(priceId);

        // Update user subscription
        await prisma.user.update({
            where: { id: user.id },
            data: {
                subscriptionType,
                subscriptionStatus: stripeSubscription.status,
                subscriptionId: stripeSubscription.id,
                currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
                currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
                hasHistoryAccess: subscriptionType === 'premium',
                hasVipFeatures: subscriptionType === 'premium'
            }
        });
    }

    // Handle single payment from Stripe webhook
    static async handleSinglePayment(paymentIntent: Stripe.PaymentIntent): Promise<void> {
        const customerId = paymentIntent.customer as string;
        const testId = paymentIntent.metadata.testId;

        if (!customerId) return;

        // Find user by Stripe customer ID
        const user = await prisma.user.findFirst({
            where: { stripeCustomerId: customerId }
        });

        if (!user) {
            console.error(`User not found for Stripe customer: ${customerId}`);
            return;
        }

        // If this is a single report purchase, unlock the specific report
        const priceId = paymentIntent.metadata.stripePriceId;
        if (priceId === process.env.STRIPE_PRICE_SINGLE_REPORT) {
            if (!testId) {
                console.error('Single report payment without testId');
                return;
            }

            // Unlock the specific test report without changing user subscription
            await prisma.testResult.update({
                where: { id: testId },
                data: {
                    isReportLocked: false,
                    unlockedByPayment: true,
                    paymentIntentId: paymentIntent.id
                }
            });

            console.log(`Successfully unlocked report ${testId} via single payment ${paymentIntent.id}`);
        }
    }

    // Helper to determine subscription type from Stripe price ID
    private static getSubscriptionTypeFromPriceId(priceId: string): string {
        if (priceId === process.env.STRIPE_PRICE_SINGLE_REPORT) {
            return 'single';
        } else if (priceId === process.env.STRIPE_PRICE_PRO_MONTHLY ||
            priceId === process.env.STRIPE_PRICE_PRO_YEARLY) {
            return 'pro';
        } else if (priceId === process.env.STRIPE_PRICE_PREMIUM_MONTHLY ||
            priceId === process.env.STRIPE_PRICE_PREMIUM_YEARLY) {
            return 'premium';
        }
        return 'single'; // Default fallback
    }

    // Cancel subscription
    static async cancelSubscription(userId: string): Promise<void> {
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { subscriptionId: true }
        });

        if (!user?.subscriptionId) {
            throw new Error('No active subscription found');
        }

        // Cancel in Stripe
        await stripe.subscriptions.update(user.subscriptionId, {
            cancel_at_period_end: true
        });

        // Update user status
        await prisma.user.update({
            where: { id: userId },
            data: {
                subscriptionStatus: 'canceled'
            }
        });
    }

    // Reactivate subscription
    static async reactivateSubscription(userId: string): Promise<void> {
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { subscriptionId: true }
        });

        if (!user?.subscriptionId) {
            throw new Error('No subscription found');
        }

        // Reactivate in Stripe
        await stripe.subscriptions.update(user.subscriptionId, {
            cancel_at_period_end: false
        });

        // Update user status
        await prisma.user.update({
            where: { id: userId },
            data: {
                subscriptionStatus: 'active'
            }
        });
    }

    // Check if a report should be unlocked based on user subscription or payment
    static async checkReportAccess(testId: string, userId?: string): Promise<{
        isUnlocked: boolean;
        reason: 'subscription' | 'payment' | 'locked';
        subscriptionType?: string;
    }> {
        const testResult = await prisma.testResult.findUnique({
            where: { id: testId },
            include: { user: true }
        });

        if (!testResult) {
            return { isUnlocked: false, reason: 'locked' };
        }

        // CRITICAL: First priority - check if already unlocked by single payment
        // This should ALWAYS take precedence over subscription status
        if (testResult.unlockedByPayment) {
            console.log(`✅ Report ${testId} unlocked by single payment`);
            return { isUnlocked: true, reason: 'payment' };
        }

        // CRITICAL: Check if report is marked as unlocked (isReportLocked = false)
        // This covers cases where reports were unlocked by any means
        if (!testResult.isReportLocked) {
            console.log(`✅ Report ${testId} is marked as unlocked (isReportLocked = false)`);
            return { isUnlocked: true, reason: 'subscription' };
        }

        // Check subscription access for locked reports
        if (userId && testResult.user) {
            const user = testResult.user;

            // Premium users get automatic access to all reports
            if (user.subscriptionType === 'premium' && user.subscriptionStatus === 'active') {
                console.log(`✅ Premium user ${user.email} has access to report ${testId}`);
                return {
                    isUnlocked: true,
                    reason: 'subscription',
                    subscriptionType: 'premium'
                };
            }

            // Pro users need to manually unlock (but we check their limits)
            if (user.subscriptionType === 'pro' && user.subscriptionStatus === 'active') {
                const limits = checkUserLimits(user, 'report');
                if (limits.allowed) {
                    console.log(`✅ Pro user ${user.email} has remaining quota for report ${testId}`);
                    return {
                        isUnlocked: true,
                        reason: 'subscription',
                        subscriptionType: 'pro'
                    };
                } else {
                    console.log(`❌ Pro user ${user.email} has no remaining quota for report ${testId}`);
                }
            }
        }

        console.log(`❌ Report ${testId} is locked and user has no access`);
        return { isUnlocked: false, reason: 'locked' };
    }

    // Unlock report for Pro users (manual action)
    static async unlockReportForProUser(testId: string, userId: string): Promise<boolean> {
        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!user || user.subscriptionType !== 'pro' || user.subscriptionStatus !== 'active') {
            return false;
        }

        const limits = checkUserLimits(user, 'report');
        if (!limits.allowed) {
            return false;
        }

        // Update usage and unlock report
        await prisma.$transaction([
            prisma.user.update({
                where: { id: userId },
                data: {
                    monthlyReportsUsed: user.monthlyReportsUsed + 1,
                    lastResetDate: new Date()
                }
            }),
            prisma.testResult.update({
                where: { id: testId },
                data: { isReportLocked: false }
            })
        ]);

        return true;
    }
}

export default SubscriptionService; 