// 全局缓存管理器 - 使用单例模式确保缓存在所有API路由间共享
class GlobalReportCache {
    private static instance: GlobalReportCache;
    private reportCache: Map<string, { data: any, timestamp: number }>;
    private activeRequests: Map<string, Promise<any>>;
    private readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟缓存过期

    private constructor() {
        this.reportCache = new Map();
        this.activeRequests = new Map();
    }

    public static getInstance(): GlobalReportCache {
        if (!GlobalReportCache.instance) {
            GlobalReportCache.instance = new GlobalReportCache();
        }
        return GlobalReportCache.instance;
    }

    /**
     * 清除指定测试结果的缓存
     */
    public clearReportCache(testId: string, locale?: string): void {
        if (locale) {
            // 清除特定语言的缓存
            const cacheKey = `${testId}-${locale}`;
            this.reportCache.delete(cacheKey);
            console.log(`🗑️ 清除全局缓存: ${cacheKey}`);
        } else {
            // 清除所有语言的缓存
            const keysToDelete: string[] = [];
            for (const [key] of this.reportCache) {
                if (key.startsWith(`${testId}-`)) {
                    keysToDelete.push(key);
                }
            }
            keysToDelete.forEach(key => {
                this.reportCache.delete(key);
                console.log(`🗑️ 清除全局缓存: ${key}`);
            });
        }
    }

    /**
     * 获取缓存
     */
    public getReportCache(): Map<string, { data: any, timestamp: number }> {
        return this.reportCache;
    }

    /**
     * 获取活跃请求
     */
    public getActiveRequests(): Map<string, Promise<any>> {
        return this.activeRequests;
    }

    /**
     * 获取缓存过期时间
     */
    public getCacheExpiry(): number {
        return this.CACHE_EXPIRY;
    }

    /**
     * 强制刷新所有缓存
     */
    public clearAllCache(): void {
        this.reportCache.clear();
        console.log('🗑️ 清除所有全局缓存');
    }
}

// 导出单例实例
const globalCache = GlobalReportCache.getInstance();

/**
 * 清除指定测试结果的缓存（已废弃，保留接口兼容性）
 */
export function clearReportCache(testId: string, locale?: string): void {
    // 服务器端缓存已移除，此函数不再执行任何操作
    console.log(`🗑️ 服务器端缓存已移除，clearReportCache调用被忽略: ${testId}-${locale || 'all'}`);
}

/**
 * 获取缓存（已废弃，保留接口兼容性）
 */
export function getReportCache(): Map<string, { data: any, timestamp: number }> {
    // 返回空的Map，表示没有缓存
    return new Map();
}

/**
 * 获取活跃请求（已废弃，保留接口兼容性）
 */
export function getActiveRequests(): Map<string, Promise<any>> {
    // 返回空的Map，表示没有活跃请求
    return new Map();
}

/**
 * 获取缓存过期时间（已废弃，保留接口兼容性）
 */
export function getCacheExpiry(): number {
    // 返回0，表示缓存立即过期
    return 0;
}

/**
 * 强制刷新所有缓存（已废弃，保留接口兼容性）
 */
export function clearAllCache(): void {
    // 服务器端缓存已移除，此函数不再执行任何操作
    console.log('🗑️ 服务器端缓存已移除，clearAllCache调用被忽略');
} 