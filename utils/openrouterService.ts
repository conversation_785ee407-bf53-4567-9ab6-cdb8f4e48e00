import axios from 'axios';
import { notifyFeishu } from './notifyFeishu';

// OpenRouter API 配置
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

// 默认模型配置
const DEFAULT_MODEL = 'google/gemini-2.0-flash-exp:free';
const FALLBACK_MODELS = [
    'google/gemini-2.0-flash-exp:free',
    'openai/gpt-4o-mini',
    'google/gemini-1.5-pro-latest',
    'anthropic/claude-3.5-sonnet'
];

// 生成配置
interface GenerationConfig {
    maxOutputTokens?: number;
    temperature?: number;
    topP?: number;
    topK?: number;
}

// OpenRouter API 响应接口
interface OpenRouterResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

// 错误重试配置
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1秒

// 延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 调用 OpenRouter API 生成内容
 */
export async function generateContentWithOpenRouter(
    prompt: string,
    config: GenerationConfig = {},
    model: string = DEFAULT_MODEL,
    retryCount: number = 0
): Promise<string> {
    if (!OPENROUTER_API_KEY) {
        throw new Error('OpenRouter API key not configured');
    }

    const startTime = Date.now();
    let currentModel = model;
    
    // 如果重试，尝试使用备用模型
    if (retryCount > 0 && retryCount < FALLBACK_MODELS.length) {
        currentModel = FALLBACK_MODELS[retryCount];
    }

    try {
        const response = await axios.post<OpenRouterResponse>(
            `${OPENROUTER_BASE_URL}/chat/completions`,
            {
                model: currentModel,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: config.maxOutputTokens || 8192,
                temperature: config.temperature || 0.2,
                top_p: config.topP || 0.95,
                // OpenRouter 不支持 topK，忽略该参数
            },
            {
                headers: {
                    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'https://app.raadstest.com',
                    'X-Title': 'RAADS Autism Test'
                },
                timeout: 60000 // 60秒超时
            }
        );

        const responseTime = Date.now() - startTime;
        const content = response.data.choices[0]?.message?.content || '';

        if (!content.trim()) {
            throw new Error('OpenRouter returned empty content');
        }

        // 记录成功的API调用
        await notifyFeishu.paidApiLog({
            provider: 'openrouter',
            model: currentModel,
            prompt,
            response: content,
            success: true,
            responseTime,
            tokenUsage: response.data.usage ? {
                promptTokens: response.data.usage.prompt_tokens,
                completionTokens: response.data.usage.completion_tokens,
                totalTokens: response.data.usage.total_tokens
            } : undefined,
            // OpenRouter 的成本计算需要根据具体模型，这里暂时不计算
        });

        return content;

    } catch (error) {
        const responseTime = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : String(error);

        // 记录失败的API调用
        await notifyFeishu.paidApiLog({
            provider: 'openrouter',
            model: currentModel,
            prompt,
            success: false,
            responseTime,
            error: errorMessage
        });

        // 如果还有重试机会，进行重试
        if (retryCount < MAX_RETRIES) {
            console.log(`OpenRouter API call failed, retrying (${retryCount + 1}/${MAX_RETRIES})...`);
            await delay(RETRY_DELAY * (retryCount + 1)); // 递增延迟
            return generateContentWithOpenRouter(prompt, config, model, retryCount + 1);
        }

        // 所有重试都失败了
        throw new Error(`OpenRouter API failed after ${MAX_RETRIES} retries: ${errorMessage}`);
    }
}

/**
 * 流式生成内容（OpenRouter 支持 SSE）
 */
export async function generateContentStreamWithOpenRouter(
    prompt: string,
    config: GenerationConfig = {},
    model: string = DEFAULT_MODEL,
    onChunk?: (chunk: string) => void
): Promise<string> {
    if (!OPENROUTER_API_KEY) {
        throw new Error('OpenRouter API key not configured');
    }

    const startTime = Date.now();
    let fullContent = '';

    try {
        const response = await axios.post(
            `${OPENROUTER_BASE_URL}/chat/completions`,
            {
                model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: config.maxOutputTokens || 8192,
                temperature: config.temperature || 0.2,
                top_p: config.topP || 0.95,
                stream: true
            },
            {
                headers: {
                    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'https://app.raadstest.com',
                    'X-Title': 'RAADS Autism Test'
                },
                responseType: 'stream',
                timeout: 120000 // 2分钟超时
            }
        );

        return new Promise((resolve, reject) => {
            response.data.on('data', (chunk: Buffer) => {
                const lines = chunk.toString().split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            const responseTime = Date.now() - startTime;
                            
                            // 记录成功的流式API调用
                            notifyFeishu.paidApiLog({
                                provider: 'openrouter',
                                model,
                                prompt,
                                response: fullContent,
                                success: true,
                                responseTime
                            });
                            
                            resolve(fullContent);
                            return;
                        }
                        
                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices?.[0]?.delta?.content;
                            if (content) {
                                fullContent += content;
                                onChunk?.(content);
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            });

            response.data.on('error', (error: Error) => {
                const responseTime = Date.now() - startTime;
                
                notifyFeishu.paidApiLog({
                    provider: 'openrouter',
                    model,
                    prompt,
                    success: false,
                    responseTime,
                    error: error.message
                });
                
                reject(error);
            });
        });

    } catch (error) {
        const responseTime = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : String(error);

        await notifyFeishu.paidApiLog({
            provider: 'openrouter',
            model,
            prompt,
            success: false,
            responseTime,
            error: errorMessage
        });

        throw new Error(`OpenRouter streaming API failed: ${errorMessage}`);
    }
}

/**
 * 检查 OpenRouter API 是否可用
 */
export async function checkOpenRouterHealth(): Promise<boolean> {
    if (!OPENROUTER_API_KEY) {
        return false;
    }

    try {
        const response = await axios.get(`${OPENROUTER_BASE_URL}/models`, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
            },
            timeout: 10000
        });
        
        return response.status === 200;
    } catch (error) {
        console.error('OpenRouter health check failed:', error);
        return false;
    }
}
