import { randomBytes } from 'crypto';

// 生成推荐码
export function generateReferralCode(): string {
    return randomBytes(6).toString('hex').toUpperCase();
}

// 推荐信息接口
export interface ReferralInfo {
    referralCode: string;
    referredById?: string;
    referralClicks: number;
    referralCompletions: number;
    referralClickTimestamps: string[];
    referralCompletionTimestamps: string[];
}

// 从 metadata 中提取推荐信息
export function extractReferralInfo(metadata: any): ReferralInfo {
    const defaultInfo: ReferralInfo = {
        referralCode: generateReferralCode(),
        referralClicks: 0,
        referralCompletions: 0,
        referralClickTimestamps: [],
        referralCompletionTimestamps: []
    };

    if (!metadata || !metadata.referral) {
        return defaultInfo;
    }

    return {
        ...defaultInfo,
        ...metadata.referral
    };
}

// 更新推荐信息到 metadata
export function updateReferralInfo(metadata: any, referralInfo: Partial<ReferralInfo>): any {
    return {
        ...metadata,
        referral: {
            ...extractReferralInfo(metadata),
            ...referralInfo
        }
    };
}

// Cookie/localStorage 键名
export const REFERRAL_STORAGE_KEY = 'raads_referral_code';
export const REFERRAL_CLICK_STORAGE_KEY = 'raads_referral_click_time';

// 设置推荐码到存储
export function setReferralToStorage(referralCode: string): void {
    if (typeof window === 'undefined') return;

    console.log('设置推荐码到存储:', referralCode);

    try {
        // 使用 localStorage 存储推荐码
        localStorage.setItem(REFERRAL_STORAGE_KEY, referralCode);
        localStorage.setItem(REFERRAL_CLICK_STORAGE_KEY, new Date().toISOString());

        // 同时设置 cookie 作为备份（7天过期）
        const expires = new Date();
        expires.setDate(expires.getDate() + 7);
        document.cookie = `${REFERRAL_STORAGE_KEY}=${referralCode}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;

        console.log('✅ 推荐码存储成功');
    } catch (error) {
        console.error('❌ 推荐码存储失败:', error);
    }
}

// 从存储获取推荐码
export function getReferralFromStorage(): { referralCode: string | null; clickTime: string | null } {
    if (typeof window === 'undefined') {
        return { referralCode: null, clickTime: null };
    }

    try {
        // 优先从 localStorage 获取
        let referralCode = localStorage.getItem(REFERRAL_STORAGE_KEY);
        let clickTime = localStorage.getItem(REFERRAL_CLICK_STORAGE_KEY);

        console.log('从 localStorage 获取:', { referralCode, clickTime });

        // 如果 localStorage 没有，尝试从 cookie 获取
        if (!referralCode) {
            const cookies = document.cookie.split(';');
            for (const cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === REFERRAL_STORAGE_KEY) {
                    referralCode = value;
                    console.log('从 cookie 获取推荐码:', referralCode);
                    break;
                }
            }
        }

        return { referralCode, clickTime };
    } catch (error) {
        console.error('❌ 获取推荐码失败:', error);
        return { referralCode: null, clickTime: null };
    }
}

// 清除推荐码存储
export function clearReferralFromStorage(): void {
    if (typeof window === 'undefined') return;

    try {
        localStorage.removeItem(REFERRAL_STORAGE_KEY);
        localStorage.removeItem(REFERRAL_CLICK_STORAGE_KEY);
        document.cookie = `${REFERRAL_STORAGE_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        console.log('推荐码存储已清除');
    } catch (error) {
        console.error('清除推荐码存储失败:', error);
    }
}

// 生成分享链接
export function generateShareUrl(testId: string, referralCode: string, locale: string, testType: 'autism' | 'adhd'): string {
    if (typeof window === 'undefined') return '';

    const baseUrl = window.location.origin;
    const localePath = locale === 'en' ? '' : `/${locale}`;
    return `${baseUrl}${localePath}/${testType}?ref=${referralCode}`;
}

// 验证推荐码格式
export function isValidReferralCode(code: string): boolean {
    const isValid = /^[A-F0-9]{12}$/.test(code);
    console.log('推荐码验证:', { code, isValid, length: code.length, pattern: /^[A-F0-9]{12}$/ });
    return isValid;
} 