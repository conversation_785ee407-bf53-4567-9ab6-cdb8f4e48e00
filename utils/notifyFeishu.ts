import axios from 'axios';

const FEISHU_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL;
const FEISHU_KEY_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL;
const FEISHU_PAID_API_LOG_WEBHOOK = process.env.FEISHU_PAID_API_LOG_WEBHOOK;
const FEISHU_AI_GEN_ERROR_WEBHOOK = process.env.FEISHU_AI_GEN_ERROR_WEBHOOK;

async function sendFeishuNotification(webhookUrl: string | undefined, message: string) {
    // 如果未配置 webhook URL，静默返回
    if (!webhookUrl) {
        return;
    }

    try {
        await axios.post(webhookUrl, {
            msg_type: 'text',
            content: { text: message },
        });
    } catch (error) {
        // 在开发环境下才显示错误信息
        if (process.env.NODE_ENV === 'development') {
            console.error('飞书通知发送失败:', error);
        }
    }
}

// 付费API调用日志通知
export async function notifyPaidApiLog(data: {
    provider: 'vertex-ai' | 'openrouter';
    model: string;
    prompt: string;
    response?: string;
    success: boolean;
    responseTime: number;
    error?: string;
    tokenUsage?: {
        promptTokens?: number;
        completionTokens?: number;
        totalTokens?: number;
    };
    cost?: number;
}) {
    const timestamp = new Date().toISOString();
    const message = `
📊 付费API调用日志
时间: ${timestamp}
提供商: ${data.provider}
模型: ${data.model}
状态: ${data.success ? '✅ 成功' : '❌ 失败'}
响应时间: ${data.responseTime}ms
${data.tokenUsage ? `Token使用: ${data.tokenUsage.promptTokens || 0} + ${data.tokenUsage.completionTokens || 0} = ${data.tokenUsage.totalTokens || 0}` : ''}
${data.cost ? `预估成本: $${data.cost.toFixed(6)}` : ''}
提示词: ${data.prompt.substring(0, 200)}${data.prompt.length > 200 ? '...' : ''}
${data.response ? `响应: ${data.response.substring(0, 200)}${data.response.length > 200 ? '...' : ''}` : ''}
${data.error ? `错误: ${data.error}` : ''}
    `.trim();

    await sendFeishuNotification(FEISHU_PAID_API_LOG_WEBHOOK, message);
}

// AI生成错误通知
export async function notifyAiGenError(data: {
    provider: 'vertex-ai' | 'openrouter';
    model: string;
    error: string;
    prompt: string;
    endpoint: string;
    retryAttempt?: number;
    fallbackUsed?: boolean;
}) {
    const timestamp = new Date().toISOString();
    const message = `
🚨 AI内容生成失败告警
时间: ${timestamp}
端点: ${data.endpoint}
提供商: ${data.provider}
模型: ${data.model}
${data.retryAttempt ? `重试次数: ${data.retryAttempt}` : ''}
${data.fallbackUsed ? '✅ 已使用降级方案' : '❌ 未使用降级方案'}
错误信息: ${data.error}
提示词: ${data.prompt.substring(0, 200)}${data.prompt.length > 200 ? '...' : ''}
    `.trim();

    await sendFeishuNotification(FEISHU_AI_GEN_ERROR_WEBHOOK, message);
}

export const notifyFeishu = {
    notify: (message: string) => sendFeishuNotification(FEISHU_WEBHOOK_URL, message),
    keyNotify: (message: string) => sendFeishuNotification(FEISHU_KEY_WEBHOOK_URL, message),
    paidApiLog: notifyPaidApiLog,
    aiGenError: notifyAiGenError,
};