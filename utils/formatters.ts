/**
 * 格式化日期为本地化字符串
 * @param date 要格式化的日期
 * @param locale 区域设置
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date, locale: string) {
  return date.toLocaleDateString(locale === 'en' ? 'en-US' : locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * 获取相对时间字符串（例如 "2 days ago"）
 * @param date 要比较的日期
 * @param locale 区域设置
 * @returns 相对时间字符串
 */
export function getRelativeTimeString(date: Date, locale: string): string {
  const formatter = new Intl.RelativeTimeFormat(locale, {
    numeric: 'auto',
  });

  const now = new Date();
  const diff = date.getTime() - now.getTime();
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);
  
  if (years !== 0) {
    return formatter.format(years, 'year');
  }
  if (months !== 0) {
    return formatter.format(months, 'month');
  }
  if (weeks !== 0) {
    return formatter.format(weeks, 'week');
  }
  if (days !== 0) {
    return formatter.format(days, 'day');
  }
  if (hours !== 0) {
    return formatter.format(hours, 'hour');
  }
  if (minutes !== 0) {
    return formatter.format(minutes, 'minute');
  }
  
  return formatter.format(seconds, 'second');
} 