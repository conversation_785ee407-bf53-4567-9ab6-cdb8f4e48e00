import { prisma } from '@/utils/prisma';

export interface UnboundTest {
  id: string;
  testType: string;
  email: string;
  createdAt: Date;
  scores?: any;
  metadata?: any;
}

export interface BindingResult {
  success: boolean;
  boundCount: number;
  errors?: string[];
}

export class TestBindingService {
  /**
   * 检查用户邮箱下是否有未绑定的测试（不执行绑定）
   */
  static async checkUnboundTests(userEmail: string): Promise<UnboundTest[]> {
    if (!userEmail) return [];

    const unboundTests = await prisma.testResult.findMany({
      where: {
        email: userEmail,
        isUserBound: false,
      },
      select: {
        id: true,
        testType: true,
        email: true,
        createdAt: true,
        scores: true,
        metadata: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return unboundTests;
  }

  /**
   * 获取用户已绑定的测试
   */
  static async getBoundTests(userId: string) {
    return await prisma.testResult.findMany({
      where: {
        userId: userId,
        isUserBound: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * 绑定指定的测试到用户账户
   */
  static async bindTests(userId: string, testIds: string[]): Promise<BindingResult> {
    if (!userId || !testIds.length) {
      return { success: false, boundCount: 0, errors: ['Invalid parameters'] };
    }

    try {
      // 验证这些测试确实是未绑定的
      const testsToVerify = await prisma.testResult.findMany({
        where: {
          id: { in: testIds },
          isUserBound: false,
        },
      });

      if (testsToVerify.length !== testIds.length) {
        return {
          success: false,
          boundCount: 0,
          errors: ['Some tests are already bound or do not exist'],
        };
      }

      // 执行绑定
      const result = await prisma.testResult.updateMany({
        where: {
          id: { in: testIds },
          isUserBound: false,
        },
        data: {
          userId: userId,
          isUserBound: true,
        },
      });

      return {
        success: true,
        boundCount: result.count,
      };
    } catch (error) {
      console.error('Error binding tests:', error);
      return {
        success: false,
        boundCount: 0,
        errors: ['Database error occurred'],
      };
    }
  }

  /**
   * 绑定单个测试
   */
  static async bindSingleTest(
    userId: string,
    testId: string,
    token?: string
  ): Promise<BindingResult> {
    try {
      // 构建查询条件
      const whereCondition: any = {
        id: testId,
        isUserBound: false,
      };

      // 如果提供了token，验证token
      if (token) {
        whereCondition.accessToken = token;
      }

      const testResult = await prisma.testResult.findFirst({
        where: whereCondition,
      });

      if (!testResult) {
        return {
          success: false,
          boundCount: 0,
          errors: ['Test not found or already bound'],
        };
      }

      // 执行绑定
      await prisma.testResult.update({
        where: { id: testId },
        data: {
          userId: userId,
          isUserBound: true,
        },
      });

      return {
        success: true,
        boundCount: 1,
      };
    } catch (error) {
      console.error('Error binding single test:', error);
      return {
        success: false,
        boundCount: 0,
        errors: ['Database error occurred'],
      };
    }
  }

  /**
   * 获取测试的友好显示名称
   */
  static getTestDisplayName(testType: string): string {
    const testNames: Record<string, string> = {
      raadsrTest: 'RAADS-R Test',
      aq10Test: 'AQ-10 Test',
      adult: 'ADHD Adult Test',
      child: 'ADHD Child Test',
      general: 'ADHD General Test',
    };

    return testNames[testType] || testType;
  }

  /**
   * 获取测试结果的简要描述
   */
  static getTestScoreSummary(testType: string, scores: any): string {
    try {
      if (testType === 'aq10Test') {
        const score = scores?.score || scores;
        if (score && typeof score.score === 'number') {
          return `Score: ${score.score}/${score.max}`;
        }
      } else if (testType === 'raadsrTest') {
        if (scores?.total && typeof scores.total.score === 'number') {
          return `Total Score: ${scores.total.score}/${scores.total.max}`;
        }
      } else if (['adult', 'child', 'general'].includes(testType)) {
        if (scores?.total && typeof scores.total.score === 'number') {
          return `Total Score: ${scores.total.score}/${scores.total.max}`;
        }
      }
      return 'Completed';
    } catch {
      return 'Completed';
    }
  }
} 