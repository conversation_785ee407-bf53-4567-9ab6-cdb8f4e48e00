import { prisma } from './prisma';
import { generateContentWithOpenRouter, checkOpenRouterHealth } from './openrouterService';
import { VertexAI } from '@google-cloud/vertexai';
import * as fs from 'fs';

// Constants
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT_ID || 'projectatm-89b3a';
const LOCATION = process.env.VERTEX_LOCATION || 'us-central1';
const MODEL_NAME = process.env.VERTEX_MODEL || 'gemini-2.0-flash';

// Initialize Vertex AI client
const initializeVertexAI = () => {
  try {
    const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    if (credentialsPath) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialsPath;
    }
    
    return new VertexAI({
      project: PROJECT_ID,
      location: LOCATION,
    });
  } catch (error) {
    console.error('[reportPreGeneration] Failed to initialize Vertex AI:', error);
    return null;
  }
};

// Load prompt template
const loadPromptTemplate = (testType: string, locale: string): string => {
  try {
    const promptPath = `prompts/${testType}_${locale}.txt`;
    const fullPath = require('path').join(process.cwd(), promptPath);
    
    if (fs.existsSync(fullPath)) {
      return fs.readFileSync(fullPath, 'utf-8');
    } else {
      console.warn(`[reportPreGeneration] Prompt file not found: ${promptPath}`);
      return '';
    }
  } catch (error) {
    console.error(`[reportPreGeneration] Error loading prompt template:`, error);
    return '';
  }
};

// Generate report content using AI
const generateReportContent = async (
  testType: string,
  answers: Record<number, number>,
  scores: any,
  locale: string
): Promise<string | null> => {
  try {
    const promptTemplate = loadPromptTemplate(testType, locale);
    if (!promptTemplate) {
      throw new Error('Prompt template not found');
    }

    // Prepare the prompt with test data
    const prompt = promptTemplate
      .replace(/\{answers\}/g, JSON.stringify(answers))
      .replace(/\{scores\}/g, JSON.stringify(scores))
      .replace(/\{locale\}/g, locale);

    // Try OpenRouter first
    const isOpenRouterHealthy = await checkOpenRouterHealth();
    if (isOpenRouterHealthy) {
      console.log('[reportPreGeneration] Using OpenRouter for report generation');
      const openRouterResult = await generateContentWithOpenRouter(prompt);
      if (openRouterResult) {
        return openRouterResult;
      }
    }

    // Fallback to Vertex AI
    console.log('[reportPreGeneration] Using Vertex AI for report generation');
    const vertexAI = initializeVertexAI();
    if (!vertexAI) {
      throw new Error('Failed to initialize Vertex AI');
    }

    const model = vertexAI.getGenerativeModel({
      model: MODEL_NAME,
      generationConfig: {
        maxOutputTokens: 8192,
        temperature: 0.7,
        topP: 0.8,
        topK: 40,
      },
    });

    const result = await model.generateContent(prompt);
    const response = result.response;
    return response.candidates?.[0]?.content?.parts?.[0]?.text || null;

  } catch (error) {
    console.error('[reportPreGeneration] Error generating report content:', error);
    return null;
  }
};

// Pre-generate and store ADHD report
export const preGenerateAdhdReport = async (
  testId: string,
  testType: 'adult' | 'child',
  answers: Record<number, number>,
  scores: any,
  locale: string
): Promise<boolean> => {
  try {
    console.log(`[reportPreGeneration] Pre-generating ADHD ${testType} report for test ${testId}`);

    // Check if report already exists
    const existingTest = await prisma.testResult.findUnique({
      where: { id: testId }
    });

    if (existingTest?.reports && !existingTest.isReportLocked) {
      console.log('[reportPreGeneration] Report already exists and is unlocked, skipping generation');
      return true;
    }

    // Generate report content
    const reportContent = await generateReportContent(
      `adhd_${testType}`,
      answers,
      scores,
      locale
    );

    if (!reportContent) {
      console.error('[reportPreGeneration] Failed to generate report content');
      return false;
    }

    // Store the report in database
    const currentReports = existingTest?.reports as any || {};
    currentReports[locale] = reportContent;

    await prisma.testResult.update({
      where: { id: testId },
      data: {
        reports: currentReports,
        isReportLocked: true, // Keep it locked initially
        updatedAt: new Date()
      }
    });

    console.log('[reportPreGeneration] ADHD report pre-generated and stored successfully');
    return true;

  } catch (error) {
    console.error('[reportPreGeneration] Error pre-generating ADHD report:', error);
    return false;
  }
};

// Pre-generate and store Autism report
export const preGenerateAutismReport = async (
  testId: string,
  testType: 'raadsr' | 'aq10',
  answers: Record<number, number>,
  scores: any,
  locale: string
): Promise<boolean> => {
  try {
    console.log(`[reportPreGeneration] Pre-generating Autism ${testType} report for test ${testId}`);

    // Check if report already exists
    const existingTest = await prisma.testResult.findUnique({
      where: { id: testId }
    });

    if (existingTest?.reports && !existingTest.isReportLocked) {
      console.log('[reportPreGeneration] Report already exists and is unlocked, skipping generation');
      return true;
    }

    // Generate report content
    const reportContent = await generateReportContent(
      testType,
      answers,
      scores,
      locale
    );

    if (!reportContent) {
      console.error('[reportPreGeneration] Failed to generate report content');
      return false;
    }

    // Store the report in database
    await prisma.autismReport.upsert({
      where: { testId },
      update: {
        report: reportContent,
        isLocked: true, // Keep it locked initially
        updatedAt: new Date()
      },
      create: {
        testId,
        report: reportContent,
        isLocked: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('[reportPreGeneration] Autism report pre-generated and stored successfully');
    return true;

  } catch (error) {
    console.error('[reportPreGeneration] Error pre-generating Autism report:', error);
    return false;
  }
};

// Background job to pre-generate reports for recent test results
export const preGenerateReportsForRecentTests = async (): Promise<void> => {
  try {
    console.log('[reportPreGeneration] Starting background pre-generation job');

    // Get recent test results without reports (last 24 hours)
    const recentTests = await prisma.testResult.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      include: {
        adhdReport: true,
        autismReport: true
      }
    });

    for (const test of recentTests) {
      try {
        const answers = test.answers as Record<number, number>;
        const scores = test.scores;
        
        if (test.testType.includes('adhd')) {
          // Pre-generate ADHD report if not exists
          if (!test.adhdReport?.report) {
            const testType = test.testType.includes('adult') ? 'adult' : 'child';
            await preGenerateAdhdReport(test.id, testType, answers, scores, test.locale || 'zh');
          }
        } else {
          // Pre-generate Autism report if not exists
          if (!test.autismReport?.report) {
            const testType = test.testType === 'raadsrTest' ? 'raadsr' : 'aq10';
            await preGenerateAutismReport(test.id, testType, answers, scores, test.locale || 'zh');
          }
        }
      } catch (error) {
        console.error(`[reportPreGeneration] Error processing test ${test.id}:`, error);
        continue; // Continue with next test
      }
    }

    console.log('[reportPreGeneration] Background pre-generation job completed');
  } catch (error) {
    console.error('[reportPreGeneration] Error in background pre-generation job:', error);
  }
};
