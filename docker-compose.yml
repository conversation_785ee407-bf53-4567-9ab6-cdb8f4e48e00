version: '3.8'

services:
  # 应用服务
  raads_plus:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NODE_ENV: development
    container_name: raads_plus
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      NODE_ENV: development
    env_file:
      - .env
    depends_on:
      - raads_plus_db
    restart: unless-stopped

  # 数据库服务
  raads_plus_db:
    image: postgres:alpine
    ports:
      - "5433:5432"
    env_file:
      - .env
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
    container_name: raads_plus_db
    restart: unless-stopped

volumes:
  postgres_data:
