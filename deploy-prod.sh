#!/bin/bash

# 一键生产部署脚本
# 172.245.64.203 -> /root/prod/raads_plus

set -e

# 设置PATH环境变量
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"

SERVER="root@172.245.64.203"
PATH="/root/prod/raads_plus"

echo "🚀 部署到生产服务器..."

# 检查文件
[ ! -f "docker-compose.prod.yml" ] && echo "❌ 缺少 docker-compose.prod.yml" && exit 1
[ ! -f ".env.production" ] && echo "❌ 缺少 .env.production" && exit 1
[ ! -f "google_application_credentials.json" ] && echo "❌ 缺少 google_application_credentials.json" && exit 1
[ ! -f "deploy.sh" ] && echo "❌ 缺少 deploy.sh" && exit 1

echo "✅ 文件检查通过"

# 设置权限
/bin/chmod +x deploy.sh

# 拷贝文件
echo "📤 拷贝文件..."
/usr/bin/ssh $SERVER "mkdir -p $PATH"
/usr/bin/scp -q docker-compose.prod.yml .env.production google_application_credentials.json deploy.sh $SERVER:$PATH/

# 设置远程权限
/usr/bin/ssh $SERVER "cd $PATH && /bin/chmod +x deploy.sh && /bin/chmod 600 .env.production google_application_credentials.json"

# 执行部署
echo "🔄 执行远程部署..."
/usr/bin/ssh $SERVER "cd $PATH && ./deploy.sh"

echo "✅ 部署完成！"

# 显示状态
echo "📊 服务状态:"
/usr/bin/ssh $SERVER "cd $PATH && docker-compose -f docker-compose.prod.yml ps" 