import { createNavigation } from 'next-intl/navigation';
import { defineRouting } from 'next-intl/routing';

export const locales = ['en', 'zh'] as const;

export const routing = defineRouting({
    locales,
    defaultLocale: 'en',
    localeDetection: false,
    localePrefix: 'as-needed',
    pathnames: {
        '/': '/',
        '/adhd': '/adhd',
        '/i/adhd': '/i/adhd',
        '/autism': '/autism',
        '/i/autism': '/i/autism',
        '/i/autism/raadsr': '/i/autism/raadsr',
        '/i/autism/aq10': '/i/autism/aq10',
        '/i/autism/result/:id': '/i/autism/result/:id',
        '/autism/raadsr': '/autism/raadsr',
        '/autism/aq10': '/autism/aq10',
        '/autism/result/:id': '/autism/result/:id',
        '/auth/signin': '/auth/signin',
        '/auth/error': '/auth/error',
        '/report/:id': '/report/:id',
        '/reports': '/reports',
        '/profile': '/profile',
        '/unauthorized': '/unauthorized'
    }
});

export type Pathnames = keyof typeof routing.pathnames;
export type Locale = (typeof routing.locales)[number];

export const { Link, getPathname, redirect, usePathname, useRouter } =
    createNavigation(routing);

export const fullLanguageNames = {
    en: 'English',
    zh: '简体中文',
} as const;