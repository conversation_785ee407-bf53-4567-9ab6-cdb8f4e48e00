import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
    // This typically corresponds to the `[locale]` segment
    let locale = await requestLocale;

    // Ensure that the incoming `locale` is valid
    if (!locale || !routing.locales.includes(locale as any)) {
        locale = routing.defaultLocale;
    }

    // Load the main language file from common directory
    const mainMessages = (
        await (locale === 'en'
            ? // When using Turbopack, this will enable HMR for `en`
            import('../locales/common/en.json')
            : import(`../locales/common/${locale}.json`))
    ).default;

    // Load page-specific language files
    const adhdMessages = (
        await import(`../locales/pages/adhd/${locale}.json`)
    ).default;

    // Load autism page-specific language files
    const autismMessages = (
        await import(`../locales/pages/autism/${locale}.json`)
    ).default;

    // Load home page-specific language files
    const homeMessages = (
        await import(`../locales/pages/home/<USER>
    ).default;

    // Load auth page-specific language files
    const authMessages = (
        await import(`../locales/pages/auth/${locale}.json`)
    ).default;

    // Load profile page-specific language files
    const profileMessages = (
        await import(`../locales/pages/profile/${locale}.json`)
    ).default;

    // Load reports page-specific language files
    const reportsMessages = (
        await import(`../locales/pages/reports/${locale}.json`)
    ).default;

    // Load report page-specific language files
    const reportMessages = (
        await import(`../locales/pages/report/${locale}.json`)
    ).default;

    // Load unauthorized page-specific language files
    const unauthorizedMessages = (
        await import(`../locales/pages/unauthorized/${locale}.json`)
    ).default;

    // Load components language files
    const componentsMessages = (
        await import(`../locales/components/${locale}.json`)
    ).default;

    // Merge the messages
    return {
        locale,
        messages: {
            ...mainMessages,
            ...componentsMessages,
            pages: {
                ...mainMessages.pages,
                adhd: adhdMessages,
                autism: autismMessages,
                home: homeMessages,
                auth: authMessages,
                profile: profileMessages,
                reports: reportsMessages,
                report: reportMessages,
                unauthorized: unauthorizedMessages
            }
        }
    };
});